# CLAUDE.md

Central context and working guide for this codebase. Use this document as the single source of truth for architecture, workflows, feature guides, and as an index of docs under `docs/`.

## Project Overview

This is a **Document Reader MVP** Flutter application for Android that allows users to view various document formats (PDF, DOCX, XLSX, PPTX) on their mobile devices. The app focuses on file discovery and viewing capabilities with two methods: automatic scanning with storage permissions or manual file selection via system picker.

## Architecture

This project follows **Clean Architecture** with a **feature-first structure**:

```
lib/
├── core/                    # Shared utilities and infrastructure
│   ├── constants/          # File types and app constants
│   ├── error/              # Failure classes
│   ├── navigation/         # GoRouter configuration
│   ├── providers/          # Core Riverpod providers
│   ├── theme/              # Material Design 3 theme
│   ├── utils/              # Formatters and utilities
│   └── widgets/            # Reusable UI components
└── features/               # Feature modules
    ├── permissions/        # Storage permission handling
    ├── file_discovery/     # Document scanning and listing
    └── document_viewer/    # Document rendering

Additional feature modules present in this repo include:
- `file_actions/` – Action sheets and buttons around a file
- `file_conversion/` – Scan camera and conversions to/from PDF
- `file_info/` – File metadata and details
- `pdf_page_management/` – Split, merge, extract, reorder PDF pages
- `pdf_security/` – Password protection and validation for PDFs
- `permissions/` – Storage permission handling
- `sharing/` – Share file integrations
```

### Feature Structure

Each feature follows Clean Architecture layers:
- **presentation/** - UI (screens, widgets, notifiers, providers)
- **domain/** - Business logic (entities, repositories interfaces, use cases)
- **data/** - Implementation (data sources, repository implementations, models)

### Key Architecture Patterns

1. **State Management**: Riverpod with code generation (`riverpod_annotation`, `riverpod_generator`)
   - Notifiers handle feature state
   - Providers expose dependencies and state
   - Use `@riverpod` annotation for automatic provider generation

2. **Error Handling**: Functional approach using `dartz` Either type
   - `Left<Failure>` for errors
   - `Right<T>` for success values
   - Custom Failure classes in `core/error/failures.dart`

3. **Data Models**: Freezed for immutable models with code generation
   - Use `@freezed` annotation
   - Always declare as `abstract class`
   - Models in `data/models/`, entities in `domain/entities/`

4. **Navigation**: GoRouter for declarative routing
   - Routes defined in `core/navigation/app_router.dart`
   - Document viewers use path parameters and query params for file paths/names

## Common Development Commands

### Build & Run
```bash
# Run the app
flutter run

# Build for Android
flutter build apk
flutter build appbundle
```

### Code Generation
```bash
# Generate Riverpod providers, Freezed models, etc.
dart run build_runner build --delete-conflicting-outputs

# Watch mode (auto-regenerate on changes)
dart run build_runner watch --delete-conflicting-outputs
```

### Code Quality
```bash
# Analyze code
flutter analyze

# Format code
dart format .

# Run tests
flutter test

# Run specific test file
flutter test test/path/to/test_file.dart
```

### Dependencies
```bash
# Add a package
flutter pub add package_name

# Add a dev dependency
flutter pub add dev:package_name

# Get dependencies
flutter pub get
```

## Document Viewer Integration

### Supported Formats & Packages
- **PDF**: `syncfusion_flutter_pdfviewer` (v31.1.22) - compatible with intl 0.20.2
- **DOCX/XLSX/PPTX**: `microsoft_viewer` (v0.0.7)
- **Note**: Avoid `excel` package - conflicts with `microsoft_viewer` due to archive dependency

### Adding New Document Types
1. Create viewer screen in `features/document_viewer/presentation/screens/`
2. Add route in `core/navigation/app_router.dart`
3. Update file type detection in `core/constants/file_types.dart`
4. Add appropriate viewer package if needed

## Important Notes

### Code Generation Issues
- **Freezed classes must be `abstract class`** - this is required, not optional
- After running build_runner, check generated `.freezed.dart` files for formatting
- Known issue: Freezed 3.0.0 may generate single-line getters; manually split to multi-line if needed
- This is NOT fixed by dependency updates - manual intervention may be required

### Constructor Syntax
- Prefer modern constructor syntax: `ClassName(super.field)` over `ClassName(Type field) : super(field)`
- See `core/error/failures.dart` for examples

### Import Management
- Remove unused imports to keep code clean
- Notifiers should NOT import their own use case files (use providers instead)
- Providers should NOT import notifiers (avoid circular dependencies)

### Permissions
- Storage permission required for automatic file discovery
- App remains functional without permission via manual file picker
- Permission logic in `features/permissions/`

## Project Status

Current implementation:
- ✅ Clean Architecture setup with feature-first structure
- ✅ Riverpod state management with code generation
- ✅ Permission handling (storage access)
- ✅ File discovery (scanning + manual picker)
- ✅ Document viewers (PDF, DOCX, XLSX, PPTX)
- ✅ GoRouter navigation
- ✅ Material Design 3 theming

Out of scope for MVP:
- File manipulation (delete, rename, share)
- Folders (converted, translated)
- AI features (translate, summarize, merge, split)
- File conversion tools
- Document editing
- Advertisements

Note: While code for certain features exists (e.g., conversion and page management), scope and availability may vary by milestone. Refer to the feature guides below for current status.

## Implementation Reporting & References

- Master Implementation Plan(s): see `docs/impl_plan/` (e.g., `251002.claude-plan-mvp-01-1.md`, per README links)
- Progress reporting: `docs/impl_plan/IMPLEMENTATION_PROGRESS_REPORT.md`
- Session summaries: `docs/impl_plan/IMPLEMENTATION_SESSION_SUMMARY.md`
- Dependency conflicts and resolutions: `docs/impl_plan/DEPENDENCY_CONFLICT_RESOLUTION.md`

Agents should keep these documents up to date during implementation (after each session/milestone), and reference them in pull requests and issues.

## Feature Guides

Feature-level documentation lives alongside code. Start here to understand flows, files, and responsibilities per feature:
- lib/features/discover/README.md
- lib/features/document_viewer/README.md
- lib/features/file_actions/README.md
- lib/features/file_conversion/README.md
- lib/features/file_discovery/README.md
- lib/features/file_info/README.md
- lib/features/pdf_page_management/README.md
- lib/features/pdf_security/README.md
- lib/features/permissions/README.md
- lib/features/sharing/README.md

## Docs Index

Curated entry points into the `docs/` folder for plans, performance, and agent workflows. See files for full details.

- docs/impl_plan/251002.claude-plan-mvp-01-1.md
- docs/impl_plan/IMPLEMENTATION_PROGRESS_REPORT.md
- docs/impl_plan/IMPLEMENTATION_SESSION_SUMMARY.md
- docs/impl_plan/DEPENDENCY_CONFLICT_RESOLUTION.md
- docs/impl_plan/prd-001/README.md
- docs/impl_plan/prd-001/QA_CHECKLIST.md
- docs/impl_plan/prd-002/IMPLEMENTATION_COMPLETE.md
- docs/impl_plan/prd-002/UI_INTEGRATION_GUIDE.md
- docs/impl_plan/prd-002/IMPLEMENTATION_PROGRESS_REPORT.md
- docs/impl_plan/prd-002/IMPLEMENTATION_SESSION_SUMMARY.md
- docs/impl_plan/prd-003/IMPLEMENTATION_PROGRESS_REPORT.md
- docs/performance/STARTUP_OPTIMIZATION_2025.md
- docs/performance/EXPLORE_NAVIGATION_ANALYSIS.md
- docs/prd-003-ui-integration.md
- docs/agents/dev.md
- docs/agents/plan.md
- docs/agents/en-plan.md
- docs/agents/en-plan-tdd.md
- docs/agents/enhance-dev.md

## Environment Setup (Terminal-Only Agents)

This section is for agents working in fresh, terminal-only environments (no VS Code or Android Studio). If Flutter and Dart are not installed yet, follow these steps.

References for project context and guidelines:
- Project architecture and patterns: this file (CLAUDE.md)
- Troubleshooting and repo overview: README.md
- Developer guidelines (Feature-First Clean Architecture, Riverpod DI): docs/agents/dev.md

### 1) Prerequisites
- Git, unzip, curl/wget
- Java JDK 17 (required for recent Android build tools)
- Android SDK (commandline-tools only; Android Studio is NOT required)

Example (Ubuntu/Debian):
```bash
sudo apt-get update
sudo apt-get install -y git unzip curl zip tar libglu1-mesa default-jdk
java -version  # Ensure Java 17; if not, install Temurin or OpenJDK 17
```

To install JDK 17 explicitly (Ubuntu):
```bash
sudo apt-get install -y openjdk-17-jdk
```

### 2) Android SDK (command-line only)
```bash
# Create SDK dir
mkdir -p "$HOME/Android/Sdk/cmdline-tools/latest"
cd "$HOME/Android/Sdk/cmdline-tools"

# Download cmdline-tools (update URL to latest if needed)
curl -L -o tools.zip \
  https://dl.google.com/android/repository/commandlinetools-linux-11076708_latest.zip
unzip tools.zip -d latest
rm tools.zip

# Environment variables (add to shell profile)
export ANDROID_SDK_ROOT="$HOME/Android/Sdk"
export ANDROID_HOME="$ANDROID_SDK_ROOT"
export PATH="$ANDROID_SDK_ROOT/platform-tools:$ANDROID_SDK_ROOT/emulator:$ANDROID_SDK_ROOT/cmdline-tools/latest/bin:$PATH"

# Install required packages
yes | sdkmanager --licenses
sdkmanager \
  "platform-tools" \
  "platforms;android-34" \
  "build-tools;34.0.0"
```

Notes:
- No emulator required for running tests; device/emulator only needed for `flutter run`.
- Agents do not need Android Studio.

### 3) Flutter SDK (includes Dart)
```bash
# Choose a directory for Flutter
cd "$HOME"
curl -L -o flutter.tar.xz https://storage.googleapis.com/flutter_infra_release/releases/stable/linux/flutter_linux_3.22.2-stable.tar.xz
tar xf flutter.tar.xz
rm flutter.tar.xz

# Add Flutter to PATH (add to shell profile)
export PATH="$HOME/flutter/bin:$PATH"

# Doctor and licenses
flutter doctor
flutter doctor --android-licenses
```

Optional: manage versions with FVM
```bash
dart pub global activate fvm
export PATH="$HOME/.pub-cache/bin:$PATH"
fvm install stable
fvm use stable
```

### 4) Project Setup
```bash
git clone <repo-url>
cd fs-fast-pdf-reader
flutter --version
flutter pub get

# Generate code (Riverpod, Freezed, JsonSerializable)
dart run build_runner build --delete-conflicting-outputs

# Quality & Tests
flutter analyze
dart format .
flutter test

# Run on a connected Android device (optional)
flutter run
```

Troubleshooting tips are in README.md. If build errors appear, re-run code generation, verify Android SDK env vars, accept licenses, and ensure Java 17.
