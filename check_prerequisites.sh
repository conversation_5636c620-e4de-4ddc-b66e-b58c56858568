#!/bin/bash

# Script to check if all prerequisites for Flutter project are installed
# This script checks for Git, Java JDK 17, Android SDK, Flutter, and Dart

echo "Checking prerequisites for fs-fast-pdf-reader project..."

# Check if Git is installed
if command -v git &> /dev/null; then
    echo "✅ Git is installed: $(git --version)"
else
    echo "❌ Git is not installed"
    GIT_INSTALLED=false
fi

# Check if unzip is installed
if command -v unzip &> /dev/null; then
    echo "✅ Unzip is installed"
else
    echo "❌ Unzip is not installed"
    UNZIP_INSTALLED=false
fi

# Check if curl is installed
if command -v curl &> /dev/null; then
    echo "✅ Curl is installed"
else
    echo "❌ Curl is not installed"
    CURL_INSTALLED=false
fi

# Check if Java JDK is installed (version 17 or 21 recommended)
if command -v java &> /dev/null; then
    JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
    if [[ "$JAVA_VERSION" == *"17."* ]] || [[ "$JAVA_VERSION" == *"21."* ]]; then
        echo "✅ Java JDK is installed (version $JAVA_VERSION) - Compatible"
    elif [[ "$JAVA_VERSION" == *"1.8"* ]]; then
        echo "⚠️  Java JDK is installed (version $JAVA_VERSION) - Version 8 is outdated, JDK 17+ recommended"
        JAVA_INSTALLED=false
    else
        echo "⚠️  Java JDK is installed (version $JAVA_VERSION) - JDK 17+ recommended for Android build tools"
        JAVA_INSTALLED=false
    fi
else
    echo "❌ Java JDK is not installed"
    JAVA_INSTALLED=false
fi

# Check if Android SDK is installed
if [ -n "$ANDROID_HOME" ] && [ -d "$ANDROID_HOME" ]; then
    echo "✅ Android SDK is installed at: $ANDROID_HOME"
    
    # Check if required Android packages are installed
    if [ -d "$ANDROID_HOME/platforms/android-34" ]; then
        echo "✅ Android platform android-34 is installed"
    else
        echo "❌ Android platform android-34 is not installed"
        ANDROID_PLATFORM_INSTALLED=false
    fi
    
    if [ -d "$ANDROID_HOME/build-tools/34.0.0" ]; then
        echo "✅ Android build-tools 34.0.0 is installed"
    else
        echo "❌ Android build-tools 34.0.0 is not installed"
        ANDROID_BUILD_TOOLS_INSTALLED=false
    fi
    
    if [ -d "$ANDROID_HOME/platform-tools" ]; then
        echo "✅ Android platform-tools is installed"
    else
        echo "❌ Android platform-tools is not installed"
        ANDROID_PLATFORM_TOOLS_INSTALLED=false
    fi
else
    echo "❌ Android SDK is not installed or ANDROID_HOME is not set"
    ANDROID_SDK_INSTALLED=false
fi

# Check if Flutter is installed
if command -v flutter &> /dev/null; then
    echo "✅ Flutter is installed: $(flutter --version | head -n1)"
    
    # Check if Flutter doctor passes
    echo "Running flutter doctor..."
    flutter doctor
else
    echo "❌ Flutter is not installed"
    FLUTTER_INSTALLED=false
fi

# Check if Dart is installed (comes with Flutter)
if command -v dart &> /dev/null; then
    echo "✅ Dart is installed: $(dart --version 2>&1)"
else
    echo "❌ Dart is not installed (usually comes with Flutter)"
    DART_INSTALLED=false
fi

# Check if Gradle is installed (Android projects have Gradle automatically)
if command -v gradle &> /dev/null; then
    echo "✅ Gradle is installed: $(gradle --version | head -n1)"
else
    echo "ℹ️  Gradle not found globally (this is normal - Android projects include Gradle wrapper)"
    # This is not necessarily a problem since Android projects have a Gradle wrapper
    GRADLE_INSTALLED=true  # Consider this as satisfied since Flutter handles it
fi

# Summary
echo ""
echo "=== Prerequisites Check Summary ==="
MISSING_PREREQUISITES=false

if [ "$GIT_INSTALLED" = false ]; then
    echo "❌ Git needs to be installed"
    MISSING_PREREQUISITES=true
fi

if [ "$UNZIP_INSTALLED" = false ]; then
    echo "❌ Unzip needs to be installed"
    MISSING_PREREQUISITES=true
fi

if [ "$CURL_INSTALLED" = false ]; then
    echo "❌ Curl needs to be installed"
    MISSING_PREREQUISITES=true
fi

if [ "$JAVA_INSTALLED" = false ]; then
    echo "❌ Java JDK 17 needs to be installed"
    MISSING_PREREQUISITES=true
fi

if [ "$ANDROID_SDK_INSTALLED" = false ]; then
    echo "❌ Android SDK needs to be installed"
    MISSING_PREREQUISITES=true
fi

if [ "$ANDROID_PLATFORM_INSTALLED" = false ]; then
    echo "❌ Android platform android-34 needs to be installed"
    MISSING_PREREQUISITES=true
fi

if [ "$ANDROID_BUILD_TOOLS_INSTALLED" = false ]; then
    echo "❌ Android build-tools 34.0.0 needs to be installed"
    MISSING_PREREQUISITES=true
fi

if [ "$ANDROID_PLATFORM_TOOLS_INSTALLED" = false ]; then
    echo "❌ Android platform-tools needs to be installed"
    MISSING_PREREQUISITES=true
fi

if [ "$FLUTTER_INSTALLED" = false ]; then
    echo "❌ Flutter needs to be installed"
    MISSING_PREREQUISITES=true
fi

if [ "$DART_INSTALLED" = false ]; then
    echo "❌ Dart needs to be installed"
    MISSING_PREREQUISITES=true
fi

if [ "$MISSING_PREREQUISITES" = false ]; then
    echo "✅ All prerequisites are installed and configured correctly!"
    exit 0
else
    echo "❌ Some prerequisites are missing. Please install them before proceeding."
    exit 1
fi