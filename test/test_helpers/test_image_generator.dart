import 'dart:io';
import 'package:image/image.dart' as img;

/// Helper utility for generating test images used in scanner tests
class TestImageGenerator {
  /// Generates a test image with solid color
  static img.Image generateTestImage({
    required int width,
    required int height,
    required img.Color color,
  }) {
    final image = img.Image(width: width, height: height);
    img.fill(image, color: color);
    return image;
  }

  /// Saves an image to the file system as JPEG
  static Future<String> saveTestImage({
    required img.Image image,
    required String outputPath,
  }) async {
    final file = File(outputPath);
    await file.parent.create(recursive: true);
    await file.writeAsBytes(img.encodeJpg(image));
    return outputPath;
  }

  /// Convenience method that generates and saves in one call
  static Future<String> generateTestImageFile({
    required String directory,
    required String filename,
    required int width,
    required int height,
    required img.Color color,
  }) async {
    final image = generateTestImage(width: width, height: height, color: color);
    final outputPath = '$directory/$filename';
    return saveTestImage(image: image, outputPath: outputPath);
  }

  /// Generates multiple test images with different colors
  static Future<List<String>> createTestImageSet({
    required String directory,
    required int count,
  }) async {
    final colors = [
      img.ColorRgb8(255, 0, 0), // Red
      img.ColorRgb8(0, 255, 0), // Green
      img.ColorRgb8(0, 0, 255), // Blue
      img.ColorRgb8(255, 255, 0), // Yellow
      img.ColorRgb8(255, 0, 255), // Magenta
      img.ColorRgb8(0, 255, 255), // Cyan
      img.ColorRgb8(128, 128, 128), // Gray
      img.ColorRgb8(255, 128, 0), // Orange
    ];

    final paths = <String>[];
    for (var i = 0; i < count; i++) {
      final color = colors[i % colors.length];
      final path = await generateTestImageFile(
        directory: directory,
        filename: 'test_image_$i.jpg',
        width: 800,
        height: 600,
        color: color,
      );
      paths.add(path);
    }
    return paths;
  }

  /// Deletes test images and directories
  static Future<void> cleanupTestImages(String directoryPath) async {
    final directory = Directory(directoryPath);
    if (await directory.exists()) {
      await directory.delete(recursive: true);
    }
  }
}
