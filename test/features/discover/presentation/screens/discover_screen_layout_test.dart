// test/features/discover/presentation/screens/discover_screen_layout_test.dart

import 'package:flutter/material.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:myapp/features/discover/domain/entities/tool_entry.dart';
import 'package:myapp/features/discover/presentation/notifiers/discover_tools_notifier.dart';
import 'package:myapp/features/discover/domain/usecases/get_discover_tools.dart';
import 'package:myapp/features/discover/presentation/screens/discover_screen.dart';

void main() {
  testWidgets('Discover grid renders without overflow for long text', (tester) async {
    final tools = List.generate(
      4,
      (i) => ToolEntry(
        id: 't$i',
        title: 'Very long tool title number $i that should wrap nicely without overflow',
        description:
            'An even longer description for tool $i that is intentionally verbose to stress layout and ensure ellipsis works as expected.',
        iconName: 'merge_type',
        enabled: i.isEven,
        route: '/noop',
      ),
    );

    await tester.binding.setSurfaceSize(const Size(360, 400));

    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          getDiscoverToolsProvider.overrideWithValue(_FakeGetDiscoverTools(tools)),
        ],
        child: const MaterialApp(home: DiscoverScreen()),
      ),
    );

    await tester.pumpAndSettle();

    // If an overflow occurs during layout, this will surface as an exception
    expect(tester.takeException(), isNull);

    // Sanity check: cards rendered
    expect(find.byType(Card), findsWidgets);
    expect(find.textContaining('Coming Soon'), findsWidgets);
  });
}

class _FakeGetDiscoverTools implements GetDiscoverTools {
  _FakeGetDiscoverTools(this.tools);
  final List<ToolEntry> tools;

  @override
  call() => Right(tools);
}
