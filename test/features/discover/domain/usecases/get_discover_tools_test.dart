// test/features/discover/domain/usecases/get_discover_tools_test.dart

import 'package:flutter_test/flutter_test.dart';
import 'package:myapp/features/discover/domain/entities/tool_entry.dart';
import 'package:myapp/features/discover/domain/usecases/get_discover_tools.dart';

void main() {
  late GetDiscoverTools usecase;

  setUp(() {
    usecase = GetDiscoverTools();
  });

  group('GetDiscoverTools', () {
    test('should return list of tool entries', () {
      // act
      final result = usecase();

      // assert
      expect(result.isRight(), true);
      result.fold(
        (failure) => fail('Should not return failure'),
        (tools) {
          expect(tools, isA<List<ToolEntry>>());
          expect(tools.length, 10);
          expect(
            tools.map((tool) => tool.id).toList(),
            [
              'scan',
              'convert-images',
              'convert-docx',
              'convert-ppt',
              'convert-xlsx',
              'merge',
              'split',
              'manage-pages',
              'extract-pages',
              'ai',
            ],
          );
        },
      );
    });

    test('should return tools with correct properties', () {
      // act
      final result = usecase();

      // assert
      result.fold(
        (failure) => fail('Should not return failure'),
        (tools) {
          final mergeTool = tools.firstWhere((t) => t.id == 'merge');
          expect(mergeTool.title, 'Merge PDFs');
          expect(mergeTool.iconName, 'merge_type');
          expect(mergeTool.route, '/merge-pdf');
          expect(mergeTool.enabled, true);
          expect(mergeTool.description, isNotNull);

          final manageTool = tools.firstWhere((t) => t.id == 'manage-pages');
          expect(manageTool.route, '/manage-pages');
          expect(manageTool.enabled, true);

          final extractTool = tools.firstWhere((t) => t.id == 'extract-pages');
          expect(extractTool.route, '/extract-pages');
          expect(extractTool.enabled, true);
        },
      );
    });

    test('should return conversion tools as enabled', () {
      // act
      final result = usecase();

      // assert
      result.fold(
        (failure) => fail('Should not return failure'),
        (tools) {
          // Check that our new conversion tools are enabled
          final scanTool = tools.firstWhere((t) => t.id == 'scan');
          final convertImagesTool = tools.firstWhere((t) => t.id == 'convert-images');
          final convertDocxTool = tools.firstWhere((t) => t.id == 'convert-docx');
          final convertPptTool = tools.firstWhere((t) => t.id == 'convert-ppt');
          final convertXlsxTool = tools.firstWhere((t) => t.id == 'convert-xlsx');
          
          expect(scanTool.enabled, true);
          expect(convertImagesTool.enabled, true);
          expect(convertDocxTool.enabled, true);
          expect(convertPptTool.enabled, true);
          expect(convertXlsxTool.enabled, true);
          
          // Check that PDF management tools are enabled
          final mergeTool = tools.firstWhere((t) => t.id == 'merge');
          final splitTool = tools.firstWhere((t) => t.id == 'split');
          final manageTool = tools.firstWhere((t) => t.id == 'manage-pages');
          final extractTool = tools.firstWhere((t) => t.id == 'extract-pages');
          final aiTool = tools.firstWhere((t) => t.id == 'ai');
          
          expect(mergeTool.enabled, true);
          expect(splitTool.enabled, true);
          expect(manageTool.enabled, true);
          expect(extractTool.enabled, true);
          expect(aiTool.enabled, false);
        },
      );
    });
  });
}
