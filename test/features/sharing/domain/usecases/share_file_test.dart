// test/features/sharing/domain/usecases/share_file_test.dart

import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/sharing/domain/repositories/sharing_repository.dart';
import 'package:myapp/features/sharing/domain/usecases/share_file.dart';

class MockSharingRepository extends Mock implements SharingRepository {}

void main() {
  late ShareFile usecase;
  late MockSharingRepository mockRepository;

  setUp(() {
    mockRepository = MockSharingRepository();
    usecase = ShareFile(mockRepository);
  });

  group('ShareFile', () {
    const tPath = '/storage/test.pdf';
    const tMime = 'application/pdf';

    test('should share file successfully', () async {
      // arrange
      when(() => mockRepository.shareFile(
            path: tPath,
            mime: tMime,
          )).thenAnswer((_) async => const Right(unit));

      // act
      final result = await usecase(path: tPath, mime: tMime);

      // assert
      expect(result, const Right(unit));
      verify(() => mockRepository.shareFile(
            path: tPath,
            mime: tMime,
          )).called(1);
      verifyNoMoreInteractions(mockRepository);
    });

    test('should return FileNotFoundFailure when file does not exist',
        () async {
      // arrange
      final tFailure = FileNotFoundFailure(tPath);
      when(() => mockRepository.shareFile(
            path: tPath,
            mime: tMime,
          )).thenAnswer((_) async => Left(tFailure));

      // act
      final result = await usecase(path: tPath, mime: tMime);

      // assert
      expect(result, Left(tFailure));
    });

    test('should return SharingFailure when sharing fails', () async {
      // arrange
      const tFailure = SharingFailure('Failed to share file');
      when(() => mockRepository.shareFile(
            path: tPath,
            mime: tMime,
          )).thenAnswer((_) async => const Left(tFailure));

      // act
      final result = await usecase(path: tPath, mime: tMime);

      // assert
      expect(result, const Left(tFailure));
    });

    test('should work with different MIME types', () async {
      // arrange
      const tDocxMime =
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      when(() => mockRepository.shareFile(
            path: tPath,
            mime: tDocxMime,
          )).thenAnswer((_) async => const Right(unit));

      // act
      final result = await usecase(path: tPath, mime: tDocxMime);

      // assert
      expect(result, const Right(unit));
    });
  });
}
