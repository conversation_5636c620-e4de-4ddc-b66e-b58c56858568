// test/features/document_viewer/data/repositories/document_reader_repository_impl_test.dart

import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/document_viewer/data/datasources/document_reader_datasource.dart';
import 'package:myapp/features/document_viewer/data/repositories/document_reader_repository_impl.dart';

class MockDocumentReaderDataSource extends Mock implements DocumentReaderDataSource {}

void main() {
  late DocumentReaderRepositoryImpl repository;
  late MockDocumentReaderDataSource mockDataSource;

  setUp(() {
    mockDataSource = MockDocumentReaderDataSource();
    repository = DocumentReaderRepositoryImpl(mockDataSource);
  });

  group('readDocument', () {
    const tPath = '/storage/test.pdf';
    final tBytes = List<int>.generate(100, (i) => i);

    test('should return DocumentContent when datasource reads file successfully', () async {
      // arrange
      when(() => mockDataSource.readDocument(any()))
          .thenAnswer((_) async => tBytes);

      // act
      final result = await repository.readDocument(tPath);

      // assert
      expect(result.isRight(), true);
      result.fold(
        (failure) => fail('Should not return failure'),
        (content) {
          expect(content.documentId, tPath);
          expect(content.bytes, tBytes);
          expect(content.bytes.length, 100);
        },
      );
      verify(() => mockDataSource.readDocument(tPath)).called(1);
    });

    test('should return FileNotFoundFailure when file does not exist', () async {
      // arrange
      when(() => mockDataSource.readDocument(any()))
          .thenThrow(Exception('File does not exist'));

      // act
      final result = await repository.readDocument(tPath);

      // assert
      expect(result.isLeft(), true);
      result.fold(
        (failure) {
          expect(failure, isA<FileNotFoundFailure>());
          final notFoundFailure = failure as FileNotFoundFailure;
          expect(notFoundFailure.message, 'File not found: $tPath');
          expect(notFoundFailure.filePath, tPath);
        },
        (content) => fail('Should not return content'),
      );
    });

    test('should return FileReadFailure when file is too large', () async {
      // arrange
      when(() => mockDataSource.readDocument(any()))
          .thenThrow(Exception('File too large'));

      // act
      final result = await repository.readDocument(tPath);

      // assert
      expect(result.isLeft(), true);
      result.fold(
        (failure) {
          expect(failure, isA<FileReadFailure>());
          expect(failure.message, contains('too large'));
        },
        (content) => fail('Should not return content'),
      );
    });

    test('should return UnsupportedFileFormatFailure for unsupported file type', () async {
      // arrange
      when(() => mockDataSource.readDocument(any()))
          .thenThrow(Exception('Unsupported file type'));

      // act
      final result = await repository.readDocument(tPath);

      // assert
      expect(result.isLeft(), true);
      result.fold(
        (failure) {
          expect(failure, isA<UnsupportedFileFormatFailure>());
          expect(failure.message, contains('Unsupported file type'));
        },
        (content) => fail('Should not return content'),
      );
    });

    test('should return FileReadFailure for generic errors', () async {
      // arrange
      when(() => mockDataSource.readDocument(any()))
          .thenThrow(Exception('Generic read error'));

      // act
      final result = await repository.readDocument(tPath);

      // assert
      expect(result.isLeft(), true);
      result.fold(
        (failure) {
          expect(failure, isA<FileReadFailure>());
          expect(failure.message, contains('Failed to read document'));
        },
        (content) => fail('Should not return content'),
      );
    });

    test('should handle different file paths correctly', () async {
      // arrange
      const paths = [
        '/storage/document.pdf',
        '/storage/folder/file.docx',
        '/data/spreadsheet.xlsx',
        '/downloads/presentation.pptx',
      ];

      for (final path in paths) {
        when(() => mockDataSource.readDocument(path))
            .thenAnswer((_) async => tBytes);

        // act
        final result = await repository.readDocument(path);

        // assert
        expect(result.isRight(), true);
        result.fold(
          (failure) => fail('Should not return failure for path: $path'),
          (content) => expect(content.documentId, path),
        );
      }
    });
  });
}
