// test/features/document_viewer/domain/usecases/read_document_content_test.dart

import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/document_viewer/domain/entities/document_content.dart';
import 'package:myapp/features/document_viewer/domain/repositories/document_reader_repository.dart';
import 'package:myapp/features/document_viewer/domain/usecases/read_document_content.dart';

class MockDocumentReaderRepository extends Mock implements DocumentReaderRepository {}

void main() {
  late ReadDocumentContent usecase;
  late MockDocumentReaderRepository mockRepository;

  setUp(() {
    mockRepository = MockDocumentReaderRepository();
    usecase = ReadDocumentContent(mockRepository);
  });

  group('ReadDocumentContent', () {
    const tPath = '/storage/test.pdf';
    final tContent = DocumentContent(
      documentId: tPath,
      bytes: [1, 2, 3, 4, 5],
    );

    test('should return document content from repository', () async {
      // arrange
      when(() => mockRepository.readDocument(any()))
          .thenAnswer((_) async => Right(tContent));

      // act
      final result = await usecase(tPath);

      // assert
      expect(result, Right(tContent));
      verify(() => mockRepository.readDocument(tPath)).called(1);
      verifyNoMoreInteractions(mockRepository);
    });

    test('should return FileSystemFailure when file not found', () async {
      // arrange
      const tFailure = FileSystemFailure('File not found');
      when(() => mockRepository.readDocument(any()))
          .thenAnswer((_) async => const Left(tFailure));

      // act
      final result = await usecase(tPath);

      // assert
      expect(result, const Left(tFailure));
      verify(() => mockRepository.readDocument(tPath)).called(1);
    });

    test('should return FileSystemFailure when file cannot be read', () async {
      // arrange
      const tFailure = FileSystemFailure('Failed to read file');
      when(() => mockRepository.readDocument(any()))
          .thenAnswer((_) async => const Left(tFailure));

      // act
      final result = await usecase(tPath);

      // assert
      expect(result, const Left(tFailure));
      verify(() => mockRepository.readDocument(tPath)).called(1);
    });
  });
}
