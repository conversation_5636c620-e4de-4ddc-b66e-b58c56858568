import 'package:flutter_test/flutter_test.dart';
import 'package:dartz/dartz.dart';
import 'package:mocktail/mocktail.dart';
import 'package:myapp/features/file_conversion/domain/entities/conversion_result.dart';
import 'package:myapp/features/file_conversion/domain/repositories/file_conversion_repository.dart';
import 'package:myapp/features/file_conversion/domain/usecases/scan_document.dart';

class MockFileConversionRepository extends Mock implements FileConversionRepository {}

void main() {
  late ScanDocumentUseCase usecase;
  late MockFileConversionRepository mockRepository;

  setUp(() {
    mockRepository = MockFileConversionRepository();
    usecase = ScanDocumentUseCase(mockRepository);
  });

  final tConversionResult = ConversionResult(
    outputPath: '/storage/downloads/scanned.pdf',
    fileName: 'scanned.pdf',
    fileSize: 102400,
    createdAt: DateTime(2025, 10, 3),
  );

  test('should call the repository to capture images', () async {
    // arrange
    when(() => mockRepository.captureImages()).thenAnswer((_) async => const Right(['/path/to/captured/image.jpg']));

    // act
    final result = await usecase();

    // assert
    expect(result, const Right(['/path/to/captured/image.jpg']));
    verify(() => mockRepository.captureImages()).called(1);
    verifyNoMoreInteractions(mockRepository);
  });

  test('should call the repository methods for complete scan flow', () async {
    // arrange
    when(() => mockRepository.captureImages()).thenAnswer((_) async => const Right(['/path/to/captured/image.jpg']));
    when(() => mockRepository.convertImagesToPdf(
      ['/path/to/captured/image.jpg'],
      'scanned.pdf',
      null,
    )).thenAnswer((_) async => Right(tConversionResult));

    // act
    final result = await usecase.scanAndConvertToPdf();

    // assert
    expect(result, Right(tConversionResult));
    verify(() => mockRepository.captureImages()).called(1);
    verify(() => mockRepository.convertImagesToPdf(
      ['/path/to/captured/image.jpg'],
      'scanned.pdf',
      null,
    )).called(1);
    verifyNoMoreInteractions(mockRepository);
  });
}
