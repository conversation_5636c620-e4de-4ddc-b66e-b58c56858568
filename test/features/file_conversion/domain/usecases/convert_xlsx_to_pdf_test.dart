import 'package:flutter_test/flutter_test.dart';
import 'package:dartz/dartz.dart';
import 'package:mocktail/mocktail.dart';
import 'package:myapp/features/file_conversion/domain/entities/conversion_result.dart';
import 'package:myapp/features/file_conversion/domain/repositories/file_conversion_repository.dart';
import 'package:myapp/features/file_conversion/domain/usecases/convert_xlsx_to_pdf.dart';

class MockFileConversionRepository extends Mock implements FileConversionRepository {}

void main() {
  late ConvertXlsxToPdfUseCase usecase;
  late MockFileConversionRepository mockRepository;

  setUp(() {
    mockRepository = MockFileConversionRepository();
    usecase = ConvertXlsxToPdfUseCase(mockRepository);
  });

  final tXlsxPath = '/path/to/spreadsheet.xlsx';
  final tOutputFileName = 'converted.pdf';
  final tConversionResult = ConversionResult(
    outputPath: '/storage/downloads/converted.pdf',
    fileName: 'converted.pdf',
    fileSize: 102400,
    createdAt: DateTime(2025, 10, 3),
  );

  test('should call the repository to convert XLSX to PDF', () async {
    // arrange
    when(() => mockRepository.convertXlsxToPdf(
      tXlsxPath,
      tOutputFileName,
    )).thenAnswer((_) async => Right(tConversionResult));

    // act
    final result = await usecase(tXlsxPath, tOutputFileName);

    // assert
    expect(result, Right(tConversionResult));
    verify(() => mockRepository.convertXlsxToPdf(
      tXlsxPath,
      tOutputFileName,
    )).called(1);
    verifyNoMoreInteractions(mockRepository);
  });
}
