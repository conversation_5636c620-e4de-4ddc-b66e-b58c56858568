import 'package:flutter_test/flutter_test.dart';
import 'package:dartz/dartz.dart';
import 'package:mocktail/mocktail.dart';
import 'package:myapp/features/file_conversion/domain/entities/conversion_request.dart';
import 'package:myapp/features/file_conversion/domain/entities/conversion_result.dart';
import 'package:myapp/features/file_conversion/domain/repositories/file_conversion_repository.dart';
import 'package:myapp/features/file_conversion/domain/usecases/convert_images_to_pdf.dart';

class MockFileConversionRepository extends Mock implements FileConversionRepository {}

void main() {
  late ConvertImagesToPdfUseCase usecase;
  late MockFileConversionRepository mockRepository;

  setUp(() {
    mockRepository = MockFileConversionRepository();
    usecase = ConvertImagesToPdfUseCase(mockRepository);
  });

  final tImagePath = '/path/to/image.jpg';
  final tOutputFileName = 'converted.pdf';
  final tOrientation = PageOrientation.portrait;
  final tConversionResult = ConversionResult(
    outputPath: '/storage/downloads/converted.pdf',
    fileName: 'converted.pdf',
    fileSize: 102400,
    createdAt: DateTime(2025, 10, 3),
  );

  test('should call the repository to convert images to PDF', () async {
    // arrange
    when(() => mockRepository.convertImagesToPdf(
      [tImagePath],
      tOutputFileName,
      tOrientation,
    )).thenAnswer((_) async => Right(tConversionResult));

    // act
    final result = await usecase([tImagePath], tOutputFileName, tOrientation);

    // assert
    expect(result, Right(tConversionResult));
    verify(() => mockRepository.convertImagesToPdf(
      [tImagePath],
      tOutputFileName,
      tOrientation,
    )).called(1);
    verifyNoMoreInteractions(mockRepository);
  });
}
