import 'package:flutter_test/flutter_test.dart';
import 'package:myapp/features/file_conversion/domain/entities/conversion_type.dart';

void main() {
  group('ConversionType', () {
    test('should have correct enum values', () {
      // This test will fail initially until we implement the ConversionType enum
      expect(ConversionType.imageToPdf, ConversionType.imageToPdf);
      expect(ConversionType.docxToPdf, ConversionType.docxToPdf);
      expect(ConversionType.pptToPdf, ConversionType.pptToPdf);
      expect(ConversionType.xlsxToPdf, ConversionType.xlsxToPdf);
      expect(ConversionType.scanToPdf, ConversionType.scanToPdf);
      
      // Ensure we have the correct number of values
      expect(ConversionType.values.length, 5);
    });
  });
}
