import 'package:flutter_test/flutter_test.dart';
import 'package:myapp/features/file_conversion/domain/entities/conversion_result.dart';

void main() {
  group('ConversionResult', () {
    test('should create ConversionResult entity with correct properties', () {
      // This test will fail initially until we implement the ConversionResult entity
      final conversionResult = ConversionResult(
        outputPath: '/storage/downloads/converted.pdf',
        fileName: 'converted.pdf',
        fileSize: 102400, // 100KB in bytes
        createdAt: DateTime(2025, 10, 3),
      );

      expect(conversionResult.outputPath, '/storage/downloads/converted.pdf');
      expect(conversionResult.fileName, 'converted.pdf');
      expect(conversionResult.fileSize, 102400);
      expect(conversionResult.createdAt, DateTime(2025, 10, 3));
    });

    test('should support copyWith method for immutability', () {
      final originalResult = ConversionResult(
        outputPath: '/storage/downloads/converted.pdf',
        fileName: 'converted.pdf',
        fileSize: 102400,
        createdAt: DateTime(2025, 10, 3),
      );

      final updatedResult = originalResult.copyWith(fileName: 'updated.pdf');

      expect(updatedResult.fileName, 'updated.pdf');
      expect(updatedResult.outputPath, originalResult.outputPath);
      expect(updatedResult.fileSize, originalResult.fileSize);
      expect(updatedResult.createdAt, originalResult.createdAt);
    });
  });
}
