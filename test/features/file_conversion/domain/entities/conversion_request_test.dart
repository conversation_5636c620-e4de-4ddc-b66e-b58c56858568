import 'dart:ui';

import 'package:flutter_test/flutter_test.dart';
import 'package:myapp/features/file_conversion/domain/entities/conversion_type.dart';
import 'package:myapp/features/file_conversion/domain/entities/scanned_page.dart';
import 'package:myapp/features/file_conversion/domain/entities/conversion_request.dart';

void main() {
  group('ConversionRequest', () {
    test('should create ConversionRequest entity with correct properties', () {
      // This test will fail initially until we implement the ConversionRequest entity
      final conversionRequest = ConversionRequest(
        type: ConversionType.imageToPdf,
        sourcePaths: ['/path/to/image1.jpg', '/path/to/image2.jpg'],
        outputFileName: 'converted.pdf',
        orientation: PageOrientation.portrait,
        scannedPages: [
          ScannedPage(
            imagePath: '/path/to/image.jpg',
            filter: PageFilter.color,
            rotationAngle: 0.0,
            cropRect: Rect.fromLTWH(0, 0, 100, 100),
          )
        ],
      );

      expect(conversionRequest.type, ConversionType.imageToPdf);
      expect(conversionRequest.sourcePaths, 
          ['/path/to/image1.jpg', '/path/to/image2.jpg']);
      expect(conversionRequest.outputFileName, 'converted.pdf');
      expect(conversionRequest.orientation, PageOrientation.portrait);
      expect(conversionRequest.scannedPages!.length, 1);
      expect(conversionRequest.scannedPages![0].imagePath, '/path/to/image.jpg');
    });

    test('should have correct PageOrientation values', () {
      expect(PageOrientation.values.length, 3);
      expect(PageOrientation.values, contains(PageOrientation.portrait));
      expect(PageOrientation.values, contains(PageOrientation.landscape));
      expect(PageOrientation.values, contains(PageOrientation.auto));
    });

    test('should support copyWith method for immutability', () {
      final originalRequest = ConversionRequest(
        type: ConversionType.imageToPdf,
        sourcePaths: ['/path/to/image1.jpg'],
        outputFileName: 'converted.pdf',
        orientation: PageOrientation.portrait,
      );

      final updatedRequest = originalRequest.copyWith(
        outputFileName: 'updated.pdf',
      );

      expect(updatedRequest.outputFileName, 'updated.pdf');
      expect(updatedRequest.type, originalRequest.type);
      expect(updatedRequest.sourcePaths, originalRequest.sourcePaths);
      expect(updatedRequest.orientation, originalRequest.orientation);
    });
  });
}
