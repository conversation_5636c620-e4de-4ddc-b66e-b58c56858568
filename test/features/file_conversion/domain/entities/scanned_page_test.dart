import 'dart:ui';

import 'package:flutter_test/flutter_test.dart';
import 'package:myapp/features/file_conversion/domain/entities/scanned_page.dart';

void main() {
  group('ScannedPage', () {
    test('should create ScannedPage entity with correct properties', () {
      // This test will fail initially until we implement the ScannedPage entity
      final scannedPage = ScannedPage(
        imagePath: '/path/to/image.jpg',
        filter: PageFilter.color,
        rotationAngle: 90.0,
        cropRect: Rect.fromLTWH(10, 10, 100, 100),
      );

      expect(scannedPage.imagePath, '/path/to/image.jpg');
      expect(scannedPage.filter, PageFilter.color);
      expect(scannedPage.rotationAngle, 90.0);
      expect(scannedPage.cropRect, Rect.fromLTWH(10, 10, 100, 100));
    });

    test('should have correct PageFilter values', () {
      expect(PageFilter.values.length, 3);
      expect(PageFilter.values, contains(PageFilter.color));
      expect(PageFilter.values, contains(PageFilter.grayscale));
      expect(PageFilter.values, contains(PageFilter.blackAndWhite));
    });

    test('should support copyWith method for immutability', () {
      final originalPage = ScannedPage(
        imagePath: '/path/to/image.jpg',
        filter: PageFilter.color,
        rotationAngle: 90.0,
        cropRect: Rect.fromLTWH(10, 10, 100, 100),
      );

      final updatedPage = originalPage.copyWith(filter: PageFilter.grayscale);

      expect(updatedPage.filter, PageFilter.grayscale);
      expect(updatedPage.imagePath, originalPage.imagePath);
      expect(updatedPage.rotationAngle, originalPage.rotationAngle);
      expect(updatedPage.cropRect, originalPage.cropRect);
    });
  });
}
