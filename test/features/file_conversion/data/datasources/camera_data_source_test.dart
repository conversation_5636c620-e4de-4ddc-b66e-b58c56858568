import 'dart:io';
import 'package:flutter_doc_scanner/flutter_doc_scanner.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/file_conversion/data/datasources/camera_data_source.dart';
import 'package:myapp/features/file_conversion/data/datasources/camera_data_source_impl.dart';

class _MockFlutterDocScanner extends Mock implements FlutterDocScanner {}

void main() {
  late _MockFlutterDocScanner mockScanner;
  late CameraDataSource dataSource;
  late Directory tempDir;

  setUp(() async {
    mockScanner = _MockFlutterDocScanner();
    dataSource = CameraDataSourceImpl(scanner: mockScanner);
    tempDir = await Directory.systemTemp.createTemp('camera_test_');
  });

  tearDown(() async {
    if (await tempDir.exists()) {
      await tempDir.delete(recursive: true);
    }
  });

  group('CameraDataSourceImpl', () {
    test('returns image paths when scanner succeeds', () async {
      // Create temporary files for testing
      final file1 = File('${tempDir.path}/a.jpg');
      final file2 = File('${tempDir.path}/b.jpg');
      await file1.writeAsString('fake image data');
      await file2.writeAsString('fake image data');

      when(() => mockScanner.getScanDocuments())
          .thenAnswer((_) async => [file1.path, file2.path]);

      final result = await dataSource.captureImages();

      expect(result.isRight(), isTrue);
      result.fold(
        (failure) => fail('Expected success but got failure: $failure'),
        (paths) => expect(paths, equals([file1.path, file2.path])),
      );
    });

    test('returns failure when user cancels scanning', () async {
      when(() => mockScanner.getScanDocuments()).thenAnswer((_) async => null);

      final result = await dataSource.captureImages();

      expect(result.isLeft(), isTrue);
      result.fold(
        (failure) => expect(failure, isA<UserCancelledFailure>()),
        (_) => fail('Expected failure when scan is cancelled'),
      );
    });

    test('returns failure when plugin throws', () async {
      when(() => mockScanner.getScanDocuments())
          .thenThrow(Exception('Plugin error'));

      final result = await dataSource.captureImages();

      expect(result.isLeft(), isTrue);
      result.fold(
        (failure) => expect(
          (failure as CameraFailure).message,
          startsWith('Failed to capture images:'),
        ),
        (_) => fail('Expected failure when plugin throws'),
      );
    });

    test('handles Map return type from plugin', () async {
      // Create temporary files for testing
      final file1 = File('${tempDir.path}/map_test1.jpg');
      final file2 = File('${tempDir.path}/map_test2.jpg');
      await file1.writeAsString('fake image data');
      await file2.writeAsString('fake image data');

      // Simulate the plugin returning a Map instead of List
      when(() => mockScanner.getScanDocuments()).thenAnswer(
        (_) async => {
          'paths': [file1.path, file2.path],
        },
      );

      final result = await dataSource.captureImages();

      expect(result.isRight(), isTrue);
      result.fold(
        (failure) => fail('Expected success but got failure: $failure'),
        (paths) => expect(paths, equals([file1.path, file2.path])),
      );
    });

    test('handles Map with different key structure', () async {
      // Create temporary files for testing
      final file1 = File('${tempDir.path}/map_key_test.jpg');
      await file1.writeAsString('fake image data');

      // Simulate the plugin returning a Map with different keys
      when(() => mockScanner.getScanDocuments()).thenAnswer(
        (_) async => {
          'documents': [file1.path],
          'status': 'success',
        },
      );

      final result = await dataSource.captureImages();

      expect(result.isRight(), isTrue);
      result.fold(
        (failure) => fail('Expected success but got failure: $failure'),
        (paths) => expect(paths, equals([file1.path])),
      );
    });

    test('uses fallback when files do not exist but paths are returned', () async {
      // Simulate the plugin returning paths to files that don't exist
      // This tests the fallback mechanism for timing issues
      final nonExistentPaths = [
        '/non/existent/path1.jpg',
        '/non/existent/path2.jpg',
      ];

      when(() => mockScanner.getScanDocuments()).thenAnswer(
        (_) async => nonExistentPaths,
      );

      final result = await dataSource.captureImages();

      // Should succeed with fallback mechanism
      expect(result.isRight(), isTrue);
      result.fold(
        (failure) => fail('Expected success with fallback but got failure: $failure'),
        (paths) => expect(paths, equals(nonExistentPaths)),
      );
    });
  });
}
