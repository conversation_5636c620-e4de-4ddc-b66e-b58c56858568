import 'package:flutter_doc_scanner/flutter_doc_scanner.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/file_conversion/data/datasources/camera_data_source.dart';
import 'package:myapp/features/file_conversion/data/datasources/camera_data_source_impl.dart';

class _MockFlutterDocScanner extends Mock implements FlutterDocScanner {}

void main() {
  late _MockFlutterDocScanner mockScanner;
  late CameraDataSource dataSource;

  setUp(() {
    mockScanner = _MockFlutterDocScanner();
    dataSource = CameraDataSourceImpl(scanner: mockScanner);
  });

  group('CameraDataSourceImpl', () {
    test('returns image paths when scanner succeeds', () async {
      when(() => mockScanner.getScanDocuments())
          .thenAnswer((_) async => ['path/a.jpg', 'path/b.jpg']);

      final result = await dataSource.captureImages();

      expect(result.isRight(), isTrue);
      result.fold(
        (failure) => fail('Expected success but got failure: $failure'),
        (paths) => expect(paths, equals(['path/a.jpg', 'path/b.jpg'])),
      );
    });

    test('returns failure when user cancels scanning', () async {
      when(() => mockScanner.getScanDocuments()).thenAnswer((_) async => null);

      final result = await dataSource.captureImages();

      expect(result.isLeft(), isTrue);
      result.fold(
        (failure) => expect((failure as CameraFailure).message,
            'Scan cancelled by user'),
        (_) => fail('Expected failure when scan is cancelled'),
      );
    });

    test('returns failure when plugin throws', () async {
      when(() => mockScanner.getScanDocuments())
          .thenThrow(Exception('Plugin error'));

      final result = await dataSource.captureImages();

      expect(result.isLeft(), isTrue);
      result.fold(
        (failure) => expect(
          (failure as CameraFailure).message,
          startsWith('Failed to capture images:'),
        ),
        (_) => fail('Expected failure when plugin throws'),
      );
    });
  });
}
