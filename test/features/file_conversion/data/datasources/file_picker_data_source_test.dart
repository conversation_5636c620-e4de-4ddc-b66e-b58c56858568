import 'package:flutter_test/flutter_test.dart';
import 'package:myapp/features/file_conversion/data/datasources/file_picker_data_source.dart';
import 'package:myapp/features/file_conversion/data/datasources/file_picker_data_source_impl.dart';

void main() {
  group('FilePickerDataSourceImpl', () {
    late FilePickerDataSourceImpl dataSource;

    setUp(() {
      dataSource = FilePickerDataSourceImpl();
    });

    test('should be a FilePickerDataSource', () async {
      expect(dataSource, isA<FilePickerDataSource>());
    });

    test('should implement pickImages method', () async {
      expect(dataSource.pickImages, isA<Function>());
    });

    test('should implement pickDocument method', () async {
      expect(dataSource.pickDocument, isA<Function>());
    });

    test('should implement pickFiles method', () async {
      expect(dataSource.pickFiles, isA<Function>());
    });
  });
}
