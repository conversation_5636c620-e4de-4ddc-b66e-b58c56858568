import 'dart:io';
import 'dart:ui' show Rect;
import 'package:flutter_test/flutter_test.dart';
import 'package:myapp/features/file_conversion/data/datasources/pdf_converter_data_source_impl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path_provider_platform_interface/path_provider_platform_interface.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';
import 'package:mocktail/mocktail.dart';
import 'dart:typed_data';
import 'package:image/image.dart' as img;

class MockPathProviderPlatform extends Fake
    with MockPlatformInterfaceMixin
    implements PathProviderPlatform {
  @override
  Future<String> getTemporaryPath() async {
    // Use a temporary directory for the test
    final tempDir = await Directory.systemTemp.createTemp('converter_test');
    return tempDir.path;
  }
}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  
  group('PDFConverterDataSourceImpl Tests with Real Temp Files', () {
    late PDFConverterDataSourceImpl dataSource;
    late Directory tempDir;

    setUpAll(() async {
      PathProviderPlatform.instance = MockPathProviderPlatform();
    });

    setUp(() async {
      // Create a temporary directory in the system temp directory
      tempDir = await Directory.systemTemp.createTemp('pdf_converter_test_');
      dataSource = PDFConverterDataSourceImpl();
    });

    tearDown(() async {
      // Clean up the temporary directory
      if (tempDir.existsSync()) {
        await tempDir.delete(recursive: true);
      }
    });

    test('convertImagesToPdf creates PDF from real image files', () async {
      // Create a temporary test image
      final testImage = img.Image(width: 100, height: 100);
      // Fill the image with a blue color using a different approach
      for (int y = 0; y < testImage.height; y++) {
        for (int x = 0; x < testImage.width; x++) {
          testImage.setPixelRgba(x, y, 0, 0, 255, 255); // Blue color with full alpha
        }
      }
      final imageBytes = img.encodeJpg(testImage);
      
      final imageFile = File('${tempDir.path}/test_image.jpg');
      await imageFile.writeAsBytes(imageBytes!);
      
      final outputPdf = File('${tempDir.path}/test_output.pdf');
      
      final result = await dataSource.convertImagesToPdf(
        [imageFile.path],
        outputPdf.path,
        null,
      );
      
      expect(result.isRight(), true);
      
      // Clean up
      await imageFile.delete();
      if (await outputPdf.exists()) {
        await outputPdf.delete();
      }
    });

    test('applyImageFilters processes real image with color filter', () async {
      // Create a temporary test image
      final testImage = img.Image(width: 100, height: 100);
      // Fill the image with a green color using a different approach
      for (int y = 0; y < testImage.height; y++) {
        for (int x = 0; x < testImage.width; x++) {
          testImage.setPixelRgba(x, y, 0, 255, 0, 255); // Green color with full alpha
        }
      }
      final imageBytes = img.encodeJpg(testImage);
      
      final inputImageFile = File('${tempDir.path}/input_test_image.jpg');
      await inputImageFile.writeAsBytes(imageBytes!);
      
      final result = await dataSource.applyImageFilters(
        inputImageFile.path,
        'color',
        0.0,
        null,
      );
      
      expect(result.isRight(), true);
      
      if (result.isRight()) {
        final processedImagePath = result.getOrElse(() => '');
        expect(await File(processedImagePath).exists(), true);
        
        // Clean up
        await inputImageFile.delete();
        await File(processedImagePath).delete();
      }
    });

    test('applyImageFilters processes real image with grayscale filter', () async {
      // Create a temporary test image
      final testImage = img.Image(width: 100, height: 100);
      // Fill the image with a red color using a different approach
      for (int y = 0; y < testImage.height; y++) {
        for (int x = 0; x < testImage.width; x++) {
          testImage.setPixelRgba(x, y, 255, 0, 0, 255); // Red color with full alpha
        }
      }
      final imageBytes = img.encodeJpg(testImage);
      
      final inputImageFile = File('${tempDir.path}/input_test_grayscale.jpg');
      await inputImageFile.writeAsBytes(imageBytes!);
      
      final result = await dataSource.applyImageFilters(
        inputImageFile.path,
        'grayscale',
        0.0,
        null,
      );
      
      expect(result.isRight(), true);
      
      if (result.isRight()) {
        final processedImagePath = result.getOrElse(() => '');
        expect(await File(processedImagePath).exists(), true);
        
        // Clean up
        await inputImageFile.delete();
        await File(processedImagePath).delete();
      }
    });

    test('applyImageFilters processes real image with blackAndWhite filter', () async {
      // Create a temporary test image
      final testImage = img.Image(width: 100, height: 100);
      // Fill the image with a gray color using a different approach
      for (int y = 0; y < testImage.height; y++) {
        for (int x = 0; x < testImage.width; x++) {
          testImage.setPixelRgba(x, y, 128, 128, 128, 255); // Gray color with full alpha
        }
      }
      final imageBytes = img.encodeJpg(testImage);
      
      final inputImageFile = File('${tempDir.path}/input_test_bw.jpg');
      await inputImageFile.writeAsBytes(imageBytes!);
      
      final result = await dataSource.applyImageFilters(
        inputImageFile.path,
        'blackAndWhite',
        0.0,
        null,
      );
      
      expect(result.isRight(), true);
      
      if (result.isRight()) {
        final processedImagePath = result.getOrElse(() => '');
        expect(await File(processedImagePath).exists(), true);
        
        // Verify that the black and white filter created a proper binary (black/white) image
        final processedImageBytes = await File(processedImagePath).readAsBytes();
        final processedImage = img.decodeImage(processedImageBytes);
        expect(processedImage, isNotNull);
        
        // Sample a few pixels to verify they're either black or white
        final pixel1 = processedImage!.getPixel(0, 0);
        final pixel2 = processedImage.getPixel(50, 50);
        final pixel3 = processedImage.getPixel(99, 99);
        
        // Each pixel should be either fully black (0,0,0) or fully white (255,255,255)
        final pixels = [pixel1, pixel2, pixel3];
        for (final pixel in pixels) {
          final r = pixel.r;
          final g = pixel.g;
          final b = pixel.b;
          // In a binary black and white image, all RGB values should be either 0 or 255
          expect(r == 0 || r == 255, isTrue, reason: 'Pixel R value should be 0 or 255, got $r');
          expect(g == 0 || g == 255, isTrue, reason: 'Pixel G value should be 0 or 255, got $g');
          expect(b == 0 || b == 255, isTrue, reason: 'Pixel B value should be 0 or 255, got $b');
          // In a proper B&W image, all RGB should be equal (grayscale)
          expect(r, equals(g));
          expect(g, equals(b));
        }
        
        // Clean up
        await inputImageFile.delete();
        await File(processedImagePath).delete();
      }
    });

    test('applyImageFilters with rotation', () async {
      // Create a temporary test image
      final testImage = img.Image(width: 100, height: 50);
      // Fill the image with a blue color using a different approach
      for (int y = 0; y < testImage.height; y++) {
        for (int x = 0; x < testImage.width; x++) {
          testImage.setPixelRgba(x, y, 0, 0, 255, 255); // Blue color with full alpha
        }
      }
      final imageBytes = img.encodeJpg(testImage);
      
      final inputImageFile = File('${tempDir.path}/input_test_rotation.jpg');
      await inputImageFile.writeAsBytes(imageBytes!);
      
      final result = await dataSource.applyImageFilters(
        inputImageFile.path,
        'color',
        90.0, // 90 degree rotation
        null,
      );
      
      expect(result.isRight(), true);
      
      if (result.isRight()) {
        final processedImagePath = result.getOrElse(() => '');
        expect(await File(processedImagePath).exists(), true);
        
        // Clean up
        await inputImageFile.delete();
        await File(processedImagePath).delete();
      }
    });

    test('applyImageFilters with cropping', () async {
      // Create a temporary test image
      final testImage = img.Image(width: 100, height: 100);
      // Fill the image with a green color using a different approach
      for (int y = 0; y < testImage.height; y++) {
        for (int x = 0; x < testImage.width; x++) {
          testImage.setPixelRgba(x, y, 0, 255, 0, 255); // Green color with full alpha
        }
      }
      final imageBytes = img.encodeJpg(testImage);
      
      final inputImageFile = File('${tempDir.path}/input_test_crop.jpg');
      await inputImageFile.writeAsBytes(imageBytes!);
      
      // Define a crop rectangle (crop to upper left quadrant)
      final cropRect = Rect.fromLTWH(0, 0, 50, 50);
      
      final result = await dataSource.applyImageFilters(
        inputImageFile.path,
        'color',
        0.0,
        cropRect,
      );
      
      expect(result.isRight(), true);
      
      if (result.isRight()) {
        final processedImagePath = result.getOrElse(() => '');
        expect(await File(processedImagePath).exists(), true);
        
        // Clean up
        await inputImageFile.delete();
        await File(processedImagePath).delete();
      }
    });

    test('convertDocxToPdf returns appropriate failure for unimplemented feature', () async {
      final result = await dataSource.convertDocxToPdf(
        'nonexistent.docx',
        'output.pdf',
      );
      
      expect(result.isLeft(), true);
    });

    test('convertPptToPdf returns appropriate failure for unimplemented feature', () async {
      final result = await dataSource.convertPptToPdf(
        'nonexistent.pptx',
        'output.pdf',
      );
      
      expect(result.isLeft(), true);
    });

    test('convertXlsxToPdf returns appropriate failure for unimplemented feature', () async {
      final result = await dataSource.convertXlsxToPdf(
        'nonexistent.xlsx',
        'output.pdf',
      );
      
      expect(result.isLeft(), true);
    });
  });
}