import 'package:flutter_test/flutter_test.dart';
import 'package:myapp/features/file_conversion/data/datasources/pdf_converter_data_source.dart';
import 'package:myapp/features/file_conversion/data/datasources/pdf_converter_data_source_impl.dart';

void main() {
  group('PDFConverterDataSourceImpl', () {
    late PDFConverterDataSourceImpl dataSource;

    setUp(() {
      dataSource = PDFConverterDataSourceImpl();
    });

    test('should be a PDFConverterDataSource', () async {
      expect(dataSource, isA<PDFConverterDataSource>());
    });

    test('should implement convertImagesToPdf method', () async {
      expect(dataSource.convertImagesToPdf, isA<Function>());
    });

    test('should implement convertDocxToPdf method', () async {
      expect(dataSource.convertDocxToPdf, isA<Function>());
    });

    test('should implement convertPptToPdf method', () async {
      expect(dataSource.convertPptToPdf, isA<Function>());
    });

    test('should implement convertXlsxToPdf method', () async {
      expect(dataSource.convertXlsxToPdf, isA<Function>());
    });

    test('should implement applyImageFilters method', () async {
      expect(dataSource.applyImageFilters, isA<Function>());
    });
  });
}
