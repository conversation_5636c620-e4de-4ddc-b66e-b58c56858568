import 'package:flutter_test/flutter_test.dart';
import 'package:myapp/features/file_conversion/data/models/conversion_request_model.dart';
import 'package:myapp/features/file_conversion/domain/entities/conversion_request.dart';
import 'package:myapp/features/file_conversion/domain/entities/conversion_type.dart';

void main() {
  group('ConversionRequestModel', () {
    test('should create ConversionRequestModel with correct properties', () {
      // This test will fail initially until we implement the ConversionRequestModel
      final conversionRequestModel = ConversionRequestModel(
        type: 'imageToPdf', 
        sourcePaths: ['/path/to/image1.jpg', '/path/to/image2.jpg'],
        outputFileName: 'converted.pdf',
        orientation: 'portrait',
        scannedPages: null, // Will be handled as JSON
      );

      expect(conversionRequestModel.type, 'imageToPdf');
      expect(conversionRequestModel.sourcePaths, 
          ['/path/to/image1.jpg', '/path/to/image2.jpg']);
      expect(conversionRequestModel.outputFileName, 'converted.pdf');
      expect(conversionRequestModel.orientation, 'portrait');
    });

    test('should convert from domain entity', () {
      // This test will fail initially until we implement the ConversionRequestModel
      final domainEntity = ConversionRequest(
        type: ConversionType.imageToPdf,
        sourcePaths: ['/path/to/image1.jpg'],
        outputFileName: 'converted.pdf',
        orientation: PageOrientation.portrait,
      );

      final model = ConversionRequestModel.fromEntity(domainEntity);
      
      expect(model.type, 'imageToPdf');
      expect(model.sourcePaths, ['/path/to/image1.jpg']);
      expect(model.outputFileName, 'converted.pdf');
      expect(model.orientation, 'portrait');
    });

    test('should convert to domain entity', () {
      // This test will fail initially until we implement the ConversionRequestModel
      final model = ConversionRequestModel(
        type: 'imageToPdf',
        sourcePaths: ['/path/to/image1.jpg'],
        outputFileName: 'converted.pdf',
        orientation: 'portrait',
        scannedPages: null,
      );

      final domainEntity = model.toEntity();
      
      expect(domainEntity.type, ConversionType.imageToPdf);
      expect(domainEntity.sourcePaths, ['/path/to/image1.jpg']);
      expect(domainEntity.outputFileName, 'converted.pdf');
      expect(domainEntity.orientation, PageOrientation.portrait);
    });

    test('should support JSON serialization', () {
      // This test will fail initially until we implement the ConversionRequestModel with JSON support
      final model = ConversionRequestModel(
        type: 'imageToPdf',
        sourcePaths: ['/path/to/image1.jpg'],
        outputFileName: 'converted.pdf',
        orientation: 'portrait', 
        scannedPages: null,
      );

      final jsonMap = model.toJson();
      final deserializedModel = ConversionRequestModel.fromJson(jsonMap);
      
      expect(deserializedModel.type, model.type);
      expect(deserializedModel.sourcePaths, model.sourcePaths);
      expect(deserializedModel.outputFileName, model.outputFileName);
      expect(deserializedModel.orientation, model.orientation);
    });
  });
}
