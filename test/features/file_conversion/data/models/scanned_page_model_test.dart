import 'dart:convert';
import 'dart:ui';
import 'package:flutter_test/flutter_test.dart';
import 'package:myapp/features/file_conversion/data/models/scanned_page_model.dart';
import 'package:myapp/features/file_conversion/domain/entities/scanned_page.dart';

void main() {
  group('ScannedPageModel', () {
    test('should create ScannedPageModel with correct properties', () {
      // This test will verify the ScannedPageModel implementation
      final scannedPageModel = ScannedPageModel(
        imagePath: '/path/to/image.jpg',
        filter: 'color',
        rotationAngle: 90.0,
        cropRect: jsonEncode({'left': 10, 'top': 10, 'width': 100, 'height': 100}),
      );

      expect(scannedPageModel.imagePath, '/path/to/image.jpg');
      expect(scannedPageModel.filter, 'color');
      expect(scannedPageModel.rotationAngle, 90.0);
      expect(scannedPageModel.cropRect, jsonEncode({'left': 10, 'top': 10, 'width': 100, 'height': 100}));
    });

    test('should convert from domain entity', () {
      // This test verifies conversion from domain entity to model
      final domainEntity = ScannedPage(
        imagePath: '/path/to/image.jpg',
        filter: PageFilter.color,
        rotationAngle: 90.0,
        cropRect: Rect.fromLTWH(10, 10, 100, 100),
      );

      final model = ScannedPageModel.fromEntity(domainEntity);
      
      expect(model.imagePath, '/path/to/image.jpg');
      expect(model.filter, 'color'); // Should be string representation
      expect(model.rotationAngle, 90.0);
    });

    test('should convert to domain entity', () {
      // This test verifies conversion from model to domain entity
      final model = ScannedPageModel(
        imagePath: '/path/to/image.jpg',
        filter: 'color',
        rotationAngle: 90.0,
        cropRect: jsonEncode({'left': 10, 'top': 10, 'width': 100, 'height': 100}),
      );

      final domainEntity = model.toEntity();
      
      expect(domainEntity.imagePath, '/path/to/image.jpg');
      expect(domainEntity.filter, PageFilter.color);
      expect(domainEntity.rotationAngle, 90.0);
    });

    test('should support JSON serialization', () {
      // This test verifies JSON serialization/deserialization
      final model = ScannedPageModel(
        imagePath: '/path/to/image.jpg',
        filter: 'color',
        rotationAngle: 90.0,
        cropRect: jsonEncode({'left': 10, 'top': 10, 'width': 100, 'height': 100}),
      );

      final jsonMap = model.toJson();
      final deserializedModel = ScannedPageModel.fromJson(jsonMap);
      
      expect(deserializedModel.imagePath, model.imagePath);
      expect(deserializedModel.filter, model.filter);
      expect(deserializedModel.rotationAngle, model.rotationAngle);
    });
  });
}
