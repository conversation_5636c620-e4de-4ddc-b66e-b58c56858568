import 'package:flutter_test/flutter_test.dart';
import 'package:myapp/features/file_conversion/data/models/conversion_result_model.dart';
import 'package:myapp/features/file_conversion/domain/entities/conversion_result.dart';

void main() {
  group('ConversionResultModel', () {
    test('should create ConversionResultModel with correct properties', () {
      // This test will fail initially until we implement the ConversionResultModel
      final conversionResultModel = ConversionResultModel(
        outputPath: '/storage/downloads/converted.pdf',
        fileName: 'converted.pdf',
        fileSize: 102400, // 100KB in bytes
        createdAt: DateTime(2025, 10, 3).toIso8601String(), // String representation of DateTime
      );

      expect(conversionResultModel.outputPath, '/storage/downloads/converted.pdf');
      expect(conversionResultModel.fileName, 'converted.pdf');
      expect(conversionResultModel.fileSize, 102400);
      expect(conversionResultModel.createdAt, DateTime(2025, 10, 3).toIso8601String());
    });

    test('should convert from domain entity', () {
      // This test will fail initially until we implement the ConversionResultModel
      final domainEntity = ConversionResult(
        outputPath: '/storage/downloads/converted.pdf',
        fileName: 'converted.pdf',
        fileSize: 102400,
        createdAt: DateTime(2025, 10, 3),
      );

      final model = ConversionResultModel.fromEntity(domainEntity);
      
      expect(model.outputPath, '/storage/downloads/converted.pdf');
      expect(model.fileName, 'converted.pdf');
      expect(model.fileSize, 102400);
      expect(model.createdAt, DateTime(2025, 10, 3).toIso8601String());
    });

    test('should convert to domain entity', () {
      // This test will fail initially until we implement the ConversionResultModel
      final model = ConversionResultModel(
        outputPath: '/storage/downloads/converted.pdf',
        fileName: 'converted.pdf',
        fileSize: 102400,
        createdAt: DateTime(2025, 10, 3).toIso8601String(),
      );

      final domainEntity = model.toEntity();
      
      expect(domainEntity.outputPath, '/storage/downloads/converted.pdf');
      expect(domainEntity.fileName, 'converted.pdf');
      expect(domainEntity.fileSize, 102400);
      expect(domainEntity.createdAt, DateTime(2025, 10, 3));
    });

    test('should support JSON serialization', () {
      // This test will fail initially until we implement the ConversionResultModel with JSON support
      final model = ConversionResultModel(
        outputPath: '/storage/downloads/converted.pdf',
        fileName: 'converted.pdf',
        fileSize: 102400,
        createdAt: DateTime(2025, 10, 3).toIso8601String(),
      );

      final jsonMap = model.toJson();
      final deserializedModel = ConversionResultModel.fromJson(jsonMap);
      
      expect(deserializedModel.outputPath, model.outputPath);
      expect(deserializedModel.fileName, model.fileName);
      expect(deserializedModel.fileSize, model.fileSize);
      expect(deserializedModel.createdAt, model.createdAt);
    });
  });
}
