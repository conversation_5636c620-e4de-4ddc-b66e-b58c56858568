import 'dart:ui' show Rect;

import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/file_conversion/data/datasources/camera_data_source.dart';
import 'package:myapp/features/file_conversion/data/datasources/file_picker_data_source.dart';
import 'package:myapp/features/file_conversion/data/datasources/pdf_converter_data_source.dart';
import 'package:myapp/features/file_conversion/data/repositories/file_conversion_repository_impl.dart';
import 'package:myapp/features/file_conversion/domain/entities/conversion_request.dart';
import 'package:myapp/features/file_conversion/domain/entities/conversion_result.dart';
import 'package:myapp/features/file_conversion/domain/entities/scanned_page.dart';

class MockCameraDataSource extends Mock implements CameraDataSource {}
class MockFilePickerDataSource extends Mock implements FilePickerDataSource {}
class MockPDFConverterDataSource extends Mock implements PDFConverterDataSource {}

void main() {
  late FileConversionRepositoryImpl repository;
  late MockCameraDataSource mockCameraDataSource;
  late MockFilePickerDataSource mockFilePickerDataSource;
  late MockPDFConverterDataSource mockPDFConverterDataSource;

  setUp(() {
    mockCameraDataSource = MockCameraDataSource();
    mockFilePickerDataSource = MockFilePickerDataSource();
    mockPDFConverterDataSource = MockPDFConverterDataSource();
    repository = FileConversionRepositoryImpl(
      cameraDataSource: mockCameraDataSource,
      filePickerDataSource: mockFilePickerDataSource,
      pdfConverterDataSource: mockPDFConverterDataSource,
    );
  });

  group('FileConversionRepositoryImpl', () {
    group('captureImages', () {
      test('should return list of image paths when camera data source succeeds', () async {
        // arrange
        final imagePaths = ['path1', 'path2'];
        when(() => mockCameraDataSource.captureImages())
            .thenAnswer((_) async => right(imagePaths));

        // act
        final result = await repository.captureImages();

        // assert
        expect(result.isRight(), true);
        result.fold(
          (l) => fail('Expected Right, but got Left: $l'),
          (r) => expect(r, equals(imagePaths)),
        );
        verify(() => mockCameraDataSource.captureImages()).called(1);
      });

      test('should return CameraFailure when camera data source fails', () async {
        // arrange
        when(() => mockCameraDataSource.captureImages())
            .thenAnswer((_) async => left(CameraFailure('Camera error')));

        // act
        final result = await repository.captureImages();

        // assert
        expect(result.isLeft(), true);
        verify(() => mockCameraDataSource.captureImages()).called(1);
      });
    });

    group('applyFiltersToImage', () {
      test('should return filtered image path when pdf converter data source succeeds', () async {
        // arrange
        const imagePath = 'input.jpg';
        const filter = PageFilter.color;
        const rotation = 90.0;
        const cropRect = null;
        const outputPath = 'filtered.jpg';
        
        when(() => mockPDFConverterDataSource.applyImageFilters(
          imagePath,
          'color',
          rotation,
          cropRect,
        )).thenAnswer((_) async => right(outputPath));

        // act
        final result = await repository.applyFiltersToImage(
          imagePath,
          filter,
          rotation,
          cropRect,
        );

        // assert
        expect(result.isRight(), true);
        result.fold(
          (l) => fail('Expected Right, but got Left: $l'),
          (r) => expect(r, equals(outputPath)),
        );
        verify(() => mockPDFConverterDataSource.applyImageFilters(
          imagePath,
          'color',
          rotation,
          cropRect,
        )).called(1);
      });

      test('should return ConversionFailure when pdf converter data source fails', () async {
        // arrange
        const imagePath = 'input.jpg';
        const filter = PageFilter.grayscale;
        const rotation = 0.0;
        const cropRect = null;
        
        when(() => mockPDFConverterDataSource.applyImageFilters(
          imagePath,
          'grayscale',
          rotation,
          cropRect,
        )).thenAnswer((_) async => left(ConversionFailure('Filter error')));

        // act
        final result = await repository.applyFiltersToImage(
          imagePath,
          filter,
          rotation,
          cropRect,
        );

        // assert
        expect(result.isLeft(), true);
        verify(() => mockPDFConverterDataSource.applyImageFilters(
          imagePath,
          'grayscale',
          rotation,
          cropRect,
        )).called(1);
      });
    });

    group('convertImagesToPdf', () {
      test('should return conversion result when pdf converter data source succeeds', () async {
        // arrange
        final imagePaths = ['image1.jpg', 'image2.jpg'];
        const outputPath = 'output.pdf';
        const orientation = PageOrientation.auto;
        const convertedPath = 'converted.pdf';
        final conversionResult = ConversionResult(
          outputPath: convertedPath,
          fileName: 'converted.pdf',
          fileSize: 1024,
          createdAt: DateTime.now(),
        );
        
        when(() => mockPDFConverterDataSource.convertImagesToPdf(
          imagePaths,
          outputPath,
          null,
        )).thenAnswer((_) async => right(convertedPath));

        // act
        final result = await repository.convertImagesToPdf(
          imagePaths,
          outputPath,
          orientation,
        );

        // assert
        expect(result.isRight(), true);
        result.fold(
          (l) => fail('Expected Right, but got Left: $l'),
          (r) => expect(r, isA<ConversionResult>()),
        );
        verify(() => mockPDFConverterDataSource.convertImagesToPdf(
          imagePaths,
          outputPath,
          null,
        )).called(1);
      });

      test('should return ConversionFailure when pdf converter data source fails', () async {
        // arrange
        final imagePaths = ['image1.jpg', 'image2.jpg'];
        const outputPath = 'output.pdf';
        const orientation = PageOrientation.portrait;
        
        when(() => mockPDFConverterDataSource.convertImagesToPdf(
          imagePaths,
          outputPath,
          any(),
        )).thenAnswer((_) async => left(ConversionFailure('Conversion error')));

        // act
        final result = await repository.convertImagesToPdf(
          imagePaths,
          outputPath,
          orientation,
        );

        // assert
        expect(result.isLeft(), true);
        verify(() => mockPDFConverterDataSource.convertImagesToPdf(
          imagePaths,
          outputPath,
          any(),
        )).called(1);
      });
    });

    group('pickImages', () {
      test('should return list of image paths when file picker data source succeeds', () async {
        // arrange
        final imagePaths = ['path1.jpg', 'path2.jpg'];
        when(() => mockFilePickerDataSource.pickImages())
            .thenAnswer((_) async => right(imagePaths));

        // act
        final result = await repository.pickImages();

        // assert
        expect(result.isRight(), true);
        result.fold(
          (l) => fail('Expected Right, but got Left: $l'),
          (r) => expect(r, equals(imagePaths)),
        );
        verify(() => mockFilePickerDataSource.pickImages()).called(1);
      });

      test('should return FilePickerFailure when file picker data source fails', () async {
        // arrange
        when(() => mockFilePickerDataSource.pickImages())
            .thenAnswer((_) async => left(FilePickerFailure('Picker error')));

        // act
        final result = await repository.pickImages();

        // assert
        expect(result.isLeft(), true);
        verify(() => mockFilePickerDataSource.pickImages()).called(1);
      });
    });

    group('convertDocxToPdf', () {
      test('should return conversion result when pdf converter data source succeeds', () async {
        // arrange
        const docxPath = 'document.docx';
        const outputFileName = 'converted.pdf';
        const convertedPath = 'converted.pdf';
        final conversionResult = ConversionResult(
          outputPath: convertedPath,
          fileName: 'converted.pdf',
          fileSize: 1024,
          createdAt: DateTime.now(),
        );

        when(() => mockPDFConverterDataSource.convertDocxToPdf(
          docxPath,
          outputFileName,
        )).thenAnswer((_) async => right(convertedPath));

        // act
        final result = await repository.convertDocxToPdf(docxPath, outputFileName);

        // assert
        expect(result.isRight(), true);
        result.fold(
          (l) => fail('Expected Right, but got Left: $l'),
          (r) => expect(r, isA<ConversionResult>()),
        );
        verify(() => mockPDFConverterDataSource.convertDocxToPdf(
          docxPath,
          outputFileName,
        )).called(1);
      });

      test('should return ConversionFailure when pdf converter data source fails', () async {
        // arrange
        const docxPath = 'document.docx';
        const outputFileName = 'converted.pdf';

        when(() => mockPDFConverterDataSource.convertDocxToPdf(
          docxPath,
          outputFileName,
        )).thenAnswer((_) async => left(ConversionFailure('Conversion error')));

        // act
        final result = await repository.convertDocxToPdf(docxPath, outputFileName);

        // assert
        expect(result.isLeft(), true);
        verify(() => mockPDFConverterDataSource.convertDocxToPdf(
          docxPath,
          outputFileName,
        )).called(1);
      });
    });

    group('convertPptToPdf', () {
      test('should return conversion result when pdf converter data source succeeds', () async {
        // arrange
        const pptPath = 'presentation.pptx';
        const outputFileName = 'converted.pdf';
        const convertedPath = 'converted.pdf';
        final conversionResult = ConversionResult(
          outputPath: convertedPath,
          fileName: 'converted.pdf',
          fileSize: 1024,
          createdAt: DateTime.now(),
        );

        when(() => mockPDFConverterDataSource.convertPptToPdf(
          pptPath,
          outputFileName,
        )).thenAnswer((_) async => right(convertedPath));

        // act
        final result = await repository.convertPptToPdf(pptPath, outputFileName);

        // assert
        expect(result.isRight(), true);
        result.fold(
          (l) => fail('Expected Right, but got Left: $l'),
          (r) => expect(r, isA<ConversionResult>()),
        );
        verify(() => mockPDFConverterDataSource.convertPptToPdf(
          pptPath,
          outputFileName,
        )).called(1);
      });

      test('should return ConversionFailure when pdf converter data source fails', () async {
        // arrange
        const pptPath = 'presentation.pptx';
        const outputFileName = 'converted.pdf';

        when(() => mockPDFConverterDataSource.convertPptToPdf(
          pptPath,
          outputFileName,
        )).thenAnswer((_) async => left(ConversionFailure('Conversion error')));

        // act
        final result = await repository.convertPptToPdf(pptPath, outputFileName);

        // assert
        expect(result.isLeft(), true);
        verify(() => mockPDFConverterDataSource.convertPptToPdf(
          pptPath,
          outputFileName,
        )).called(1);
      });
    });

    group('convertXlsxToPdf', () {
      test('should return conversion result when pdf converter data source succeeds', () async {
        // arrange
        const xlsxPath = 'spreadsheet.xlsx';
        const outputFileName = 'converted.pdf';
        const convertedPath = 'converted.pdf';
        final conversionResult = ConversionResult(
          outputPath: convertedPath,
          fileName: 'converted.pdf',
          fileSize: 1024,
          createdAt: DateTime.now(),
        );

        when(() => mockPDFConverterDataSource.convertXlsxToPdf(
          xlsxPath,
          outputFileName,
        )).thenAnswer((_) async => right(convertedPath));

        // act
        final result = await repository.convertXlsxToPdf(xlsxPath, outputFileName);

        // assert
        expect(result.isRight(), true);
        result.fold(
          (l) => fail('Expected Right, but got Left: $l'),
          (r) => expect(r, isA<ConversionResult>()),
        );
        verify(() => mockPDFConverterDataSource.convertXlsxToPdf(
          xlsxPath,
          outputFileName,
        )).called(1);
      });

      test('should return ConversionFailure when pdf converter data source fails', () async {
        // arrange
        const xlsxPath = 'spreadsheet.xlsx';
        const outputFileName = 'converted.pdf';

        when(() => mockPDFConverterDataSource.convertXlsxToPdf(
          xlsxPath,
          outputFileName,
        )).thenAnswer((_) async => left(ConversionFailure('Conversion error')));

        // act
        final result = await repository.convertXlsxToPdf(xlsxPath, outputFileName);

        // assert
        expect(result.isLeft(), true);
        verify(() => mockPDFConverterDataSource.convertXlsxToPdf(
          xlsxPath,
          outputFileName,
        )).called(1);
      });
    });

    group('pickDocument', () {
      test('should return document path when file picker data source succeeds', () async {
        // arrange
        const extensions = ['pdf', 'docx'];
        const documentPath = 'document.pdf';
        when(() => mockFilePickerDataSource.pickDocument(extensions))
            .thenAnswer((_) async => right(documentPath));

        // act
        final result = await repository.pickDocument(extensions);

        // assert
        expect(result.isRight(), true);
        result.fold(
          (l) => fail('Expected Right, but got Left: $l'),
          (r) => expect(r, equals(documentPath)),
        );
        verify(() => mockFilePickerDataSource.pickDocument(extensions)).called(1);
      });

      test('should return FilePickerFailure when file picker data source fails', () async {
        // arrange
        const extensions = ['pdf', 'docx'];
        when(() => mockFilePickerDataSource.pickDocument(extensions))
            .thenAnswer((_) async => left(FilePickerFailure('Picker error')));

        // act
        final result = await repository.pickDocument(extensions);

        // assert
        expect(result.isLeft(), true);
        verify(() => mockFilePickerDataSource.pickDocument(extensions)).called(1);
      });
    });
  });
}