import 'dart:ui';

import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/file_conversion/domain/entities/conversion_request.dart';
import 'package:myapp/features/file_conversion/domain/entities/conversion_result.dart';
import 'package:myapp/features/file_conversion/domain/entities/scanned_page.dart';
import 'package:myapp/features/file_conversion/domain/repositories/file_conversion_repository.dart';
import 'package:myapp/features/file_conversion/presentation/notifiers/scan_notifier.dart';
import 'package:myapp/features/file_conversion/presentation/providers/file_conversion_providers.dart';

class MockFileConversionRepository extends Mock
    implements FileConversionRepository {}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  setUpAll(() {
    registerFallbackValue(PageFilter.color);
    registerFallbackValue(PageOrientation.auto);
    registerFallbackValue(const Rect.fromLTWH(0, 0, 100, 100));
  });

  group('ScanNotifier', () {
    late MockFileConversionRepository mockRepository;
    late ProviderContainer container;

    setUp(() {
      mockRepository = MockFileConversionRepository();
      container = ProviderContainer(
        overrides: [
          fileConversionRepositoryProvider.overrideWith((ref) => mockRepository),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    group('captureImage', () {
      test('should add captured images as new pages', () async {
        when(() => mockRepository.captureImages()).thenAnswer(
          (_) async => const Right(['/test/image1.jpg', '/test/image2.jpg']),
        );

        await container.read(scanProvider.notifier).captureImage();

        final state = container.read(scanProvider);
        expect(state.asData?.value.length, 2);
        expect(state.asData?.value[0].imagePath, '/test/image1.jpg');
        expect(state.asData?.value[0].filter, PageFilter.color);
        expect(state.asData?.value[0].rotationAngle, 0.0);
        expect(state.asData?.value[1].imagePath, '/test/image2.jpg');
      });

      test('should append to existing pages', () async {
        when(() => mockRepository.captureImages()).thenAnswer(
          (_) async => const Right(['/test/image1.jpg']),
        );

        await container.read(scanProvider.notifier).captureImage();

        when(() => mockRepository.captureImages()).thenAnswer(
          (_) async => const Right(['/test/image2.jpg']),
        );

        await container.read(scanProvider.notifier).captureImage();

        final state = container.read(scanProvider);
        expect(state.asData?.value.length, 2);
      });

      test('should preserve existing pages on user cancellation', () async {
        when(() => mockRepository.captureImages()).thenAnswer(
          (_) async => const Right(['/test/image1.jpg']),
        );

        await container.read(scanProvider.notifier).captureImage();

        when(() => mockRepository.captureImages()).thenAnswer(
          (_) async => Left(CameraFailure('Scan cancelled by user')),
        );

        await container.read(scanProvider.notifier).captureImage();

        final state = container.read(scanProvider);
        expect(state.asData?.value.length, 1);
        expect(state.asData?.value[0].imagePath, '/test/image1.jpg');
      });

      test('should set error state on camera failure', () async {
        when(() => mockRepository.captureImages()).thenAnswer(
          (_) async => Left(CameraFailure('Camera unavailable')),
        );

        await container.read(scanProvider.notifier).captureImage();

        final state = container.read(scanProvider);
        expect(state.hasError, true);
        expect(state.error, isA<CameraFailure>());
      });
    });

    group('applyFilter', () {
      test('should apply filter to page at valid index', () async {
        when(() => mockRepository.captureImages()).thenAnswer(
          (_) async => const Right(['/test/image1.jpg']),
        );

        await container.read(scanProvider.notifier).captureImage();
        await container.read(scanProvider.notifier).applyFilter(0, PageFilter.grayscale);

        final state = container.read(scanProvider);
        expect(state.asData?.value[0].filter, PageFilter.grayscale);
      });

      test('should not modify state for invalid index', () async {
        when(() => mockRepository.captureImages()).thenAnswer(
          (_) async => const Right(['/test/image1.jpg']),
        );

        await container.read(scanProvider.notifier).captureImage();
        await container.read(scanProvider.notifier).applyFilter(10, PageFilter.grayscale);

        final state = container.read(scanProvider);
        expect(state.asData?.value[0].filter, PageFilter.color);
      });

      test('should handle negative index gracefully', () async {
        when(() => mockRepository.captureImages()).thenAnswer(
          (_) async => const Right(['/test/image1.jpg']),
        );

        await container.read(scanProvider.notifier).captureImage();
        await container.read(scanProvider.notifier).applyFilter(-1, PageFilter.grayscale);

        final state = container.read(scanProvider);
        expect(state.asData?.value[0].filter, PageFilter.color);
      });
    });

    group('rotatePage', () {
      test('should rotate page with angle normalization', () async {
        when(() => mockRepository.captureImages()).thenAnswer(
          (_) async => const Right(['/test/image1.jpg']),
        );

        await container.read(scanProvider.notifier).captureImage();
        await container.read(scanProvider.notifier).rotatePage(0, 90.0);

        final state = container.read(scanProvider);
        expect(state.asData?.value[0].rotationAngle, 90.0);
      });

      test('should normalize rotation angle beyond 360', () async {
        when(() => mockRepository.captureImages()).thenAnswer(
          (_) async => const Right(['/test/image1.jpg']),
        );

        await container.read(scanProvider.notifier).captureImage();
        await container.read(scanProvider.notifier).rotatePage(0, 450.0);

        final state = container.read(scanProvider);
        expect(state.asData?.value[0].rotationAngle, 90.0);
      });

      test('should not modify state for invalid index', () async {
        when(() => mockRepository.captureImages()).thenAnswer(
          (_) async => const Right(['/test/image1.jpg']),
        );

        await container.read(scanProvider.notifier).captureImage();
        await container.read(scanProvider.notifier).rotatePage(10, 90.0);

        final state = container.read(scanProvider);
        expect(state.asData?.value[0].rotationAngle, 0.0);
      });
    });

    group('cropPage', () {
      test('should update crop rect for valid index', () async {
        when(() => mockRepository.captureImages()).thenAnswer(
          (_) async => const Right(['/test/image1.jpg']),
        );

        await container.read(scanProvider.notifier).captureImage();
        const cropRect = Rect.fromLTWH(10, 10, 100, 100);
        await container.read(scanProvider.notifier).cropPage(0, cropRect);

        final state = container.read(scanProvider);
        expect(state.asData?.value[0].cropRect, cropRect);
      });

      test('should not modify state for invalid index', () async {
        when(() => mockRepository.captureImages()).thenAnswer(
          (_) async => const Right(['/test/image1.jpg']),
        );

        await container.read(scanProvider.notifier).captureImage();
        const cropRect = Rect.fromLTWH(10, 10, 100, 100);
        await container.read(scanProvider.notifier).cropPage(10, cropRect);

        final state = container.read(scanProvider);
        expect(state.asData?.value[0].cropRect, null);
      });
    });

    group('reorderPages', () {
      test('should reorder pages correctly', () async {
        when(() => mockRepository.captureImages()).thenAnswer(
          (_) async => const Right(['/test/image1.jpg', '/test/image2.jpg', '/test/image3.jpg']),
        );

        await container.read(scanProvider.notifier).captureImage();
        await container.read(scanProvider.notifier).reorderPages(0, 2);

        final state = container.read(scanProvider);
        expect(state.asData?.value[0].imagePath, '/test/image2.jpg');
        expect(state.asData?.value[1].imagePath, '/test/image1.jpg');
        expect(state.asData?.value[2].imagePath, '/test/image3.jpg');
      });

      test('should handle invalid old index', () async {
        when(() => mockRepository.captureImages()).thenAnswer(
          (_) async => const Right(['/test/image1.jpg', '/test/image2.jpg']),
        );

        await container.read(scanProvider.notifier).captureImage();
        await container.read(scanProvider.notifier).reorderPages(10, 0);

        final state = container.read(scanProvider);
        expect(state.asData?.value.length, 2);
        expect(state.asData?.value[0].imagePath, '/test/image1.jpg');
      });

      test('should clamp new index to valid range', () async {
        when(() => mockRepository.captureImages()).thenAnswer(
          (_) async => const Right(['/test/image1.jpg', '/test/image2.jpg']),
        );

        await container.read(scanProvider.notifier).captureImage();
        await container.read(scanProvider.notifier).reorderPages(0, 100);

        final state = container.read(scanProvider);
        expect(state.asData?.value.length, 2);
      });
    });

    group('deletePage', () {
      test('should delete page at valid index', () async {
        when(() => mockRepository.captureImages()).thenAnswer(
          (_) async => const Right(['/test/image1.jpg', '/test/image2.jpg']),
        );

        await container.read(scanProvider.notifier).captureImage();
        await container.read(scanProvider.notifier).deletePage(0);

        final state = container.read(scanProvider);
        expect(state.asData?.value.length, 1);
        expect(state.asData?.value[0].imagePath, '/test/image2.jpg');
      });

      test('should not modify state for invalid index', () async {
        when(() => mockRepository.captureImages()).thenAnswer(
          (_) async => const Right(['/test/image1.jpg']),
        );

        await container.read(scanProvider.notifier).captureImage();
        await container.read(scanProvider.notifier).deletePage(10);

        final state = container.read(scanProvider);
        expect(state.asData?.value.length, 1);
      });
    });

    group('updateImagePath', () {
      test('should update image path and clear crop rect', () async {
        when(() => mockRepository.captureImages()).thenAnswer(
          (_) async => const Right(['/test/image1.jpg']),
        );

        await container.read(scanProvider.notifier).captureImage();
        await container.read(scanProvider.notifier).cropPage(0, const Rect.fromLTWH(0, 0, 50, 50));
        await container.read(scanProvider.notifier).updateImagePath(0, '/test/new_image.jpg');

        final state = container.read(scanProvider);
        expect(state.asData?.value[0].imagePath, '/test/new_image.jpg');
        expect(state.asData?.value[0].cropRect, null);
      });

      test('should not modify state for invalid index', () async {
        when(() => mockRepository.captureImages()).thenAnswer(
          (_) async => const Right(['/test/image1.jpg']),
        );

        await container.read(scanProvider.notifier).captureImage();
        await container.read(scanProvider.notifier).updateImagePath(10, '/test/new_image.jpg');

        final state = container.read(scanProvider);
        expect(state.asData?.value[0].imagePath, '/test/image1.jpg');
      });
    });

    group('clearPages', () {
      test('should clear all pages', () async {
        when(() => mockRepository.captureImages()).thenAnswer(
          (_) async => const Right(['/test/image1.jpg', '/test/image2.jpg']),
        );

        await container.read(scanProvider.notifier).captureImage();
        await container.read(scanProvider.notifier).clearPages();

        final state = container.read(scanProvider);
        expect(state.asData?.value.isEmpty, true);
      });
    });

    group('finalizeScan', () {
      test('should return null when pages are empty', () async {
        final result = await container.read(scanProvider.notifier).finalizeScan();

        expect(result, null);
      });

      test('should create PDF without processing for color filter and no rotation', () async {
        when(() => mockRepository.captureImages()).thenAnswer(
          (_) async => const Right(['/test/image1.jpg']),
        );
        when(() => mockRepository.convertImagesToPdf(
          any(),
          any(),
          any(),
        )).thenAnswer(
          (_) async => Right(ConversionResult(
            outputPath: '/test/output.pdf',
            fileName: 'output.pdf',
            fileSize: 1000,
            createdAt: DateTime(2024, 1, 1),
          )),
        );

        await container.read(scanProvider.notifier).captureImage();
        final result = await container.read(scanProvider.notifier).finalizeScan();

        expect(result, isNotNull);
        expect(result?.outputPath, '/test/output.pdf');
        verify(() => mockRepository.convertImagesToPdf(
          ['/test/image1.jpg'],
          any(),
          PageOrientation.auto,
        )).called(1);
        verifyNever(() => mockRepository.applyFiltersToImage(any(), any(), any(), any()));
      });

      test('should process image when grayscale filter is applied', () async {
        when(() => mockRepository.captureImages()).thenAnswer(
          (_) async => const Right(['/test/image1.jpg']),
        );
        when(() => mockRepository.applyFiltersToImage(
          any(),
          any(),
          any(),
          any(),
        )).thenAnswer(
          (_) async => const Right('/test/processed1.jpg'),
        );
        when(() => mockRepository.convertImagesToPdf(
          any(),
          any(),
          any(),
        )).thenAnswer(
          (_) async => Right(ConversionResult(
            outputPath: '/test/output.pdf',
            fileName: 'output.pdf',
            fileSize: 1000,
            createdAt: DateTime(2024, 1, 1),
          )),
        );

        await container.read(scanProvider.notifier).captureImage();
        await container.read(scanProvider.notifier).applyFilter(0, PageFilter.grayscale);
        final result = await container.read(scanProvider.notifier).finalizeScan();

        expect(result, isNotNull);
        verify(() => mockRepository.applyFiltersToImage(
          '/test/image1.jpg',
          PageFilter.grayscale,
          0.0,
          null,
        )).called(1);
        verify(() => mockRepository.convertImagesToPdf(
          ['/test/processed1.jpg'],
          any(),
          PageOrientation.auto,
        )).called(1);
      });

      test('should process image when rotation is applied', () async {
        when(() => mockRepository.captureImages()).thenAnswer(
          (_) async => const Right(['/test/image1.jpg']),
        );
        when(() => mockRepository.applyFiltersToImage(
          any(),
          any(),
          any(),
          any(),
        )).thenAnswer(
          (_) async => const Right('/test/processed1.jpg'),
        );
        when(() => mockRepository.convertImagesToPdf(
          any(),
          any(),
          any(),
        )).thenAnswer(
          (_) async => Right(ConversionResult(
            outputPath: '/test/output.pdf',
            fileName: 'output.pdf',
            fileSize: 1000,
            createdAt: DateTime(2024, 1, 1),
          )),
        );

        await container.read(scanProvider.notifier).captureImage();
        await container.read(scanProvider.notifier).rotatePage(0, 90.0);
        final result = await container.read(scanProvider.notifier).finalizeScan();

        expect(result, isNotNull);
        verify(() => mockRepository.applyFiltersToImage(
          '/test/image1.jpg',
          PageFilter.color,
          90.0,
          null,
        )).called(1);
      });

      test('should process image when crop rect is set', () async {
        when(() => mockRepository.captureImages()).thenAnswer(
          (_) async => const Right(['/test/image1.jpg']),
        );
        when(() => mockRepository.applyFiltersToImage(
          any(),
          any(),
          any(),
          any(),
        )).thenAnswer(
          (_) async => const Right('/test/processed1.jpg'),
        );
        when(() => mockRepository.convertImagesToPdf(
          any(),
          any(),
          any(),
        )).thenAnswer(
          (_) async => Right(ConversionResult(
            outputPath: '/test/output.pdf',
            fileName: 'output.pdf',
            fileSize: 1000,
            createdAt: DateTime(2024, 1, 1),
          )),
        );

        await container.read(scanProvider.notifier).captureImage();
        const cropRect = Rect.fromLTWH(10, 10, 100, 100);
        await container.read(scanProvider.notifier).cropPage(0, cropRect);
        final result = await container.read(scanProvider.notifier).finalizeScan();

        expect(result, isNotNull);
        verify(() => mockRepository.applyFiltersToImage(
          '/test/image1.jpg',
          PageFilter.color,
          0.0,
          cropRect,
        )).called(1);
      });

      test('should return null and set error state when applyFiltersToImage fails', () async {
        when(() => mockRepository.captureImages()).thenAnswer(
          (_) async => const Right(['/test/image1.jpg']),
        );
        when(() => mockRepository.applyFiltersToImage(
          any(),
          any(),
          any(),
          any(),
        )).thenAnswer(
          (_) async => Left(ConversionFailure('Image decode failed')),
        );

        await container.read(scanProvider.notifier).captureImage();
        await container.read(scanProvider.notifier).applyFilter(0, PageFilter.grayscale);
        final result = await container.read(scanProvider.notifier).finalizeScan();

        expect(result, null);
        final state = container.read(scanProvider);
        expect(state.hasError, true);
        expect(state.error, isA<ConversionFailure>());
      });

      test('should return null and set error state when convertImagesToPdf fails', () async {
        when(() => mockRepository.captureImages()).thenAnswer(
          (_) async => const Right(['/test/image1.jpg']),
        );
        when(() => mockRepository.convertImagesToPdf(
          any(),
          any(),
          any(),
        )).thenAnswer(
          (_) async => Left(ConversionFailure('PDF creation failed')),
        );

        await container.read(scanProvider.notifier).captureImage();
        final result = await container.read(scanProvider.notifier).finalizeScan();

        expect(result, null);
        final state = container.read(scanProvider);
        expect(state.hasError, true);
        expect(state.error, isA<ConversionFailure>());
      });

      test('should handle mixed processing for multiple pages', () async {
        when(() => mockRepository.captureImages()).thenAnswer(
          (_) async => const Right(['/test/image1.jpg', '/test/image2.jpg', '/test/image3.jpg']),
        );
        when(() => mockRepository.applyFiltersToImage(
          '/test/image2.jpg',
          PageFilter.grayscale,
          90.0,
          null,
        )).thenAnswer(
          (_) async => const Right('/test/processed2.jpg'),
        );
        when(() => mockRepository.applyFiltersToImage(
          '/test/image3.jpg',
          PageFilter.blackAndWhite,
          0.0,
          any(),
        )).thenAnswer(
          (_) async => const Right('/test/processed3.jpg'),
        );
        when(() => mockRepository.convertImagesToPdf(
          any(),
          any(),
          any(),
        )).thenAnswer(
          (_) async => Right(ConversionResult(
            outputPath: '/test/output.pdf',
            fileName: 'output.pdf',
            fileSize: 1000,
            createdAt: DateTime(2024, 1, 1),
          )),
        );

        await container.read(scanProvider.notifier).captureImage();
        await container.read(scanProvider.notifier).applyFilter(1, PageFilter.grayscale);
        await container.read(scanProvider.notifier).rotatePage(1, 90.0);
        await container.read(scanProvider.notifier).applyFilter(2, PageFilter.blackAndWhite);
        await container.read(scanProvider.notifier).cropPage(2, const Rect.fromLTWH(0, 0, 50, 50));

        final result = await container.read(scanProvider.notifier).finalizeScan();

        expect(result, isNotNull);
        verify(() => mockRepository.applyFiltersToImage(
          '/test/image2.jpg',
          PageFilter.grayscale,
          90.0,
          null,
        )).called(1);
        verify(() => mockRepository.applyFiltersToImage(
          '/test/image3.jpg',
          PageFilter.blackAndWhite,
          0.0,
          any(),
        )).called(1);
        verify(() => mockRepository.convertImagesToPdf(
          ['/test/image1.jpg', '/test/processed2.jpg', '/test/processed3.jpg'],
          any(),
          PageOrientation.auto,
        )).called(1);
      });
    });
  });
}
