// test/features/file_discovery/domain/usecases/add_to_recent_test.dart

import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/file_discovery/domain/entities/document.dart';
import 'package:myapp/features/file_discovery/domain/repositories/document_repository.dart';
import 'package:myapp/features/file_discovery/domain/usecases/add_to_recent.dart';

class MockDocumentRepository extends Mock implements DocumentRepository {}

void main() {
  late AddToRecent usecase;
  late MockDocumentRepository mockRepository;

  setUp(() {
    mockRepository = MockDocumentRepository();
    usecase = AddToRecent(mockRepository);
  });

  setUpAll(() {
    // Register fallback values for mocktail
    registerFallbackValue(Document(
      id: '1',
      name: 'test.pdf',
      path: '/test.pdf',
      type: DocumentType.pdf,
      sizeInBytes: 1024,
      dateModified: DateTime(2024, 1, 1),
    ));
  });

  group('AddToRecent', () {
    final tDocument = Document(
      id: '1',
      name: 'test.pdf',
      path: '/storage/test.pdf',
      type: DocumentType.pdf,
      sizeInBytes: 1024,
      dateModified: DateTime(2024, 1, 1),
    );

    test('should add document to recent list successfully', () async {
      // arrange
      when(() => mockRepository.addToRecent(any()))
          .thenAnswer((_) async => const Right(unit));

      // act
      final result = await usecase(tDocument);

      // assert
      expect(result, const Right(unit));
      verify(() => mockRepository.addToRecent(tDocument)).called(1);
      verifyNoMoreInteractions(mockRepository);
    });

    test('should return FileSystemFailure when adding fails', () async {
      // arrange
      const tFailure = FileSystemFailure('Failed to save recent document');
      when(() => mockRepository.addToRecent(any()))
          .thenAnswer((_) async => const Left(tFailure));

      // act
      final result = await usecase(tDocument);

      // assert
      expect(result, const Left(tFailure));
      verify(() => mockRepository.addToRecent(tDocument)).called(1);
    });
  });
}
