// test/features/file_discovery/domain/usecases/get_recent_documents_test.dart

import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/file_discovery/domain/entities/document.dart';
import 'package:myapp/features/file_discovery/domain/repositories/document_repository.dart';
import 'package:myapp/features/file_discovery/domain/usecases/get_recent_documents.dart';

class MockDocumentRepository extends Mock implements DocumentRepository {}

void main() {
  late GetRecentDocuments usecase;
  late MockDocumentRepository mockRepository;

  setUp(() {
    mockRepository = MockDocumentRepository();
    usecase = GetRecentDocuments(mockRepository);
  });

  group('GetRecentDocuments', () {
    final tDocuments = [
      Document(
        id: '1',
        name: 'recent1.pdf',
        path: '/storage/recent1.pdf',
        type: DocumentType.pdf,
        sizeInBytes: 1024,
        dateModified: DateTime(2024, 1, 2),
      ),
      Document(
        id: '2',
        name: 'recent2.docx',
        path: '/storage/recent2.docx',
        type: DocumentType.docx,
        sizeInBytes: 2048,
        dateModified: DateTime(2024, 1, 1),
      ),
    ];

    test('should return list of recent documents from repository', () async {
      // arrange
      when(() => mockRepository.getRecentDocuments())
          .thenAnswer((_) async => Right(tDocuments));

      // act
      final result = await usecase();

      // assert
      expect(result, Right(tDocuments));
      verify(() => mockRepository.getRecentDocuments()).called(1);
      verifyNoMoreInteractions(mockRepository);
    });

    test('should return empty list when no recent documents', () async {
      // arrange
      when(() => mockRepository.getRecentDocuments())
          .thenAnswer((_) async => const Right([]));

      // act
      final result = await usecase();

      // assert
      expect(result.isRight(), isTrue);
      result.fold(
        (l) => fail('Expected Right but got Left'),
        (r) => expect(r, isEmpty),
      );
      verify(() => mockRepository.getRecentDocuments()).called(1);
    });

    test('should return FileSystemFailure when error occurs', () async {
      // arrange
      const tFailure = FileSystemFailure('Failed to load recent documents');
      when(() => mockRepository.getRecentDocuments())
          .thenAnswer((_) async => const Left(tFailure));

      // act
      final result = await usecase();

      // assert
      expect(result, const Left(tFailure));
      verify(() => mockRepository.getRecentDocuments()).called(1);
    });
  });
}
