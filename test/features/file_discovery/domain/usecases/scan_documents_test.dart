// test/features/file_discovery/domain/usecases/scan_documents_test.dart

import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/file_discovery/domain/entities/document.dart';
import 'package:myapp/features/file_discovery/domain/repositories/document_repository.dart';
import 'package:myapp/features/file_discovery/domain/usecases/scan_documents.dart';

class MockDocumentRepository extends Mock implements DocumentRepository {}

void main() {
  late ScanDocuments usecase;
  late MockDocumentRepository mockRepository;

  setUp(() {
    mockRepository = MockDocumentRepository();
    usecase = ScanDocuments(mockRepository);
  });

  group('ScanDocuments', () {
    final tDocuments = [
      Document(
        id: '1',
        name: 'test.pdf',
        path: '/storage/test.pdf',
        type: DocumentType.pdf,
        sizeInBytes: 1024,
        dateModified: DateTime(2024, 1, 1),
      ),
      Document(
        id: '2',
        name: 'document.docx',
        path: '/storage/document.docx',
        type: DocumentType.docx,
        sizeInBytes: 2048,
        dateModified: DateTime(2024, 1, 2),
      ),
    ];

    test('should return list of documents from repository', () async {
      // arrange
      when(() => mockRepository.scanDocuments())
          .thenAnswer((_) async => Right(tDocuments));

      // act
      final result = await usecase();

      // assert
      expect(result, Right(tDocuments));
      verify(() => mockRepository.scanDocuments()).called(1);
      verifyNoMoreInteractions(mockRepository);
    });

    test('should return empty list when no documents found', () async {
      // arrange
      when(() => mockRepository.scanDocuments())
          .thenAnswer((_) async => const Right([]));

      // act
      final result = await usecase();

      // assert
      expect(result.isRight(), isTrue);
      result.fold(
        (l) => fail('Expected Right but got Left'),
        (r) => expect(r, isEmpty),
      );
      verify(() => mockRepository.scanDocuments()).called(1);
    });

    test('should return FileSystemFailure when permission denied', () async {
      // arrange
      const tFailure = FileSystemFailure('Permission denied');
      when(() => mockRepository.scanDocuments())
          .thenAnswer((_) async => const Left(tFailure));

      // act
      final result = await usecase();

      // assert
      expect(result, const Left(tFailure));
      verify(() => mockRepository.scanDocuments()).called(1);
    });

    test('should return FileSystemFailure when error occurs', () async {
      // arrange
      const tFailure = FileSystemFailure('Failed to scan documents');
      when(() => mockRepository.scanDocuments())
          .thenAnswer((_) async => const Left(tFailure));

      // act
      final result = await usecase();

      // assert
      expect(result, const Left(tFailure));
      verify(() => mockRepository.scanDocuments()).called(1);
    });
  });
}
