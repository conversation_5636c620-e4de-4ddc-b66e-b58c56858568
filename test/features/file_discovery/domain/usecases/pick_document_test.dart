// test/features/file_discovery/domain/usecases/pick_document_test.dart

import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/file_discovery/domain/entities/document.dart';
import 'package:myapp/features/file_discovery/domain/repositories/document_repository.dart';
import 'package:myapp/features/file_discovery/domain/usecases/pick_document.dart';

class MockDocumentRepository extends Mock implements DocumentRepository {}

void main() {
  late PickDocument usecase;
  late MockDocumentRepository mockRepository;

  setUp(() {
    mockRepository = MockDocumentRepository();
    usecase = PickDocument(mockRepository);
  });

  group('PickDocument', () {
    final tDocument = Document(
      id: '1',
      name: 'test.pdf',
      path: '/storage/test.pdf',
      type: DocumentType.pdf,
      sizeInBytes: 1024,
      dateModified: DateTime(2024, 1, 1),
    );

    test('should return picked document from repository', () async {
      // arrange
      when(() => mockRepository.pickDocument())
          .thenAnswer((_) async => Right(tDocument));

      // act
      final result = await usecase();

      // assert
      expect(result, Right(tDocument));
      verify(() => mockRepository.pickDocument()).called(1);
      verifyNoMoreInteractions(mockRepository);
    });

    test('should return FileSystemFailure when user cancels', () async {
      // arrange
      const tFailure = FileSystemFailure('User cancelled file selection');
      when(() => mockRepository.pickDocument())
          .thenAnswer((_) async => const Left(tFailure));

      // act
      final result = await usecase();

      // assert
      expect(result, const Left(tFailure));
      verify(() => mockRepository.pickDocument()).called(1);
    });

    test('should return FileSystemFailure when file picker fails', () async {
      // arrange
      const tFailure = FileSystemFailure('Failed to pick file');
      when(() => mockRepository.pickDocument())
          .thenAnswer((_) async => const Left(tFailure));

      // act
      final result = await usecase();

      // assert
      expect(result, const Left(tFailure));
      verify(() => mockRepository.pickDocument()).called(1);
    });
  });
}
