// test/features/file_discovery/presentation/notifiers/document_list_notifier_cache_test.dart

import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/file_discovery/domain/entities/document.dart';
import 'package:myapp/features/file_discovery/domain/repositories/document_repository.dart';
import 'package:myapp/features/file_discovery/presentation/notifiers/document_list_notifier.dart';
import 'package:myapp/features/file_discovery/presentation/providers/file_discovery_providers.dart';

class CountingRepo implements DocumentRepository {
  int scans = 0;

  final List<Document> toReturn;
  CountingRepo(this.toReturn);

  @override
  Future<Either<Failure, Unit>> addToRecent(Document document) async => Right(unit);

  @override
  Future<Either<Failure, List<Document>>> getRecentDocuments() async => Right(const []);

  @override
  Future<Either<Failure, Document>> pickDocument() async => Left(FileSystemFailure('not used'));

  @override
  Future<Either<Failure, List<Document>>> scanDocuments() async {
    scans += 1;
    return Right(toReturn);
  }
}

void main() {
  group('DocumentListNotifier caching (TDD)', () {
    test('should not rescan within TTL window (cache hit)', () async {
      final docs = [
        Document(
          id: '1',
          name: 'a.pdf',
          path: '/a.pdf',
          type: DocumentType.pdf,
          sizeInBytes: 10,
          dateModified: DateTime(2024, 1, 1),
        ),
      ];
      final repo = CountingRepo(docs);
      final container = ProviderContainer(overrides: [
        documentRepositoryProvider.overrideWith((ref) => repo),
      ]);

      addTearDown(container.dispose);

      // First build triggers scan
      final first = await container.read(documentListProvider.future);
      expect(first, docs);
      expect(repo.scans, 1);

      // Second read should use cache (expected after TTL implementation)
      final second = await container.read(documentListProvider.future);
      expect(second, docs);
      // Fails now; should remain 1 when cache is in place
      expect(repo.scans, 1);
    });

    test('refresh() bypasses cache and forces rescan', () async {
      final docs = [
        Document(
          id: '1',
          name: 'b.pdf',
          path: '/b.pdf',
          type: DocumentType.pdf,
          sizeInBytes: 10,
          dateModified: DateTime(2024, 1, 1),
        ),
      ];
      final repo = CountingRepo(docs);
      final container = ProviderContainer(overrides: [
        documentRepositoryProvider.overrideWith((ref) => repo),
      ]);

      addTearDown(container.dispose);

      await container.read(documentListProvider.future);
      expect(repo.scans, 1);

      await container.read(documentListProvider.notifier).refresh();
      // After refresh, scan count should increase
      expect(repo.scans, 2);
    });
  });
}

