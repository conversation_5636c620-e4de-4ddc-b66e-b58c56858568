// test/features/file_discovery/presentation/notifiers/document_list_notifier_filter_test.dart

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:myapp/features/file_discovery/domain/entities/document.dart';
import 'package:myapp/features/file_discovery/presentation/notifiers/document_list_notifier.dart';

void main() {
  group('DocumentListNotifier.filterByType', () {
    test('returns correct subsets per DocumentType', () async {
      final container = ProviderContainer();
      addTearDown(container.dispose);

      final notifier = container.read(documentListProvider.notifier);
      final docs = [
        Document(
          id: '1',
          name: 'a.pdf',
          path: '/a.pdf',
          type: DocumentType.pdf,
          sizeInBytes: 1,
          dateModified: DateTime(2024, 1, 1),
        ),
        Document(
          id: '2',
          name: 'b.docx',
          path: '/b.docx',
          type: DocumentType.docx,
          sizeInBytes: 2,
          dateModified: DateTime(2024, 1, 2),
        ),
      ];

      notifier.state = AsyncData(docs);

      expect(notifier.filterByType(DocumentType.unknown), docs);
      expect(
        notifier.filterByType(DocumentType.pdf).map((d) => d.id).toList(),
        ['1'],
      );
      expect(
        notifier.filterByType(DocumentType.docx).map((d) => d.id).toList(),
        ['2'],
      );
    });
  });
}

