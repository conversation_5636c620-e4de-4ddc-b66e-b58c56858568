// test/features/file_discovery/presentation/widgets/document_list_item_test.dart

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:myapp/features/file_discovery/domain/entities/document.dart';
import 'package:myapp/features/file_discovery/presentation/widgets/document_list_item.dart';

void main() {
  group('DocumentListItem', () {
    final testDocument = Document(
      id: '1',
      name: 'test_document.pdf',
      path: '/storage/test_document.pdf',
      type: DocumentType.pdf,
      sizeInBytes: 1048576, // 1MB
      dateModified: DateTime(2024, 1, 15),
    );

    testWidgets('displays document name', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DocumentListItem(document: testDocument, onTap: () {}),
          ),
        ),
      );

      expect(find.text('test_document.pdf'), findsOneWidget);
    });

    testWidgets('displays PDF icon for PDF documents', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DocumentListItem(document: testDocument, onTap: () {}),
          ),
        ),
      );

      expect(find.byIcon(Icons.picture_as_pdf), findsOneWidget);
    });

    testWidgets('displays DOCX icon for DOCX documents', (tester) async {
      final docxDocument = testDocument.copyWith(
        type: DocumentType.docx,
        name: 'document.docx',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DocumentListItem(document: docxDocument, onTap: () {}),
          ),
        ),
      );

      expect(find.byIcon(Icons.description), findsOneWidget);
    });

    testWidgets('displays XLSX icon for XLSX documents', (tester) async {
      final xlsxDocument = testDocument.copyWith(
        type: DocumentType.xlsx,
        name: 'spreadsheet.xlsx',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DocumentListItem(document: xlsxDocument, onTap: () {}),
          ),
        ),
      );

      expect(find.byIcon(Icons.table_chart), findsOneWidget);
    });

    testWidgets('displays PPTX icon for PPTX documents', (tester) async {
      final pptxDocument = testDocument.copyWith(
        type: DocumentType.pptx,
        name: 'presentation.pptx',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DocumentListItem(document: pptxDocument, onTap: () {}),
          ),
        ),
      );

      expect(find.byIcon(Icons.slideshow), findsOneWidget);
    });

    testWidgets('displays file type in subtitle', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DocumentListItem(document: testDocument, onTap: () {}),
          ),
        ),
      );

      expect(find.text('PDF', findRichText: true), findsOneWidget);
    });

    testWidgets('displays file size in subtitle', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DocumentListItem(document: testDocument, onTap: () {}),
          ),
        ),
      );

      // File size should be formatted as "1.0 MB"
      expect(find.textContaining('1.0 MB', findRichText: true), findsOneWidget);
    });

    testWidgets('calls onTap when tapped', (tester) async {
      var tapped = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DocumentListItem(
              document: testDocument,
              onTap: () => tapped = true,
            ),
          ),
        ),
      );

      await tester.tap(find.byType(ListTile));
      expect(tapped, true);
    });

    testWidgets('displays chevron right icon', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DocumentListItem(document: testDocument, onTap: () {}),
          ),
        ),
      );

      expect(find.byIcon(Icons.chevron_right), findsOneWidget);
    });

    testWidgets('shows more actions button', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DocumentListItem(document: testDocument, onTap: () {}),
          ),
        ),
      );

      expect(find.byIcon(Icons.more_vert), findsOneWidget);
    });
  });
}
