// test/features/file_discovery/presentation/widgets/file_type_tabs_test.dart

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:myapp/features/file_discovery/domain/entities/document.dart';
import 'package:myapp/features/file_discovery/presentation/widgets/file_type_tabs.dart';

void main() {
  group('FileTypeTabs', () {
    testWidgets('displays all 5 tabs', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: FileTypeTabs(
              selectedType: DocumentType.unknown,
              onTypeSelected: (_) {},
            ),
          ),
        ),
      );

      expect(find.text('ALL'), findsOneWidget);
      expect(find.text('PDF'), findsOneWidget);
      expect(find.text('WORD'), findsOneWidget);
      expect(find.text('EXCEL'), findsOneWidget);
      expect(find.text('PPT'), findsOneWidget);
    });

    testWidgets('marks ALL tab as selected by default', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: FileTypeTabs(
              selectedType: DocumentType.unknown,
              onTypeSelected: (_) {},
            ),
          ),
        ),
      );

      final allChip = tester.widget<FilterChip>(
        find.ancestor(
          of: find.text('ALL'),
          matching: find.byType(FilterChip),
        ),
      );

      expect(allChip.selected, true);
    });

    testWidgets('marks PDF tab as selected when PDF type is selected', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: FileTypeTabs(
              selectedType: DocumentType.pdf,
              onTypeSelected: (_) {},
            ),
          ),
        ),
      );

      final pdfChip = tester.widget<FilterChip>(
        find.ancestor(
          of: find.text('PDF'),
          matching: find.byType(FilterChip),
        ),
      );

      expect(pdfChip.selected, true);
    });

    testWidgets('marks WORD tab as selected when DOCX type is selected', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: FileTypeTabs(
              selectedType: DocumentType.docx,
              onTypeSelected: (_) {},
            ),
          ),
        ),
      );

      final wordChip = tester.widget<FilterChip>(
        find.ancestor(
          of: find.text('WORD'),
          matching: find.byType(FilterChip),
        ),
      );

      expect(wordChip.selected, true);
    });

    testWidgets('calls onTypeSelected when tab is tapped', (tester) async {
      DocumentType? selectedType;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: FileTypeTabs(
              selectedType: DocumentType.unknown,
              onTypeSelected: (type) => selectedType = type,
            ),
          ),
        ),
      );

      await tester.tap(find.text('PDF'));
      await tester.pumpAndSettle();

      expect(selectedType, DocumentType.pdf);
    });

    testWidgets('calls onTypeSelected with correct type for each tab', (tester) async {
      DocumentType? selectedType;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: FileTypeTabs(
              selectedType: DocumentType.unknown,
              onTypeSelected: (type) => selectedType = type,
            ),
          ),
        ),
      );

      // Test ALL tab
      await tester.tap(find.text('ALL'));
      await tester.pumpAndSettle();
      expect(selectedType, DocumentType.unknown);

      // Test PDF tab
      await tester.tap(find.text('PDF'));
      await tester.pumpAndSettle();
      expect(selectedType, DocumentType.pdf);

      // Test WORD tab
      await tester.tap(find.text('WORD'));
      await tester.pumpAndSettle();
      expect(selectedType, DocumentType.docx);

      // Test EXCEL tab
      await tester.tap(find.text('EXCEL'));
      await tester.pumpAndSettle();
      expect(selectedType, DocumentType.xlsx);

      // Test PPT tab
      await tester.tap(find.text('PPT'));
      await tester.pumpAndSettle();
      expect(selectedType, DocumentType.pptx);
    });

    testWidgets('renders in horizontal scrollable list', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: FileTypeTabs(
              selectedType: DocumentType.unknown,
              onTypeSelected: (_) {},
            ),
          ),
        ),
      );

      final listView = find.byType(ListView);
      expect(listView, findsOneWidget);

      final listViewWidget = tester.widget<ListView>(listView);
      expect(listViewWidget.scrollDirection, Axis.horizontal);
    });
  });
}
