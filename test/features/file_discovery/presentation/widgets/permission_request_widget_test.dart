// test/features/file_discovery/presentation/widgets/permission_request_widget_test.dart

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:myapp/features/file_discovery/presentation/widgets/permission_request_widget.dart';

void main() {
  group('PermissionRequestWidget', () {
    testWidgets('displays illustration icon', (tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: PermissionRequestWidget(
                onPermissionRequested: () {},
              ),
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.assignment_ind), findsOneWidget);
    });

    testWidgets('displays title text', (tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: PermissionRequestWidget(
                onPermissionRequested: () {},
              ),
            ),
          ),
        ),
      );

      expect(
        find.text(
          'To read and edit your files, please allow All PDF Reader to access all your files.',
        ),
        findsOneWidget,
      );
    });

    testWidgets('displays description text', (tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: PermissionRequestWidget(
                onPermissionRequested: () {},
              ),
            ),
          ),
        ),
      );

      expect(
        find.text(
          'Granting access lets us scan and sync your documents while keeping everything secure on your device.',
        ),
        findsOneWidget,
      );
    });

    testWidgets('displays Allow button', (tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: PermissionRequestWidget(
                onPermissionRequested: () {},
              ),
            ),
          ),
        ),
      );

      expect(find.text('Allow'), findsOneWidget);
      expect(
        find.widgetWithText(FilledButton, 'Allow'),
        findsOneWidget,
      );
    });

    testWidgets('calls onPermissionRequested when Allow is tapped', (tester) async {
      var permissionRequested = false;

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: PermissionRequestWidget(
                onPermissionRequested: () => permissionRequested = true,
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.text('Allow'));
      await tester.pumpAndSettle();

      expect(permissionRequested, true);
    });

    testWidgets('widgets are properly aligned and spaced', (tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: PermissionRequestWidget(
                onPermissionRequested: () {},
              ),
            ),
          ),
        ),
      );

      // Check that main container exists
      expect(find.byType(Container), findsWidgets);
      
      // Check that Column with centered alignment exists
      final column = tester.widget<Column>(find.byType(Column).first);
      expect(column.mainAxisAlignment, MainAxisAlignment.center);
    });
  });
}
