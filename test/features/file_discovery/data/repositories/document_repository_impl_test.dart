// test/features/file_discovery/data/repositories/document_repository_impl_test.dart

import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:myapp/features/file_discovery/data/datasources/file_system_datasource_impl.dart';
import 'package:myapp/features/file_discovery/data/models/document_model.dart';
import 'package:myapp/features/file_discovery/data/repositories/document_repository_impl.dart';
import 'package:myapp/features/file_discovery/domain/entities/document.dart';

class MockFileSystemDataSource extends Mock implements FileSystemDataSource {}

void main() {
  late DocumentRepositoryImpl repository;
  late MockFileSystemDataSource mockDataSource;

  setUp(() {
    mockDataSource = MockFileSystemDataSource();
    repository = DocumentRepositoryImpl(mockDataSource);
  });

  setUpAll(() {
    // Register fallback values for mocktail
    registerFallbackValue(DocumentModel(
      id: '1',
      name: 'test.pdf',
      path: '/test.pdf',
      type: 'pdf',
      sizeInBytes: 1024,
      dateModifiedMillis: DateTime(2024, 1, 1).millisecondsSinceEpoch,
    ));
  });

  group('scanDocuments', () {
    final tDocumentModels = [
      DocumentModel(
        id: '1',
        name: 'test.pdf',
        path: '/storage/test.pdf',
        type: 'pdf',
        sizeInBytes: 1024,
        dateModifiedMillis: DateTime(2024, 1, 1).millisecondsSinceEpoch,
      ),
      DocumentModel(
        id: '2',
        name: 'document.docx',
        path: '/storage/document.docx',
        type: 'docx',
        sizeInBytes: 2048,
        dateModifiedMillis: DateTime(2024, 1, 2).millisecondsSinceEpoch,
      ),
    ];

    test('should return list of documents when datasource succeeds', () async {
      // arrange
      when(() => mockDataSource.scanDocuments())
          .thenAnswer((_) async => tDocumentModels);

      // act
      final result = await repository.scanDocuments();

      // assert
      expect(result.isRight(), true);
      result.fold(
        (failure) => fail('Should not return failure'),
        (documents) {
          expect(documents.length, 2);
          expect(documents[0].name, 'test.pdf');
          expect(documents[0].type, DocumentType.pdf);
          expect(documents[1].name, 'document.docx');
          expect(documents[1].type, DocumentType.docx);
        },
      );
      verify(() => mockDataSource.scanDocuments()).called(1);
    });

    test('should return empty list when no documents found', () async {
      // arrange
      when(() => mockDataSource.scanDocuments())
          .thenAnswer((_) async => []);

      // act
      final result = await repository.scanDocuments();

      // assert
      expect(result.isRight(), true);
      result.fold(
        (failure) => fail('Should not return failure'),
        (documents) => expect(documents, isEmpty),
      );
    });

    test('should return FileSystemFailure when datasource throws exception', () async {
      // arrange
      when(() => mockDataSource.scanDocuments())
          .thenThrow(Exception('Scan error'));

      // act
      final result = await repository.scanDocuments();

      // assert
      expect(result.isLeft(), true);
      result.fold(
        (failure) => expect(failure.message, contains('Failed to scan documents')),
        (documents) => fail('Should not return documents'),
      );
    });
  });

  group('pickDocument', () {
    final tDocumentModel = DocumentModel(
      id: '1',
      name: 'picked.pdf',
      path: '/storage/picked.pdf',
      type: 'pdf',
      sizeInBytes: 1024,
      dateModifiedMillis: DateTime(2024, 1, 1).millisecondsSinceEpoch,
    );

    test('should return document when user picks a file', () async {
      // arrange
      when(() => mockDataSource.pickDocument())
          .thenAnswer((_) async => tDocumentModel);

      // act
      final result = await repository.pickDocument();

      // assert
      expect(result.isRight(), true);
      result.fold(
        (failure) => fail('Should not return failure'),
        (document) {
          expect(document.name, 'picked.pdf');
          expect(document.type, DocumentType.pdf);
        },
      );
      verify(() => mockDataSource.pickDocument()).called(1);
    });

    test('should return FileSystemFailure when user cancels', () async {
      // arrange
      when(() => mockDataSource.pickDocument())
          .thenAnswer((_) async => null);

      // act
      final result = await repository.pickDocument();

      // assert
      expect(result.isLeft(), true);
      result.fold(
        (failure) => expect(failure.message, 'No document selected'),
        (document) => fail('Should not return document'),
      );
    });

    test('should return FileSystemFailure when datasource throws exception', () async {
      // arrange
      when(() => mockDataSource.pickDocument())
          .thenThrow(Exception('Picker error'));

      // act
      final result = await repository.pickDocument();

      // assert
      expect(result.isLeft(), true);
      result.fold(
        (failure) => expect(failure.message, contains('Failed to pick document')),
        (document) => fail('Should not return document'),
      );
    });
  });

  group('getRecentDocuments', () {
    final tDocumentModels = [
      DocumentModel(
        id: '1',
        name: 'recent1.pdf',
        path: '/storage/recent1.pdf',
        type: 'pdf',
        sizeInBytes: 1024,
        dateModifiedMillis: DateTime(2024, 1, 2).millisecondsSinceEpoch,
      ),
    ];

    test('should return list of recent documents', () async {
      // arrange
      when(() => mockDataSource.getRecentDocuments())
          .thenAnswer((_) async => tDocumentModels);

      // act
      final result = await repository.getRecentDocuments();

      // assert
      expect(result.isRight(), true);
      result.fold(
        (failure) => fail('Should not return failure'),
        (documents) {
          expect(documents.length, 1);
          expect(documents[0].name, 'recent1.pdf');
        },
      );
      verify(() => mockDataSource.getRecentDocuments()).called(1);
    });

    test('should return empty list when no recent documents', () async {
      // arrange
      when(() => mockDataSource.getRecentDocuments())
          .thenAnswer((_) async => []);

      // act
      final result = await repository.getRecentDocuments();

      // assert
      expect(result.isRight(), true);
      result.fold(
        (failure) => fail('Should not return failure'),
        (documents) => expect(documents, isEmpty),
      );
    });

    test('should return FileSystemFailure when datasource throws exception', () async {
      // arrange
      when(() => mockDataSource.getRecentDocuments())
          .thenThrow(Exception('Storage error'));

      // act
      final result = await repository.getRecentDocuments();

      // assert
      expect(result.isLeft(), true);
      result.fold(
        (failure) => expect(failure.message, contains('Failed to get recent documents')),
        (documents) => fail('Should not return documents'),
      );
    });
  });

  group('addToRecent', () {
    final tDocument = Document(
      id: '1',
      name: 'test.pdf',
      path: '/storage/test.pdf',
      type: DocumentType.pdf,
      sizeInBytes: 1024,
      dateModified: DateTime(2024, 1, 1),
    );

    test('should add document to recent list successfully', () async {
      // arrange
      when(() => mockDataSource.addToRecent(any()))
          .thenAnswer((_) async => Future.value());

      // act
      final result = await repository.addToRecent(tDocument);

      // assert
      expect(result.isRight(), true);
      result.fold(
        (failure) => fail('Should not return failure'),
        (unit) => expect(unit, equals(unit)),
      );
      verify(() => mockDataSource.addToRecent(any())).called(1);
    });

    test('should return FileSystemFailure when datasource throws exception', () async {
      // arrange
      when(() => mockDataSource.addToRecent(any()))
          .thenThrow(Exception('Storage error'));

      // act
      final result = await repository.addToRecent(tDocument);

      // assert
      expect(result.isLeft(), true);
      result.fold(
        (failure) => expect(failure.message, contains('Failed to add document to recent')),
        (unit) => fail('Should not return unit'),
      );
    });
  });
}
