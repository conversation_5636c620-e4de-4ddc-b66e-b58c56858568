// test/features/file_discovery/data/utils/file_scanner_test.dart

import 'package:flutter_test/flutter_test.dart';
import 'package:myapp/features/file_discovery/data/utils/file_scanner.dart';

// TDD: Spec for a pure scanning utility. Implementation to follow.
// The utility should expose a pure function that accepts a list of
// root directory paths and returns serializable document maps.

void main() {
  group('file_scanner.scanPaths', () {
    test('filters only supported extensions and maps metadata', () async {
      // Given roots with mixed files (the implementation will abstract FS reads)
      final roots = ['/tmp/fake-root'];

      // When
      final results = await scanPaths(roots);

      // Then
      // Expect maps to contain required keys and only supported extensions
      for (final m in results) {
        expect(m, contains('id'));
        expect(m, contains('name'));
        expect(m, contains('path'));
        expect(m, contains('type'));
        expect(m, contains('sizeInBytes'));
        expect(m, contains('dateModifiedMillis'));
        expect(
          ['pdf', 'docx', 'xlsx', 'pptx', 'unknown'],
          contains((m['type'] as String).toLowerCase()),
        );
      }
    });

    test('skips hidden/system directories and tolerates permission errors', () async {
      final roots = ['/storage/emulated/0/.hidden', '/storage/emulated/0/secure'];

      final results = await scanPaths(roots);

      // No throw; returns zero or fewer entries compared to naive traversal
      expect(results, isA<List<Map<String, dynamic>>>());
    });
  });
}

