// test/features/file_info/data/repositories/file_metadata_repository_impl_test.dart

import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/file_info/data/datasources/file_system_metadata_datasource.dart';
import 'package:myapp/features/file_info/data/models/file_metadata_model.dart';
import 'package:myapp/features/file_info/data/repositories/file_metadata_repository_impl.dart';
import 'package:myapp/features/file_info/domain/entities/file_metadata.dart';

class MockFileSystemMetadataDataSource extends Mock
    implements FileSystemMetadataDataSource {}

void main() {
  late FileMetadataRepositoryImpl repository;
  late MockFileSystemMetadataDataSource mockDataSource;

  setUp(() {
    mockDataSource = MockFileSystemMetadataDataSource();
    repository = FileMetadataRepositoryImpl(mockDataSource);
  });

  group('FileMetadataRepositoryImpl', () {
    const tPath = '/storage/test.pdf';
    final tMetadataModel = FileMetadataModel(
      path: tPath,
      name: 'test.pdf',
      sizeBytes: 1024,
      createdAt: DateTime(2024, 1, 1),
      modifiedAt: DateTime(2024, 1, 2),
      mime: 'application/pdf',
      author: null,
      isEncrypted: false,
    );

    test('should return FileMetadata when datasource succeeds', () async {
      // arrange
      when(() => mockDataSource.getMetadata(tPath))
          .thenAnswer((_) async => tMetadataModel);

      // act
      final result = await repository.getMetadata(tPath);

      // assert
      expect(result.isRight(), true);
      result.fold(
        (failure) => fail('Should not return failure'),
        (metadata) {
          expect(metadata, isA<FileMetadata>());
          expect(metadata.path, tPath);
          expect(metadata.name, 'test.pdf');
          expect(metadata.sizeBytes, 1024);
        },
      );
      verify(() => mockDataSource.getMetadata(tPath)).called(1);
      verifyNoMoreInteractions(mockDataSource);
    });

    test('should return FileNotFoundFailure when file does not exist',
        () async {
      // arrange
      when(() => mockDataSource.getMetadata(tPath))
          .thenThrow(Exception('File not found: $tPath'));

      // act
      final result = await repository.getMetadata(tPath);

      // assert
      expect(result.isLeft(), true);
      result.fold(
        (failure) {
          expect(failure, isA<FileNotFoundFailure>());
          expect((failure as FileNotFoundFailure).filePath, tPath);
        },
        (metadata) => fail('Should return failure'),
      );
    });

    test('should return FileSystemFailure on FileSystemException', () async {
      // arrange
      when(() => mockDataSource.getMetadata(tPath))
          .thenThrow(const FileSystemException('Permission denied'));

      // act
      final result = await repository.getMetadata(tPath);

      // assert
      expect(result.isLeft(), true);
      result.fold(
        (failure) {
          expect(failure, isA<FileSystemFailure>());
        },
        (metadata) => fail('Should return failure'),
      );
    });

    test('should return MetadataReadFailure on generic exception', () async {
      // arrange
      when(() => mockDataSource.getMetadata(tPath))
          .thenThrow(Exception('Failed to read metadata'));

      // act
      final result = await repository.getMetadata(tPath);

      // assert
      expect(result.isLeft(), true);
      result.fold(
        (failure) {
          expect(failure, isA<MetadataReadFailure>());
        },
        (metadata) => fail('Should return failure'),
      );
    });
  });
}
