// test/features/file_info/domain/usecases/get_file_metadata_test.dart

import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/file_info/domain/entities/file_metadata.dart';
import 'package:myapp/features/file_info/domain/repositories/file_metadata_repository.dart';
import 'package:myapp/features/file_info/domain/usecases/get_file_metadata.dart';

class MockFileMetadataRepository extends Mock
    implements FileMetadataRepository {}

void main() {
  late GetFileMetadata usecase;
  late MockFileMetadataRepository mockRepository;

  setUp(() {
    mockRepository = MockFileMetadataRepository();
    usecase = GetFileMetadata(mockRepository);
  });

  group('GetFileMetadata', () {
    const tPath = '/storage/test.pdf';
    final tMetadata = FileMetadata(
      path: tPath,
      name: 'test.pdf',
      sizeBytes: 1024,
      createdAt: DateTime(2024, 1, 1),
      modifiedAt: DateTime(2024, 1, 2),
      mime: 'application/pdf',
      author: null,
      isEncrypted: false,
    );

    test('should get file metadata from repository', () async {
      // arrange
      when(() => mockRepository.getMetadata(tPath))
          .thenAnswer((_) async => Right(tMetadata));

      // act
      final result = await usecase(tPath);

      // assert
      expect(result, Right(tMetadata));
      verify(() => mockRepository.getMetadata(tPath)).called(1);
      verifyNoMoreInteractions(mockRepository);
    });

    test('should return FileNotFoundFailure when file does not exist',
        () async {
      // arrange
      final tFailure = FileNotFoundFailure(tPath);
      when(() => mockRepository.getMetadata(tPath))
          .thenAnswer((_) async => Left(tFailure));

      // act
      final result = await usecase(tPath);

      // assert
      expect(result, Left(tFailure));
      verify(() => mockRepository.getMetadata(tPath)).called(1);
    });

    test('should return MetadataReadFailure when metadata read fails',
        () async {
      // arrange
      const tFailure = MetadataReadFailure('Failed to read metadata');
      when(() => mockRepository.getMetadata(tPath))
          .thenAnswer((_) async => const Left(tFailure));

      // act
      final result = await usecase(tPath);

      // assert
      expect(result, const Left(tFailure));
      verify(() => mockRepository.getMetadata(tPath)).called(1);
    });
  });
}
