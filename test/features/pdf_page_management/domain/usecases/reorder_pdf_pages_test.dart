import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:dartz/dartz.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/pdf_page_management/domain/repositories/pdf_page_repository.dart';
import 'package:myapp/features/pdf_page_management/domain/usecases/reorder_pdf_pages.dart';
import 'package:myapp/features/file_discovery/domain/entities/document.dart';
import 'package:myapp/features/pdf_page_management/domain/entities/pdf_operation_result.dart';
import 'package:myapp/features/pdf_page_management/domain/entities/page_selection.dart';

class MockPdfPageRepository extends Mock implements PdfPageRepository {}

class FakeDocument extends Fake implements Document {}

class FakePageEditAction extends Fake implements PageEditAction {}

void main() {
  late ReorderPdfPagesUseCase useCase;
  late MockPdfPageRepository mockRepository;

  setUpAll(() {
    registerFallbackValue(FakeDocument());
    registerFallbackValue(<PageEditAction>[]);
  });

  setUp(() {
    mockRepository = MockPdfPageRepository();
    useCase = ReorderPdfPagesUseCase(mockRepository);
  });

  final tDoc = Document(id: 'id1', path: 'path1', name: 'name1', sizeInBytes: 1, type: DocumentType.pdf, dateModified: DateTime.now());
  final tEdits = [PageEditAction(pageIndex: 0, rotation: 90, isDeleted: false)];
  final tPdfOperationResult = PdfOperationResult(outputPath: 'outputPath', displayName: 'displayName', pageCount: 1, fileSize: 1);

  group('ReorderPdfPagesUseCase', () {
    test('should call the repository to reorder the pages', () async {
      // arrange
      final params = ReorderPdfPagesParams(document: tDoc, edits: tEdits);
      when(() => mockRepository.reorderPages(any(), any())).thenAnswer((_) async => Right(tPdfOperationResult));

      // act
      await useCase(params);

      // assert
      verify(() => mockRepository.reorderPages(tDoc, tEdits)).called(1);
      verifyNoMoreInteractions(mockRepository);
    });

    test('should return a Failure when the repository call fails', () async {
      // arrange
      final params = ReorderPdfPagesParams(document: tDoc, edits: tEdits);
      final failure = const OperationFailure('Failed to reorder pages.');
      when(() => mockRepository.reorderPages(any(), any())).thenAnswer((_) async => Left(failure));

      // act
      final result = await useCase(params);

      // assert
      expect(result, Left(failure));
      verify(() => mockRepository.reorderPages(tDoc, tEdits)).called(1);
      verifyNoMoreInteractions(mockRepository);
    });
  });
}
