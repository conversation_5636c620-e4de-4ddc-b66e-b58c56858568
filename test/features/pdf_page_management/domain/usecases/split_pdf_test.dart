import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:dartz/dartz.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/pdf_page_management/domain/repositories/pdf_page_repository.dart';
import 'package:myapp/features/pdf_page_management/domain/usecases/split_pdf.dart';
import 'package:myapp/features/file_discovery/domain/entities/document.dart';
import 'package:myapp/features/pdf_page_management/domain/entities/pdf_operation_result.dart';

class MockPdfPageRepository extends Mock implements PdfPageRepository {}

class FakeDocument extends Fake implements Document {}

void main() {
  late SplitPdfUseCase useCase;
  late MockPdfPageRepository mockRepository;

  setUpAll(() {
    registerFallbackValue(FakeDocument());
  });

  setUp(() {
    mockRepository = MockPdfPageRepository();
    useCase = SplitPdfUseCase(mockRepository);
  });

  final tDoc = Document(id: 'id1', path: 'path1', name: 'name1', sizeInBytes: 1, type: DocumentType.pdf, dateModified: DateTime.now());
  final tPdfOperationResult = PdfOperationResult(outputPath: 'outputPath', displayName: 'displayName', pageCount: 1, fileSize: 1);

  group('SplitPdfUseCase', () {
    test('should return a FileValidationFailure for an empty page range', () async {
      // arrange
      final params = SplitPdfParams(document: tDoc, pageRange: ' ');

      // act
      final result = await useCase(params);

      // assert
      expect(result, Left(const FileValidationFailure('Page range cannot be empty')));
      verifyZeroInteractions(mockRepository);
    });

    test('should call the repository to split the document', () async {
      // arrange
      final params = SplitPdfParams(document: tDoc, pageRange: '1-2');
      when(() => mockRepository.splitPdf(any(), any())).thenAnswer((_) async => Right(tPdfOperationResult));

      // act
      await useCase(params);

      // assert
      verify(() => mockRepository.splitPdf(tDoc, '1-2')).called(1);
      verifyNoMoreInteractions(mockRepository);
    });

    test('should return a Failure when the repository call fails', () async {
      // arrange
      final params = SplitPdfParams(document: tDoc, pageRange: '1-2');
      final failure = const OperationFailure('Failed to split PDF.');
      when(() => mockRepository.splitPdf(any(), any())).thenAnswer((_) async => Left(failure));

      // act
      final result = await useCase(params);

      // assert
      expect(result, Left(failure));
      verify(() => mockRepository.splitPdf(tDoc, '1-2')).called(1);
      verifyNoMoreInteractions(mockRepository);
    });
  });
}
