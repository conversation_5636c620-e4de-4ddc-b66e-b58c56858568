import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:dartz/dartz.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/pdf_page_management/domain/repositories/pdf_page_repository.dart';
import 'package:myapp/features/pdf_page_management/domain/usecases/merge_pdfs.dart';
import 'package:myapp/features/file_discovery/domain/entities/document.dart';
import 'package:myapp/features/pdf_page_management/domain/entities/pdf_operation_result.dart';

class MockPdfPageRepository extends Mock implements PdfPageRepository {}

void main() {
  late MergePdfsUseCase useCase;
  late MockPdfPageRepository mockRepository;

  setUp(() {
    mockRepository = MockPdfPageRepository();
    useCase = MergePdfsUseCase(mockRepository);
  });

  final tDoc1 = Document(id: 'id1', path: 'path1', name: 'name1', sizeInBytes: 1, type: DocumentType.pdf, dateModified: DateTime.now());
  final tDoc2 = Document(id: 'id2', path: 'path2', name: 'name2', sizeInBytes: 2, type: DocumentType.pdf, dateModified: DateTime.now());
  final tPdfOperationResult = PdfOperationResult(outputPath: 'outputPath', displayName: 'displayName', pageCount: 2, fileSize: 3);

  group('MergePdfsUseCase', () {
    test('should return a FileValidationFailure when less than 2 documents are provided', () async {
      // arrange
      final params = MergePdfsParams(documents: [tDoc1]);

      // act
      final result = await useCase(params);

      // assert
      expect(result, Left(const FileValidationFailure('At least two PDFs are required for merging.')));
      verifyZeroInteractions(mockRepository);
    });

    test('should call the repository to merge the documents when 2 or more are provided', () async {
      // arrange
      final params = MergePdfsParams(documents: [tDoc1, tDoc2]);
      when(() => mockRepository.mergePdfs(any())).thenAnswer((_) async => Right(tPdfOperationResult));

      // act
      await useCase(params);

      // assert
      verify(() => mockRepository.mergePdfs([tDoc1, tDoc2])).called(1);
      verifyNoMoreInteractions(mockRepository);
    });

    test('should return a Failure when the repository call fails', () async {
      // arrange
      final params = MergePdfsParams(documents: [tDoc1, tDoc2]);
      final failure = const OperationFailure('Failed to merge PDFs.');
      when(() => mockRepository.mergePdfs(any())).thenAnswer((_) async => Left(failure));

      // act
      final result = await useCase(params);

      // assert
      expect(result, Left(failure));
      verify(() => mockRepository.mergePdfs([tDoc1, tDoc2])).called(1);
      verifyNoMoreInteractions(mockRepository);
    });
  });
}
