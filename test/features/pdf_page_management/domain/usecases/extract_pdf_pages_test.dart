import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:dartz/dartz.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/pdf_page_management/domain/repositories/pdf_page_repository.dart';
import 'package:myapp/features/pdf_page_management/domain/usecases/extract_pdf_pages.dart';
import 'package:myapp/features/file_discovery/domain/entities/document.dart';
import 'package:myapp/features/pdf_page_management/domain/entities/pdf_operation_result.dart';

class MockPdfPageRepository extends Mock implements PdfPageRepository {}

class FakeDocument extends Fake implements Document {}

void main() {
  late ExtractPdfPagesUseCase useCase;
  late MockPdfPageRepository mockRepository;

  setUpAll(() {
    registerFallbackValue(FakeDocument());
  });

  setUp(() {
    mockRepository = MockPdfPageRepository();
    useCase = ExtractPdfPagesUseCase(mockRepository);
  });

  final tDoc = Document(id: 'id1', path: 'path1', name: 'name1', sizeInBytes: 1, type: DocumentType.pdf, dateModified: DateTime.now());
  final tPageIndices = [0, 2];
  final tPdfOperationResult = PdfOperationResult(outputPath: 'outputPath', displayName: 'displayName', pageCount: 2, fileSize: 1);

  group('ExtractPdfPagesUseCase', () {
    test('should call the repository to extract the pages', () async {
      // arrange
      final params = ExtractPdfPagesParams(document: tDoc, pageIndices: tPageIndices);
      when(() => mockRepository.extractPages(any(), any())).thenAnswer((_) async => Right(tPdfOperationResult));

      // act
      await useCase(params);

      // assert
      verify(() => mockRepository.extractPages(tDoc, tPageIndices)).called(1);
      verifyNoMoreInteractions(mockRepository);
    });

    test('should return a Failure when the repository call fails', () async {
      // arrange
      final params = ExtractPdfPagesParams(document: tDoc, pageIndices: tPageIndices);
      final failure = const OperationFailure('Failed to extract pages.');
      when(() => mockRepository.extractPages(any(), any())).thenAnswer((_) async => Left(failure));

      // act
      final result = await useCase(params);

      // assert
      expect(result, Left(failure));
      verify(() => mockRepository.extractPages(tDoc, tPageIndices)).called(1);
      verifyNoMoreInteractions(mockRepository);
    });
  });
}
