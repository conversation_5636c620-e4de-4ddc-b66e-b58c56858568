import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:myapp/features/pdf_page_management/presentation/widgets/split_range_input.dart';

void main() {
  testWidgets('SplitRangeInput allows valid input', (WidgetTester tester) async {
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: SplitRangeInput(onChanged: (_) {}),
        ),
      ),
    );

    final textField = find.byType(TextField);
    await tester.enterText(textField, '1-3,5');

    expect(find.text('1-3,5'), findsOneWidget);
  });

  testWidgets('SplitRangeInput shows error for invalid input', (WidgetTester tester) async {
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: SplitRangeInput(onChanged: (_) {}),
        ),
      ),
    );

    final textField = find.byType(TextField);
    await tester.enterText(textField, 'abc');
    await tester.pump(); // Rebuild the widget after the validation error

    expect(find.text('Invalid format'), findsOneWidget);
  });
}
