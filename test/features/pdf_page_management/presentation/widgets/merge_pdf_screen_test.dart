import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:myapp/features/pdf_page_management/presentation/screens/merge_pdf_screen.dart';
import 'package:myapp/features/pdf_page_management/presentation/notifiers/merge_pdf_notifier.dart';
import 'package:myapp/features/file_discovery/domain/entities/document.dart';

class FakeMergePdfNotifier extends MergePdfNotifier {
  final Completer<void> _completer = Completer<void>();
  List<Document> initialDocs;

  FakeMergePdfNotifier(this.initialDocs);

  @override
  Future<List<Document>> build() async => initialDocs;

  @override
  Future<void> mergeFiles() async {
    state = const AsyncLoading();
    await _completer.future;
  }

  void completeMerge() {
    _completer.complete();
  }
}

void main() {
  final tDoc1 = Document(id: 'id1', path: 'path1', name: 'name1', sizeInBytes: 1, type: DocumentType.pdf, dateModified: DateTime.now());
  final tDoc2 = Document(id: 'id2', path: 'path2', name: 'name2', sizeInBytes: 2, type: DocumentType.pdf, dateModified: DateTime.now());

  testWidgets('Merge button is disabled when less than 2 files are selected', (WidgetTester tester) async {
    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          mergePdfProvider.overrideWith(() => FakeMergePdfNotifier([])),
        ],
        child: const MaterialApp(home: MergePdfScreen()),
      ),
    );
    await tester.pumpAndSettle();

    final mergeButton = find.byType(ElevatedButton);
    expect(mergeButton, findsOneWidget);
    final button = tester.widget<ElevatedButton>(mergeButton);
    expect(button.onPressed, isNull);
  });

  testWidgets('Merge button is enabled when 2 or more files are selected', (WidgetTester tester) async {
    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          mergePdfProvider.overrideWith(() => FakeMergePdfNotifier([tDoc1, tDoc2])),
        ],
        child: const MaterialApp(home: MergePdfScreen()),
      ),
    );
    await tester.pumpAndSettle();

    final mergeButton = find.byType(ElevatedButton);
    expect(mergeButton, findsOneWidget);
    final button = tester.widget<ElevatedButton>(mergeButton);
    expect(button.onPressed, isNotNull);
  });

  testWidgets('shows progress indicator when merging', (WidgetTester tester) async {
    final fakeNotifier = FakeMergePdfNotifier([tDoc1, tDoc2]);

    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          mergePdfProvider.overrideWith(() => fakeNotifier),
        ],
        child: const MaterialApp(home: MergePdfScreen()),
      ),
    );
    await tester.pumpAndSettle();

    final mergeButton = find.byType(ElevatedButton);
    await tester.tap(mergeButton);
    await tester.pump(); // Start the loading state

    expect(find.byType(CircularProgressIndicator), findsOneWidget);

    fakeNotifier.completeMerge();
  });
}
