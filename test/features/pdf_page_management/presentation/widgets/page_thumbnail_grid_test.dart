import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:myapp/features/pdf_page_management/presentation/widgets/page_thumbnail_grid.dart';

void main() {
  testWidgets('PageThumbnailGrid displays thumbnails', (WidgetTester tester) async {
    await tester.pumpWidget(
      const MaterialApp(
        home: Scaffold(
          body: PageThumbnailGrid(
            pageCount: 3,
            thumbnailBuilder: _thumbnailBuilder,
          ),
        ),
      ),
    );

    expect(find.byType(Image), findsNWidgets(3));
  });
}

Widget _thumbnailBuilder(BuildContext context, int index) {
  return Image.memory(
    // Dummy image data
    Uri.parse(
            'data:image/gif;base64,R0lGODlhAQABAIAAAAUEBAAAACwAAAAAAQABAAACAkQBADs=')
        .data!
        .contentAsBytes(),
  );
}
