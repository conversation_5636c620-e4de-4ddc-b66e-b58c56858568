import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:myapp/features/pdf_page_management/presentation/screens/page_management_screen.dart';
import 'package:myapp/features/pdf_page_management/presentation/widgets/page_thumbnail_grid.dart';
import 'package:myapp/features/pdf_page_management/presentation/notifiers/page_management_notifier.dart';
import 'package:myapp/features/pdf_page_management/domain/entities/page_selection.dart';

class FakePageManagementNotifier extends PageManagementNotifier {
  FakePageManagementNotifier(this.initialState);
  final List<PageEditAction> initialState;
  @override
  List<PageEditAction> build() => initialState;
}

void main() {
  testWidgets('PageManagementScreen displays PageThumbnailGrid', (WidgetTester tester) async {
    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          pageManagementProvider.overrideWith(() => FakePageManagementNotifier([])),
        ],
        child: const MaterialApp(home: PageManagementScreen()),
      ),
    );

    expect(find.byType(PageThumbnailGrid), findsOneWidget);
  });
}
