import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:myapp/features/pdf_page_management/presentation/screens/extract_pdf_screen.dart';
import 'package:myapp/features/pdf_page_management/presentation/widgets/page_thumbnail_grid.dart';
import 'package:myapp/features/pdf_page_management/presentation/notifiers/extract_pdf_notifier.dart';

class FakeExtractPdfNotifier extends ExtractPdfNotifier {
  FakeExtractPdfNotifier(this.initialState);
  final List<int> initialState;
  @override
  List<int> build() => initialState;
}

void main() {
  testWidgets('ExtractPdfScreen displays PageThumbnailGrid', (WidgetTester tester) async {
    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          extractPdfProvider.overrideWith(() => FakeExtractPdfNotifier([])),
        ],
        child: const MaterialApp(home: ExtractPdfScreen()),
      ),
    );

    expect(find.byType(PageThumbnailGrid), findsOneWidget);
  });
}
