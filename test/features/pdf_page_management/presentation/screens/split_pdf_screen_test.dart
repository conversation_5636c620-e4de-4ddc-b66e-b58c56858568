import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:myapp/features/pdf_page_management/presentation/screens/split_pdf_screen.dart';
import 'package:myapp/features/pdf_page_management/presentation/notifiers/split_pdf_notifier.dart';

class FakeSplitPdfNotifier extends SplitPdfNotifier {
  FakeSplitPdfNotifier(this.initialState);
  final String initialState;
  @override
  String build() => initialState;
}

void main() {
  testWidgets('Split button is disabled for invalid page range', (WidgetTester tester) async {
    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          splitPdfProvider.overrideWith(() => FakeSplitPdfNotifier('')),
        ],
        child: const MaterialApp(home: SplitPdfScreen()),
      ),
    );

    final splitButton = find.widgetWithText(ElevatedButton, 'Split');
    final button = tester.widget<ElevatedButton>(splitButton);
    expect(button.onPressed, isNull);
  });
}
