import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:path_provider_platform_interface/path_provider_platform_interface.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';
import 'package:myapp/features/pdf_page_management/data/datasources/syncfusion_pdf_page_datasource.dart';
import 'package:myapp/features/file_discovery/domain/entities/document.dart';

// Mock PathProvider for tests
class MockPathProviderPlatform extends Fake
    with MockPlatformInterfaceMixin
    implements PathProviderPlatform {

  @override
  Future<String?> getApplicationDocumentsPath() async {
    // Use a temporary directory for the test
    final tempDir = await Directory.systemTemp.createTemp('app_docs');
    return tempDir.path;
  }
}


void main() {
  late SyncfusionPdfPageDatasource datasource;
  
  setUpAll(() async {
    PathProviderPlatform.instance = MockPathProviderPlatform();
  });

  setUp(() {
    datasource = SyncfusionPdfPageDatasource();
  });

  group('mergePdfs', () {
    test('should create a new PDF file containing the merged documents', () async {
      // arrange
      final samplePdfPath = 'test_assets/sample.pdf';
      final doc1 = Document(id: 'id1', path: samplePdfPath, name: 'sample.pdf', sizeInBytes: 1, type: DocumentType.pdf, dateModified: DateTime.now());
      final doc2 = Document(id: 'id2', path: samplePdfPath, name: 'sample.pdf', sizeInBytes: 1, type: DocumentType.pdf, dateModified: DateTime.now());
      final documents = [doc1, doc2];

      // act
      final result = await datasource.mergePdfs(documents);

      // assert
      final mergedFile = File(result.outputPath);
      expect(await mergedFile.exists(), isTrue);
      expect(result.displayName, startsWith('Merged_'));
      // With a real PDF, we can now expect a specific page count
      expect(result.pageCount, 2); 
    });
  });
}
