import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:dartz/dartz.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/pdf_page_management/data/datasources/pdf_page_datasource.dart';
import 'package:myapp/features/pdf_page_management/data/repositories/pdf_page_repository_impl.dart';
import 'package:myapp/features/file_discovery/domain/entities/document.dart';
import 'package:myapp/features/pdf_page_management/domain/entities/pdf_operation_result.dart';

class MockPdfPageDatasource extends Mock implements PdfPageDatasource {}

void main() {
  late PdfPageRepositoryImpl repository;
  late MockPdfPageDatasource mockDatasource;

  setUp(() {
    mockDatasource = MockPdfPageDatasource();
    repository = PdfPageRepositoryImpl(datasource: mockDatasource);
  });

  final tDoc1 = Document(id: 'id1', path: 'path1', name: 'name1', sizeInBytes: 1, type: DocumentType.pdf, dateModified: DateTime.now());
  final tDoc2 = Document(id: 'id2', path: 'path2', name: 'name2', sizeInBytes: 2, type: DocumentType.pdf, dateModified: DateTime.now());
  final tDocumentList = [tDoc1, tDoc2];
  final tPdfOperationResult = PdfOperationResult(outputPath: 'outputPath', displayName: 'displayName', pageCount: 2, fileSize: 3);

  group('mergePdfs', () {
    test('should call the datasource to merge the documents', () async {
      // arrange
      when(() => mockDatasource.mergePdfs(any())).thenAnswer((_) async => tPdfOperationResult);

      // act
      await repository.mergePdfs(tDocumentList);

      // assert
      verify(() => mockDatasource.mergePdfs(tDocumentList)).called(1);
      verifyNoMoreInteractions(mockDatasource);
    });

    test('should return a PdfOperationResult when the call to the datasource is successful', () async {
      // arrange
      when(() => mockDatasource.mergePdfs(any())).thenAnswer((_) async => tPdfOperationResult);

      // act
      final result = await repository.mergePdfs(tDocumentList);

      // assert
      expect(result, Right(tPdfOperationResult));
    });

    test('should return a OperationFailure when the call to the datasource throws an exception', () async {
      // arrange
      when(() => mockDatasource.mergePdfs(any())).thenThrow(Exception('Something went wrong'));

      // act
      final result = await repository.mergePdfs(tDocumentList);

      // assert
      expect(result, isA<Left<Failure, PdfOperationResult>>());
    });
  });
}
