// test/features/pdf_security/domain/usecases/set_pdf_password_test.dart

import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/pdf_security/domain/repositories/pdf_security_repository.dart';
import 'package:myapp/features/pdf_security/domain/usecases/set_pdf_password.dart';

class MockPdfSecurityRepository extends Mock
    implements PdfSecurityRepository {}

void main() {
  late SetPdfPassword usecase;
  late MockPdfSecurityRepository mockRepository;

  setUp(() {
    mockRepository = MockPdfSecurityRepository();
    usecase = SetPdfPassword(mockRepository);
  });

  group('SetPdfPassword', () {
    const tPath = '/storage/test.pdf';
    const tPassword = 'test123';

    test('should set password on PDF successfully', () async {
      // arrange
      when(() => mockRepository.setPassword(
            path: tPath,
            newPassword: tPassword,
          )).thenAnswer((_) async => const Right(unit));

      // act
      final result = await usecase(path: tPath, newPassword: tPassword);

      // assert
      expect(result, const Right(unit));
      verify(() => mockRepository.setPassword(
            path: tPath,
            newPassword: tPassword,
          )).called(1);
      verifyNoMoreInteractions(mockRepository);
    });

    test('should return FileNotFoundFailure when file does not exist',
        () async {
      // arrange
      final tFailure = FileNotFoundFailure(tPath);
      when(() => mockRepository.setPassword(
            path: tPath,
            newPassword: tPassword,
          )).thenAnswer((_) async => Left(tFailure));

      // act
      final result = await usecase(path: tPath, newPassword: tPassword);

      // assert
      expect(result, Left(tFailure));
    });

    test('should return PdfAlreadyEncryptedFailure when PDF is already encrypted',
        () async {
      // arrange
      const tFailure = PdfAlreadyEncryptedFailure();
      when(() => mockRepository.setPassword(
            path: tPath,
            newPassword: tPassword,
          )).thenAnswer((_) async => const Left(tFailure));

      // act
      final result = await usecase(path: tPath, newPassword: tPassword);

      // assert
      expect(result, const Left(tFailure));
    });

    test('should return PdfEncryptionFailure when encryption fails', () async {
      // arrange
      const tFailure = PdfEncryptionFailure('Encryption failed');
      when(() => mockRepository.setPassword(
            path: tPath,
            newPassword: tPassword,
          )).thenAnswer((_) async => const Left(tFailure));

      // act
      final result = await usecase(path: tPath, newPassword: tPassword);

      // assert
      expect(result, const Left(tFailure));
    });
  });
}
