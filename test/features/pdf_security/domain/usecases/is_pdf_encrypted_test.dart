// test/features/pdf_security/domain/usecases/is_pdf_encrypted_test.dart

import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/pdf_security/domain/repositories/pdf_security_repository.dart';
import 'package:myapp/features/pdf_security/domain/usecases/is_pdf_encrypted.dart';

class MockPdfSecurityRepository extends Mock
    implements PdfSecurityRepository {}

void main() {
  late IsPdfEncrypted usecase;
  late MockPdfSecurityRepository mockRepository;

  setUp(() {
    mockRepository = MockPdfSecurityRepository();
    usecase = IsPdfEncrypted(mockRepository);
  });

  group('IsPdfEncrypted', () {
    const tPath = '/storage/test.pdf';

    test('should return true when PDF is encrypted', () async {
      // arrange
      when(() => mockRepository.isEncrypted(tPath))
          .thenAnswer((_) async => const Right(true));

      // act
      final result = await usecase(tPath);

      // assert
      expect(result, const Right(true));
      verify(() => mockRepository.isEncrypted(tPath)).called(1);
      verifyNoMoreInteractions(mockRepository);
    });

    test('should return false when PDF is not encrypted', () async {
      // arrange
      when(() => mockRepository.isEncrypted(tPath))
          .thenAnswer((_) async => const Right(false));

      // act
      final result = await usecase(tPath);

      // assert
      expect(result, const Right(false));
    });

    test('should return FileNotFoundFailure when file does not exist',
        () async {
      // arrange
      final tFailure = FileNotFoundFailure(tPath);
      when(() => mockRepository.isEncrypted(tPath))
          .thenAnswer((_) async => Left(tFailure));

      // act
      final result = await usecase(tPath);

      // assert
      expect(result, Left(tFailure));
    });

    test('should return PdfEncryptionFailure when check fails', () async {
      // arrange
      const tFailure = PdfEncryptionFailure('Failed to check encryption status');
      when(() => mockRepository.isEncrypted(tPath))
          .thenAnswer((_) async => const Left(tFailure));

      // act
      final result = await usecase(tPath);

      // assert
      expect(result, const Left(tFailure));
    });
  });
}
