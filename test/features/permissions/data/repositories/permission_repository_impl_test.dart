// test/features/permissions/data/repositories/permission_repository_impl_test.dart

import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:myapp/features/permissions/data/datasources/permission_datasource.dart';
import 'package:myapp/features/permissions/data/repositories/permission_repository_impl.dart';

class MockPermissionDataSource extends Mock implements PermissionDataSource {}

void main() {
  late PermissionRepositoryImpl repository;
  late MockPermissionDataSource mockDataSource;

  setUp(() {
    mockDataSource = MockPermissionDataSource();
    repository = PermissionRepositoryImpl(mockDataSource);
  });

  group('checkStoragePermission', () {
    test('should return PermissionStatus.granted when datasource returns granted', () async {
      // arrange
      when(() => mockDataSource.checkStoragePermission())
          .thenAnswer((_) async => PermissionStatus.granted);

      // act
      final result = await repository.checkStoragePermission();

      // assert
      expect(result.isRight(), true);
      result.fold(
        (failure) => fail('Should not return failure'),
        (status) => expect(status, PermissionStatus.granted),
      );
      verify(() => mockDataSource.checkStoragePermission()).called(1);
    });

    test('should return PermissionStatus.denied when datasource returns denied', () async {
      // arrange
      when(() => mockDataSource.checkStoragePermission())
          .thenAnswer((_) async => PermissionStatus.denied);

      // act
      final result = await repository.checkStoragePermission();

      // assert
      expect(result.isRight(), true);
      result.fold(
        (failure) => fail('Should not return failure'),
        (status) => expect(status, PermissionStatus.denied),
      );
      verify(() => mockDataSource.checkStoragePermission()).called(1);
    });

    test('should return PermissionStatus.permanentlyDenied when datasource returns permanentlyDenied', () async {
      // arrange
      when(() => mockDataSource.checkStoragePermission())
          .thenAnswer((_) async => PermissionStatus.permanentlyDenied);

      // act
      final result = await repository.checkStoragePermission();

      // assert
      expect(result.isRight(), true);
      result.fold(
        (failure) => fail('Should not return failure'),
        (status) => expect(status, PermissionStatus.permanentlyDenied),
      );
    });

    test('should return StoragePermissionFailure when datasource throws exception', () async {
      // arrange
      when(() => mockDataSource.checkStoragePermission())
          .thenThrow(Exception('Permission check error'));

      // act
      final result = await repository.checkStoragePermission();

      // assert
      expect(result.isLeft(), true);
      result.fold(
        (failure) => expect(failure.message, contains('Failed to check storage permission')),
        (status) => fail('Should not return status'),
      );
    });
  });

  group('requestStoragePermission', () {
    test('should return PermissionStatus.granted when user grants permission', () async {
      // arrange
      when(() => mockDataSource.requestStoragePermission())
          .thenAnswer((_) async => PermissionStatus.granted);

      // act
      final result = await repository.requestStoragePermission();

      // assert
      expect(result.isRight(), true);
      result.fold(
        (failure) => fail('Should not return failure'),
        (status) => expect(status, PermissionStatus.granted),
      );
      verify(() => mockDataSource.requestStoragePermission()).called(1);
    });

    test('should return PermissionStatus.denied when user denies permission', () async {
      // arrange
      when(() => mockDataSource.requestStoragePermission())
          .thenAnswer((_) async => PermissionStatus.denied);

      // act
      final result = await repository.requestStoragePermission();

      // assert
      expect(result.isRight(), true);
      result.fold(
        (failure) => fail('Should not return failure'),
        (status) => expect(status, PermissionStatus.denied),
      );
      verify(() => mockDataSource.requestStoragePermission()).called(1);
    });

    test('should return PermissionStatus.permanentlyDenied when user permanently denies', () async {
      // arrange
      when(() => mockDataSource.requestStoragePermission())
          .thenAnswer((_) async => PermissionStatus.permanentlyDenied);

      // act
      final result = await repository.requestStoragePermission();

      // assert
      expect(result.isRight(), true);
      result.fold(
        (failure) => fail('Should not return failure'),
        (status) => expect(status, PermissionStatus.permanentlyDenied),
      );
    });

    test('should return StoragePermissionFailure when datasource throws exception', () async {
      // arrange
      when(() => mockDataSource.requestStoragePermission())
          .thenThrow(Exception('Permission request error'));

      // act
      final result = await repository.requestStoragePermission();

      // assert
      expect(result.isLeft(), true);
      result.fold(
        (failure) => expect(failure.message, contains('Failed to request storage permission')),
        (status) => fail('Should not return status'),
      );
    });
  });
}
