// test/features/permissions/domain/usecases/check_storage_permission_test.dart

import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/permissions/domain/repositories/permission_repository.dart';
import 'package:myapp/features/permissions/domain/usecases/check_storage_permission.dart';

class MockPermissionRepository extends Mock implements PermissionRepository {}

void main() {
  late CheckStoragePermission usecase;
  late MockPermissionRepository mockRepository;

  setUp(() {
    mockRepository = MockPermissionRepository();
    usecase = CheckStoragePermission(mockRepository);
  });

  group('CheckStoragePermission', () {
    test('should return true when permission is granted', () async {
      // arrange
      when(() => mockRepository.checkStoragePermission())
          .thenAnswer((_) async => const Right(PermissionStatus.granted));

      // act
      final result = await usecase();

      // assert
      expect(result, const Right(true));
      verify(() => mockRepository.checkStoragePermission()).called(1);
      verifyNoMoreInteractions(mockRepository);
    });

    test('should return false when permission is denied', () async {
      // arrange
      when(() => mockRepository.checkStoragePermission())
          .thenAnswer((_) async => const Right(PermissionStatus.denied));

      // act
      final result = await usecase();

      // assert
      expect(result, const Right(false));
      verify(() => mockRepository.checkStoragePermission()).called(1);
    });

    test('should return false when permission is permanently denied', () async {
      // arrange
      when(() => mockRepository.checkStoragePermission())
          .thenAnswer((_) async => const Right(PermissionStatus.permanentlyDenied));

      // act
      final result = await usecase();

      // assert
      expect(result, const Right(false));
      verify(() => mockRepository.checkStoragePermission()).called(1);
    });

    test('should return false when permission is restricted', () async {
      // arrange
      when(() => mockRepository.checkStoragePermission())
          .thenAnswer((_) async => const Right(PermissionStatus.restricted));

      // act
      final result = await usecase();

      // assert
      expect(result, const Right(false));
      verify(() => mockRepository.checkStoragePermission()).called(1);
    });

    test('should return false when permission is limited', () async {
      // arrange
      when(() => mockRepository.checkStoragePermission())
          .thenAnswer((_) async => const Right(PermissionStatus.limited));

      // act
      final result = await usecase();

      // assert
      expect(result, const Right(false));
      verify(() => mockRepository.checkStoragePermission()).called(1);
    });

    test('should return StoragePermissionFailure when checking fails', () async {
      // arrange
      const tFailure = StoragePermissionFailure('Failed to check permission');
      when(() => mockRepository.checkStoragePermission())
          .thenAnswer((_) async => const Left(tFailure));

      // act
      final result = await usecase();

      // assert
      expect(result, const Left(tFailure));
      verify(() => mockRepository.checkStoragePermission()).called(1);
    });
  });
}
