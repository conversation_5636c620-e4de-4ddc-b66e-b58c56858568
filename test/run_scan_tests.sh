#!/bin/bash

# <PERSON>ript to run all scan-related tests
echo "Running scan-related tests..."

# Run unit tests
echo "Running unit tests..."
flutter test test/features/file_conversion/presentation/notifiers/scan_notifier_finalize_flow_test.dart
NOTIFIER_RESULT=$?

flutter test test/features/file_conversion/presentation/screens/scan_review_screen_test.dart
SCREEN_RESULT=$?

flutter test test/features/file_conversion/data/datasources/pdf_converter_data_source_impl_test.dart
DATASOURCE_RESULT=$?

# Run integration tests
echo "Running integration tests..."
flutter test test/features/file_conversion/integration/scan_flow_integration_test.dart
INTEGRATION_RESULT=$?

# Check results
if [ $NOTIFIER_RESULT -eq 0 ] && [ $SCREEN_RESULT -eq 0 ] && [ $DATASOURCE_RESULT -eq 0 ] && [ $INTEGRATION_RESULT -eq 0 ]; then
    echo "All scan tests passed!"
    exit 0
else
    echo "Some tests failed!"
    exit 1
fi