// test/core/utils/date_formatter_test.dart

import 'package:flutter_test/flutter_test.dart';
import 'package:myapp/core/utils/date_formatter.dart';

void main() {
  group('DateFormatter', () {
    group('formatDateModified', () {
      test('formats today date correctly', () {
        final now = DateTime.now();
        final result = DateFormatter.formatDateModified(now);
        expect(result, startsWith('Today'));
      });

      test('formats yesterday date correctly', () {
        final yesterday = DateTime.now().subtract(const Duration(days: 1));
        final result = DateFormatter.formatDateModified(yesterday);
        expect(result, startsWith('Yesterday'));
      });

      test('formats this week date correctly', () {
        final threeDaysAgo = DateTime.now().subtract(const Duration(days: 3));
        final result = DateFormatter.formatDateModified(threeDaysAgo);
        // Should contain day name (e.g., "<PERSON>", "<PERSON>e")
        expect(result, isNotEmpty);
        expect(result.contains('Today'), false);
        expect(result.contains('Yesterday'), false);
      });

      test('formats older dates with year', () {
        final oldDate = DateTime(2023, 1, 15, 14, 30);
        final result = DateFormatter.formatDateModified(oldDate);
        expect(result, contains('2023'));
      });
    });

    group('formatRelativeDate', () {
      test('formats just now correctly', () {
        final now = DateTime.now();
        final result = DateFormatter.formatRelativeDate(now);
        expect(result, 'Just now');
      });

      test('formats minutes ago correctly', () {
        final fiveMinutesAgo = DateTime.now().subtract(const Duration(minutes: 5));
        final result = DateFormatter.formatRelativeDate(fiveMinutesAgo);
        expect(result, '5 minutes ago');
      });

      test('formats hours ago correctly', () {
        final twoHoursAgo = DateTime.now().subtract(const Duration(hours: 2));
        final result = DateFormatter.formatRelativeDate(twoHoursAgo);
        expect(result, '2 hours ago');
      });

      test('formats yesterday correctly', () {
        final yesterday = DateTime.now().subtract(const Duration(days: 1));
        final result = DateFormatter.formatRelativeDate(yesterday);
        expect(result, 'Yesterday');
      });

      test('formats days ago correctly', () {
        final threeDaysAgo = DateTime.now().subtract(const Duration(days: 3));
        final result = DateFormatter.formatRelativeDate(threeDaysAgo);
        expect(result, '3 days ago');
      });

      test('formats weeks ago correctly', () {
        final twoWeeksAgo = DateTime.now().subtract(const Duration(days: 14));
        final result = DateFormatter.formatRelativeDate(twoWeeksAgo);
        expect(result, '2 weeks ago');
      });

      test('formats months ago correctly', () {
        final twoMonthsAgo = DateTime.now().subtract(const Duration(days: 60));
        final result = DateFormatter.formatRelativeDate(twoMonthsAgo);
        expect(result, '2 months ago');
      });

      test('formats years ago correctly', () {
        final twoYearsAgo = DateTime.now().subtract(const Duration(days: 730));
        final result = DateFormatter.formatRelativeDate(twoYearsAgo);
        expect(result, '2 years ago');
      });
    });

    group('formatCompactDate', () {
      test('formats current year date without year', () {
        final currentYear = DateTime.now();
        final result = DateFormatter.formatCompactDate(currentYear);
        expect(result, isNot(contains(currentYear.year.toString())));
      });

      test('formats old date with year', () {
        final oldDate = DateTime(2023, 1, 15);
        final result = DateFormatter.formatCompactDate(oldDate);
        // Should contain abbreviated year (e.g., "'23" or "23")
        expect(result, isNotEmpty);
      });
    });

    group('isToday', () {
      test('returns true for today', () {
        final now = DateTime.now();
        expect(DateFormatter.isToday(now), true);
      });

      test('returns false for yesterday', () {
        final yesterday = DateTime.now().subtract(const Duration(days: 1));
        expect(DateFormatter.isToday(yesterday), false);
      });

      test('returns false for tomorrow', () {
        final tomorrow = DateTime.now().add(const Duration(days: 1));
        expect(DateFormatter.isToday(tomorrow), false);
      });
    });

    group('isYesterday', () {
      test('returns true for yesterday', () {
        final yesterday = DateTime.now().subtract(const Duration(days: 1));
        expect(DateFormatter.isYesterday(yesterday), true);
      });

      test('returns false for today', () {
        final now = DateTime.now();
        expect(DateFormatter.isYesterday(now), false);
      });

      test('returns false for two days ago', () {
        final twoDaysAgo = DateTime.now().subtract(const Duration(days: 2));
        expect(DateFormatter.isYesterday(twoDaysAgo), false);
      });
    });

    group('isWithinWeek', () {
      test('returns true for dates within last 7 days', () {
        final threeDaysAgo = DateTime.now().subtract(const Duration(days: 3));
        expect(DateFormatter.isWithinWeek(threeDaysAgo), true);
      });

      test('returns false for dates older than 7 days', () {
        final tenDaysAgo = DateTime.now().subtract(const Duration(days: 10));
        expect(DateFormatter.isWithinWeek(tenDaysAgo), false);
      });

      test('returns true for today', () {
        final now = DateTime.now();
        expect(DateFormatter.isWithinWeek(now), true);
      });
    });
  });
}
