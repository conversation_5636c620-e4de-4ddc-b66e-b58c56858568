// test/core/utils/performance_utils_test.dart

import 'dart:async';
import 'package:flutter_test/flutter_test.dart';
import 'package:myapp/core/utils/performance_utils.dart';

void main() {
  group('Debouncer (TDD)', () {
    test('coalesces rapid calls and runs last after delay', () async {
      int calls = 0;
      final d = Debouncer(const Duration(milliseconds: 20));

      d(() => calls++);
      d(() => calls++);
      d(() => calls++);

      await Future<void>.delayed(const Duration(milliseconds: 50));

      expect(calls, 1);
      d.dispose();
    });
  });

  group('Throttler (TDD)', () {
    test('executes immediately then blocks subsequent calls within window', () async {
      int calls = 0;
      final t = Throttler(const Duration(milliseconds: 30));

      t(() => calls++); // executes
      t(() => calls++); // throttled
      await Future<void>.delayed(const Duration(milliseconds: 40));
      t(() => calls++); // executes after window

      expect(calls, 2);
    });
  });

  group('measurePerformance (TDD)', () {
    test('returns wrapped result', () async {
      final result = await measurePerformance<int>('unit', () async {
        await Future<void>.delayed(const Duration(milliseconds: 5));
        return 42;
      });
      expect(result, 42);
    });
  });

  group('computeWithTimeout (TDD)', () {
    test('completes before timeout', () async {
      final result = await computeWithTimeout<int, int>(
        (x) => x + 1,
        1,
        timeout: const Duration(seconds: 1),
      );
      expect(result, 2);
    });

    test('throws on timeout', () async {
      expect(
        () => computeWithTimeout<void, int>(
          (x) async {
            await Future<void>.delayed(const Duration(milliseconds: 100));
            return;
          },
          1,
          timeout: const Duration(milliseconds: 10),
        ),
        throwsA(isA<TimeoutException>()),
      );
    });
  });
}
