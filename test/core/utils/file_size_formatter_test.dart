// test/core/utils/file_size_formatter_test.dart

import 'package:flutter_test/flutter_test.dart';
import 'package:myapp/core/utils/file_size_formatter.dart';

void main() {
  group('FileSizeFormatter', () {
    group('formatFileSize', () {
      test('formats bytes correctly', () {
        expect(FileSizeFormatter.formatFileSize(0), '0 B');
        expect(FileSizeFormatter.formatFileSize(512), '512 B');
        expect(FileSizeFormatter.formatFileSize(1023), '1023 B');
      });

      test('formats kilobytes correctly', () {
        expect(FileSizeFormatter.formatFileSize(1024), '1.0 KB');
        expect(FileSizeFormatter.formatFileSize(1536), '1.5 KB');
        expect(FileSizeFormatter.formatFileSize(10240), '10.0 KB');
      });

      test('formats megabytes correctly', () {
        expect(FileSizeFormatter.formatFileSize(1048576), '1.0 MB');
        expect(FileSizeFormatter.formatFileSize(5242880), '5.0 MB');
        expect(FileSizeFormatter.formatFileSize(10485760), '10.0 MB');
      });

      test('formats gigabytes correctly', () {
        expect(FileSizeFormatter.formatFileSize(1073741824), '1.0 GB');
        expect(FileSizeFormatter.formatFileSize(2147483648), '2.0 GB');
      });

      test('throws ArgumentError for negative bytes', () {
        expect(() => FileSizeFormatter.formatFileSize(-1), throwsArgumentError);
        expect(() => FileSizeFormatter.formatFileSize(-100), throwsArgumentError);
      });
    });

    group('formatFileSizeCompact', () {
      test('formats bytes compactly', () {
        expect(FileSizeFormatter.formatFileSizeCompact(0), '0');
        expect(FileSizeFormatter.formatFileSizeCompact(512), '512');
        expect(FileSizeFormatter.formatFileSizeCompact(1023), '1023');
      });

      test('formats kilobytes compactly', () {
        expect(FileSizeFormatter.formatFileSizeCompact(1024), '1.0K');
        expect(FileSizeFormatter.formatFileSizeCompact(1536), '1.5K');
      });

      test('formats megabytes compactly', () {
        expect(FileSizeFormatter.formatFileSizeCompact(1048576), '1.0M');
        expect(FileSizeFormatter.formatFileSizeCompact(5242880), '5.0M');
      });

      test('formats gigabytes compactly', () {
        expect(FileSizeFormatter.formatFileSizeCompact(1073741824), '1.0G');
      });

      test('throws ArgumentError for negative bytes', () {
        expect(() => FileSizeFormatter.formatFileSizeCompact(-1), throwsArgumentError);
      });
    });

    group('isFileSizeAllowed', () {
      test('returns true for sizes under 50MB', () {
        expect(FileSizeFormatter.isFileSizeAllowed(0), true);
        expect(FileSizeFormatter.isFileSizeAllowed(1024), true);
        expect(FileSizeFormatter.isFileSizeAllowed(10 * 1024 * 1024), true);
        expect(FileSizeFormatter.isFileSizeAllowed(50 * 1024 * 1024), true);
      });

      test('returns false for sizes over 50MB', () {
        expect(FileSizeFormatter.isFileSizeAllowed(50 * 1024 * 1024 + 1), false);
        expect(FileSizeFormatter.isFileSizeAllowed(100 * 1024 * 1024), false);
      });
    });

    group('getFileSizeCategory', () {
      test('categorizes file sizes correctly', () {
        expect(FileSizeFormatter.getFileSizeCategory(512), 'tiny');
        expect(FileSizeFormatter.getFileSizeCategory(1023), 'tiny');
        
        expect(FileSizeFormatter.getFileSizeCategory(1024), 'small');
        expect(FileSizeFormatter.getFileSizeCategory(500 * 1024), 'small');
        
        expect(FileSizeFormatter.getFileSizeCategory(1 * 1024 * 1024), 'medium');
        expect(FileSizeFormatter.getFileSizeCategory(5 * 1024 * 1024), 'medium');
        
        expect(FileSizeFormatter.getFileSizeCategory(10 * 1024 * 1024), 'large');
        expect(FileSizeFormatter.getFileSizeCategory(40 * 1024 * 1024), 'large');
        
        expect(FileSizeFormatter.getFileSizeCategory(50 * 1024 * 1024), 'huge');
        expect(FileSizeFormatter.getFileSizeCategory(100 * 1024 * 1024), 'huge');
      });
    });
  });
}
