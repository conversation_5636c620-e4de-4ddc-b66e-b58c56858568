// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:myapp/main.dart';
import 'package:myapp/features/permissions/presentation/providers/permission_providers.dart';
import 'package:myapp/features/permissions/data/datasources/permission_datasource.dart';
import 'package:permission_handler/permission_handler.dart';

void main() {
  testWidgets('App renders HomeScreen with Documents title', (WidgetTester tester) async {
    // Fake permission data source to avoid platform plugins in tests
    final fakePermissionDataSource = _FakePermissionDataSourceDenied();

    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          permissionDataSourceProvider.overrideWithValue(fakePermissionDataSource),
        ],
        child: const MyApp(),
      ),
    );

    // Allow for splash -> main navigation and warm-up
    await tester.pump(const Duration(milliseconds: 100));
    await tester.pumpAndSettle(const Duration(seconds: 2));

    expect(find.text('Documents'), findsOneWidget, reason: 'Home AppBar should show after splash');
  });
}

class _FakePermissionDataSourceDenied implements PermissionDataSource {
  @override
  Future<PermissionStatus> checkStoragePermission() async => PermissionStatus.denied;

  @override
  Future<PermissionStatus> requestStoragePermission() async => PermissionStatus.denied;
}
