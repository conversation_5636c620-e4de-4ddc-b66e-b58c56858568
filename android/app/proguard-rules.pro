# ProGuard rules for Document Reader MVP
# Keep rules for all dependencies used in the app

# Syncfusion PDF Viewer
-keep class com.syncfusion.** { *; }
-dontwarn com.syncfusion.**



# ProGuard only affects JVM bytecode; Dart/Riverpod/Freezed classes are not processed.
# Keep fields annotated with @SerializedName for plugins that rely on Gson.
-keepclassmembers class ** {
    @com.google.gson.annotations.SerializedName <fields>;
}

# JSON Serializable
-keepclassmembers class ** {
    @com.fasterxml.jackson.annotation.* <methods>;
}
-keep @interface com.fasterxml.jackson.annotation.**

# Microsoft Viewer
-keep class com.microsoft.** { *; }
-dontwarn com.microsoft.**

# File Picker
-keep class com.mr.flutter.plugin.filepicker.** { *; }

# Permission Handler
-keep class com.baseflow.permissionhandler.** { *; }

# Path Provider
-keep class io.flutter.plugins.pathprovider.** { *; }



# Keep all Flutter plugins
-keep class io.flutter.** { *; }
-dontwarn io.flutter.**



# Keep generic signatures
-keepattributes Signature
-keepattributes *Annotation*
-keepattributes EnclosingMethod
-keepattributes InnerClasses

# Dart specific rules
-keep class io.flutter.plugins.** { *; }
-keep class io.flutter.embedding.** { *; }

# Keep classes referenced by reflection
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# Prevent obfuscation of classes that use Parcelable
-keepclassmembers class * implements android.os.Parcelable {
    static ** CREATOR;
}

# Keep enums
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# Native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Intl package
-keep class com.ibm.icu.** { *; }
-dontwarn com.ibm.icu.**


# Application entry point referenced by Android framework via reflection
-keep class com.example.myapp.MainActivity { *; }

