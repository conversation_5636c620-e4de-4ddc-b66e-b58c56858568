// integration_test/app_test.dart

import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:myapp/main.dart' as app;
import 'package:flutter/material.dart';
import 'package:myapp/features/file_discovery/presentation/widgets/document_list_item.dart';


/// Integration tests for Document Reader MVP
///
/// These tests verify end-to-end user flows including:
/// - Permission handling (granted and denied scenarios)
/// - Document listing and filtering
/// - Document viewing
///
/// Note: These tests require a physical device or emulator with:
/// - Android API 23+ (minimum SDK)
/// - Test documents in storage (for permission granted flow)
/// - File picker support (for permission denied flow)
void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('App Launch and Permission Flow', () {
    testWidgets('app launches and displays Documents title', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Verify that the app launches and shows the home screen
      expect(find.text('Documents'), findsOneWidget);
    });

    testWidgets('permission denied shows permission request widget', (tester) async {
      app.main();
      await tester.pumpAndSettle(const Duration(seconds: 2));

      // If permission is denied, should show permission request
      // Note: This test behavior depends on actual device permission state
      // In a denied state, we should see:
      expect(
        find.text('Storage Permission Required'),
        findsWidgets, // May find 0 or 1 depending on permission state
      );
    });
  });

  group('Document Listing and Filtering', () {
    testWidgets('document list displays when permission granted', (tester) async {
      app.main();
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // If permission is granted, should show tabs
      // Note: This test behavior depends on actual device permission state
      expect(
        find.text('ALL'),
        findsWidgets, // May find 0 or 1 depending on permission state
      );
    });

    testWidgets('can switch between document type tabs', (tester) async {
      app.main();
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Try to find and tap the PDF tab
      final pdfTab = find.text('PDF');
      if (pdfTab.evaluate().isNotEmpty) {
        await tester.tap(pdfTab);
        await tester.pumpAndSettle();

        // Verify tab is now selected (filter chip should be selected)
        expect(find.text('PDF'), findsOneWidget);
      }

      // Try to find and tap the WORD tab
      final wordTab = find.text('WORD');
      if (wordTab.evaluate().isNotEmpty) {
        await tester.tap(wordTab);
        await tester.pumpAndSettle();

        // Verify tab is now selected
        expect(find.text('WORD'), findsOneWidget);
      }
    });

    testWidgets('can tap ALL tab to show all documents', (tester) async {
      app.main();
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // First tap a specific type
      final pdfTab = find.text('PDF');
      if (pdfTab.evaluate().isNotEmpty) {
        await tester.tap(pdfTab);
        await tester.pumpAndSettle();
      }

      // Then tap ALL to show all documents
      final allTab = find.text('ALL');
      if (allTab.evaluate().isNotEmpty) {
        await tester.tap(allTab);
        await tester.pumpAndSettle();

        expect(find.text('ALL'), findsOneWidget);
      }
    });
  });

  group('Manual File Selection Flow', () {
    testWidgets('can open file picker via FAB', (tester) async {
      app.main();
      await tester.pumpAndSettle(const Duration(seconds: 2));

      // Look for the floating action button
      final fab = find.byTooltip('Select File');
      if (fab.evaluate().isNotEmpty) {
        await tester.tap(fab);
        await tester.pumpAndSettle();

        // File picker should open (external activity)
        // We can't fully test the file picker interaction in integration tests
        // as it's a system dialog, but we can verify the tap doesn't crash
      }
    });

    testWidgets('can tap Select a File button from permission widget', (tester) async {
      app.main();
      await tester.pumpAndSettle(const Duration(seconds: 2));

      // Look for the Select a File button
      final selectButton = find.text('Select a File');
      if (selectButton.evaluate().isNotEmpty) {
        await tester.tap(selectButton);
        await tester.pumpAndSettle();

        // File picker should open
        // We can't fully test the file picker interaction
      }
    });
  });

  group('Permission Request Flow', () {
    testWidgets('can tap Grant Permission button', (tester) async {
      app.main();
      await tester.pumpAndSettle(const Duration(seconds: 2));

      // Look for the Grant Permission button
      final grantButton = find.text('Grant Permission');
      if (grantButton.evaluate().isNotEmpty) {
        await tester.tap(grantButton);
        await tester.pumpAndSettle(const Duration(seconds: 2));

        // System permission dialog should appear
        // We can't fully test the system dialog interaction
        // but we can verify the tap doesn't crash
      }
    });
  });

  group('Document Interaction', () {
    testWidgets('can tap on a document to open viewer', (tester) async {
      app.main();
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Look for any document list items
      final listItems = find.byType(DocumentListItem);
      if (listItems.evaluate().isNotEmpty) {
        // Tap the first document
        await tester.tap(listItems.first);
        await tester.pumpAndSettle(const Duration(seconds: 2));

        // Should navigate to viewer screen
        // Verify by checking if we're no longer on home screen
        // (viewer screens have different titles)
      }
    });

    testWidgets('can navigate back from viewer to home', (tester) async {
      app.main();
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Look for any document list items
      final listItems = find.byType(DocumentListItem);
      if (listItems.evaluate().isNotEmpty) {
        // Tap the first document
        await tester.tap(listItems.first);
        await tester.pumpAndSettle(const Duration(seconds: 2));

        // Navigate back using back button in app bar
        final backButton = find.byTooltip('Back');
        if (backButton.evaluate().isNotEmpty) {
          await tester.tap(backButton);
          await tester.pumpAndSettle();

          // Should be back on home screen
          expect(find.text('Documents'), findsOneWidget);
        }
      }
    });
  });

  group('Pull to Refresh', () {
    testWidgets('can pull to refresh document list', (tester) async {
      app.main();
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Look for RefreshIndicator (it wraps the list when permission is granted)
      // Perform a drag gesture to trigger refresh
      await tester.drag(
        find.byType(CustomScrollView).first,
        const Offset(0, 300),
      );
      await tester.pumpAndSettle(const Duration(seconds: 2));

      // List should refresh (may show loading indicator briefly)
      // Then show documents again
      expect(find.text('Documents'), findsOneWidget);
    });
  });

  group('Error Handling', () {
    testWidgets('app handles missing documents gracefully', (tester) async {
      app.main();
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // If there are no documents, should show empty state message
      final emptyMessage = find.text('No documents found');
      // Message may or may not appear depending on device content
      // Just verify app doesn't crash
      expect(emptyMessage, findsWidgets); // 0 or 1
    });
  });
}
