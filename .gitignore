# Flutter-specific
.dart_tool/
.dart_tool_build/
.flutter-plugins/
.flutter-plugins-dependencies
.packages
build/
flutter_assets_build/
.code-actions

# Dependencies
/pubspec.lock
/pubspec_overrides/
.pub/
.pub-cache/

# Generated code
**/lib/generated/
**/generated_plugin_registrant.dart
**/lib/**/*.g.dart
**/lib/**/*.freezed.dart

# Documentation
**/doc/api/

# Android
android/app/build/
android/app/outputs/
android/app/profile/
android/app/debug/
android/app/release/
android/.gradle/
android/captures/
android/gradle.properties
android/gradlew
android/gradlew.bat
.android/

# iOS
ios/Flutter/App.framework/
ios/Flutter/Flutter.framework/
ios/Flutter/Flutter.podspec
ios/Default-*.png
ios/Runner.xcodeproj/
ios/Runner.xcworkspace/
ios/.symlinks/
ios/Pods/
ios/Flutter/build/
ios/Flutter/.symlinks/
ios/Pods/Pods.xcodeproj/
ios/Runner/GeneratedPluginRegistrant.*

# Web
web/.build/
build/web/

# macOS
**/macos/Flutter/ephemeral
**/macos/Flutter/FlutterEmbedder.framework
**/macos/Flutter/GeneratedPluginRegistrant.swift
**/macos/Flutter/ephemeral/.symlinks
**/macos/.symlinks

# Windows
**/windows/Flutter/ephemeral
**/windows/Flutter/GeneratedPlugins/
**/windows/Flutter/FlutterPluginRegistrant/
**/windows/Flutter/generated_plugins.cmake
**/windows/Flutter/.symlinks

# Linux
**/linux/Flutter/ephemeral
**/linux/Flutter/generated_plugins.cmake
**/linux/Flutter/.symlinks

# IDE Files
.idea/
.vscode/
*.swp
*.log
*.tmp
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Standard Flutter template items
.atom/
*.iml
.uuid
Podfile.lock
Pods/
.swiftpm/
.symlinks/
*instrumentscli*.trace
*.cipd
chrome-trace.json
flutter_export_environment.sh
local.properties
keystore.properties
**/Flutter/Generated.xcconfig
**/Flutter/App.framework/
**/Flutter/ephemeral/
**/Flutter/Flutter.podspec
**/Flutter/Flutter.framework/
**/Flutter/flutter_assets/
ServiceDefinitions.json
xcuserdata/
**/DerivedData/
generated_plugin_registrant.*
GeneratedPluginRegistrant.*
**/gradle-wrapper.jar
.gradle/
gradlew
gradlew.bat
.cxx/
.project
.classpath
.settings

# Symbolication and Obfuscation
app.*.symbols
app.*.map.json
.kilocode/mcp.json
.claude/agents/risk-detector.md
linux/CMakeLists.txt
linux/flutter/CMakeLists.txt
linux/flutter/generated_plugins.cmake
linux/runner/CMakeLists.txt
linux/runner/main.cc
linux/runner/my_application.cc
linux/runner/my_application.h
