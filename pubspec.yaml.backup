name: myapp
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.9.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  # State Management & DI
  flutter_riverpod: ^3.0.1
  riverpod_annotation: ^3.0.1

  # Functional Programming (Either)
  dartz: ^0.10.1

  # Immutable Models
  freezed_annotation: ^3.1.0
  json_annotation: ^4.9.0

  # Document Viewers
  syncfusion_flutter_pdfviewer: ^31.1.22  # PDF viewing (compatible with intl 0.20.2)
  syncfusion_flutter_pdf: ^31.1.22       # PDF manipulation (encryption, metadata)
  microsoft_viewer: ^0.0.7               # DOCX/XLSX/PPTX viewing
  # Note: excel package conflicts with microsoft_viewer due to archive dependency
  # Using microsoft_viewer for XLSX viewing instead of custom excel + DataGrid approach

  # File Management
  file_picker: ^8.2.1                    # Manual file selection
  permission_handler: ^11.3.1            # Storage permissions
  device_info_plus: ^12.1.0              # Platform & device info (API level detection)
  path_provider: ^2.1.5                  # App directories
  share_plus: ^10.1.4                    # Platform share sheet integration

  # Utilities
  path: ^1.9.0                           # Path manipulation
  intl: ^0.20.2                          # Date/time formatting

  # UI Utilities
  go_router: ^14.2.0                     # Navigation
  # two_dimensional_scrollables: ^0.3.0    # No longer needed (microsoft_viewer handles XLSX)

dev_dependencies:
  flutter_test:
    sdk: flutter

  integration_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

  # Code Generation
  build_runner: ^2.4.8
  freezed: ^3.2.3
  json_serializable: ^6.7.1
  riverpod_generator: ^3.0.1
  riverpod_lint: ^3.0.1

  # Testing
  mocktail: ^1.0.4

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
