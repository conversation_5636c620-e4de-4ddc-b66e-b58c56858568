### **Master Product Plan: Document Reader v2.0**

The update from the MVP reader to a full-featured document suite will be managed through the following discrete PRDs. This modular approach allows for parallel development streams.

*   **`PRD-001: Core App Enhancements`**: Foundational UI/UX improvements and essential file-level utilities.
*   **`PRD-002: File Conversion Suite`**: A complete toolkit for converting various file formats into PDF.
*   **`PRD-003: PDF Page Management Tools`**: Features focused on manipulating the structure of PDF documents (reordering, merging, splitting pages).
*   **`PRD-004: PDF Editor & Annotation Toolkit`**: The core in-document editing and markup features.
*   **`PRD-005: AI-Powered PDF Tools (VIP Features)`**: Advanced, premium features that leverage AI for document intelligence.

---

### **`PRD-001: Core App Enhancements`**

*   **Goal:** To enhance the core application shell with essential utilities and a better navigation structure to accommodate future features. This module can be developed alongside any other module.
*   **Features & Scope:**
    1.  **"Discover" Tab Implementation:** Create the main tab/entry point for all new tools.
    2.  **File Security:** Add the ability to password-protect PDFs.
    3.  **Enhanced Sharing:** A more robust sharing mechanism.
    4.  **File Information:** A dedicated screen to view file metadata.
*   **Screens:**
    *   **Discover Screen:** A grid or list-based layout that will house the entry points for all tools (Merge, Split, Convert, AI, etc.). This screen acts as a central hub.
    *   **Password Prompt UI:** A dialog to set/enter a password for a PDF.
    *   **File Info Screen:** A screen displaying metadata like file name, path, size, creation date, author, etc.
*   **User Stories:**
    *   **US1.1 (Navigation):** As a user, I want to tap a "Discover" or "Tools" button in the main navigation so that I can easily find all the advanced features in one place.
    *   **US1.2 (Security):** As a user handling sensitive documents, I want to add a password to my PDF so that only authorized people can open it.
    *   **US1.3 (Info):** As a user, I want to view the detailed properties of a file so that I can see its location on my device and when it was last modified.

---

### **`PRD-002: File Conversion Suite`**

*   **Goal:** To provide users with a comprehensive set of tools to create PDF files from other common formats and from physical documents.
*   **Features & Scope:**
    1.  **Image to PDF:** Convert one or multiple images into a single PDF.
    2.  **Docx to PDF:** Convert Word documents.
    3.  **PPT to PDF:** Convert PowerPoint presentations.
    4.  **XLSX to PDF:** Convert Excel spreadsheets.
    5.  **Scan Document:** Use the device camera to scan and convert physical pages into a PDF.
*   **Screens:**
    *   **File/Image Picker:** A native interface to select one or more files/images for conversion.
    *   **Conversion Options Screen:** A simple screen showing the selected file and a "Convert" button. It may include basic options like page orientation for images.
    *   **Scanner Interface:** A dedicated screen that activates the camera. It should include an overlay for edge detection, a capture button, and options for flash.
    *   **Scan Review Screen:** After capturing, this screen allows the user to crop, rotate, and apply filters (e.g., B&W, Grayscale) to the scanned image before finalizing the PDF.
*   **User Flow (Docx to PDF):**
    1.  User navigates to Discover > "Docx to PDF".
    2.  The app opens a file picker filtered for .doc/.docx files.
    3.  User selects a document.
    4.  App shows the selected file and a "Convert to PDF" button.
    5.  User taps the button.
    6.  The app processes the file and saves the new PDF in a "Converted" folder, then offers to open or share it.
*   **User Stories:**
    *   **US2.1 (Scan):** As a student, I want to quickly scan my classmate's handwritten notes with my phone camera and save them as a single PDF so I can study them later.
    *   **US2.2 (Convert):** As a professional, I want to convert my Word resume into a PDF directly on my phone so I can send a universally compatible version to a recruiter.
    *   **US2.3 (Images):** As a user, I want to combine multiple screenshots of a recipe into one PDF file so I can easily share it as a single, organized document.

---

### **`PRD-003: PDF Page Management Tools`**

*   **Goal:** To empower users with tools to organize and restructure PDF documents at the page level.
*   **Features & Scope:**
    1.  **Merge PDF:** Combine two or more PDF documents into one.
    2.  **Split PDF:** Extract a range of pages or individual pages from a PDF into a new file.
    3.  **Manage/Reorder Pages:** Visually change the order of pages within a PDF.
    4.  **Extract Pages:** A simplified version of Split, focusing on selecting specific pages to create a new PDF.
*   **Screens:**
    *   **Page Management UI:** A crucial screen showing thumbnails of all pages in a grid. This UI must support:
        *   Multi-selection of pages.
        *   Drag-and-drop functionality to reorder pages.
        *   Buttons for deleting or rotating selected pages.
    *   **Multi-File Picker (for Merge):** An interface that allows the user to select multiple PDF files and define their order before merging.
    *   **Split Options Dialog (for Split):** A dialog where users can input a page range (e.g., "2-5") or select from options like "Split every 1 page".
*   **User Flow (Reorder Pages):**
    1.  User opens a PDF or selects "Manage Page" from the Discover tab and then picks a PDF.
    2.  The app opens the Page Management UI, displaying all pages as thumbnails.
    3.  User long-presses and drags page 5 to the position of page 2.
    4.  The other pages shift accordingly.
    5.  User taps a "Save" or "Apply" button.
    6.  The app creates a new, reordered copy of the PDF.
*   **User Stories:**
    *   **US3.1 (Merge):** As an assistant, I want to merge a cover letter PDF and a resume PDF into a single application document.
    *   **US3.2 (Split):** As a researcher, I want to split a 50-page research paper to extract only the 5-page methodology section that I need to share with a colleague.
    *   **US3.3 (Reorder):** As a user, I want to reorder the pages of a scanned report because I scanned them in the wrong order.

---

### **`PRD-004: PDF Editor & Annotation Toolkit`**

*   **Goal:** To provide rich, in-document tools for marking up, signing, and adding content to PDF files. This is likely the most complex module.
*   **Features & Scope:**
    1.  **Annotation Tools:** Highlight, underline, strikethrough text.
    2.  **Signature Tool:** Create, save, and place digital signatures.
    3.  **Add Watermark:** Apply a text watermark.
    4.  **Add Page Numbers:** Insert page numbers with basic formatting options.
    5.  **Form Filling:** Detect and allow typing in PDF form fields.
*   **Screens / UI Components:**
    *   **Editing Toolbar:** An overlay toolbar (likely at the bottom or top of the viewer) that appears when in "Edit" or "Annotate" mode. It will contain icons for each tool (highlighter, signature, etc.).
    *   **Signature Creation Pad:** A full-screen canvas where the user can draw their signature with their finger or a stylus. It should have options to save the signature for future use.
    *   **Text/Watermark Input Dialog:** A simple pop-up to type the text for watermarks or other text additions. It could include options for font, size, and color.
*   **User Flow (Signing a Document):**
    1.  User opens a PDF contract.
    2.  User taps an "Edit" or "Sign" button.
    3.  The editing toolbar appears. User selects the "Signature" tool.
    4.  If no signature is saved, the Signature Creation Pad opens. The user draws their signature and saves it.
    5.  The signature now appears as a draggable, resizable object on the PDF.
    6.  User drags the signature to the signature line and adjusts its size.
    7.  User taps "Save", and the app flattens the signature onto the PDF, creating a new saved version.
*   **User Stories:**
    *   **US4.1 (Sign):** As a freelancer, I want to sign a contract sent to me as a PDF on my phone and email it back immediately.
    *   **US4.2 (Annotate):** As a student, I want to highlight important paragraphs and add text notes in my digital textbook PDF to prepare for an exam.
    *   **US4.3 (Form):** As a user, I want to fill out a registration form in PDF format without having to print it.

---

### **`PRD-005: AI-Powered PDF Tools (VIP Features)`**

*   **Goal:** To introduce premium, high-value features that differentiate the product and create a monetization opportunity. These features rely on external AI/LLM services.
*   **Features & Scope:**
    1.  **Summarize PDF:** Generate a concise summary of a long document.
    2.  **Translate PDF:** Translate the text of a PDF to another language.
    3.  **PDF AI Assistant:** An interactive chat interface to ask questions about the document's content ("What was the conclusion of this report?").
*   **Screens:**
    *   **AI Tools Sub-menu:** A menu within the Discover screen for the AI tools.
    *   **Paywall/VIP Screen:** A screen explaining the benefits of the VIP features and prompting the user to subscribe or purchase.
    *   **Summary/Translation Display:** A simple screen or bottom sheet that displays the generated text with a "copy to clipboard" option.
    *   **AI Assistant Chat UI:** A chat-like interface where the user can type questions and see answers generated by the AI.
*   **User Flow (Summarize):**
    1.  User opens a 30-page research paper.
    2.  From the options menu or Discover tab, they select "Summarize PDF".
    3.  (First time) The app displays the VIP screen. User subscribes.
    4.  The app shows a progress indicator ("Summarizing...").
    5.  A bottom sheet appears with a bulleted or paragraph summary of the document.
    6.  The user can read the summary and copy it.
*   **User Stories:**
    *   **US5.1 (Summarize):** As a busy manager, I want to get a quick summary of a long report to understand its key points without reading the whole thing.
    *   **US5.2 (Ask):** As a student, I want to ask my PDF "What are the arguments against this theory?" so I can find information quickly without re-reading multiple sections.
    *   **US5.3 (Translate):** As a non-native English speaker, I want to translate a technical manual from English to my native language to better understand it.