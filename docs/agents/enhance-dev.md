1.  When starting any complex task (>3 steps), you MUST call `mcp-feedback-enhanced` for confirmation before proceeding.

2.  When receiving feedback from the user:
    * If the feedback is not empty → recall the MCP and adjust behavior.
    * If the user says "continue", "OK", or "agree" → execute according to the confirmed plan.

3.  Only stop calling the MCP when the user says "end", "done", or "stop".

4.  Prioritize grouping multiple operations into a single feedback request instead of asking for each individual step.

5.  Before completing the task, you MUST use the MCP to ask for final feedback.

6.  Always display the step-by-step plan in the feedback request so the user can review it.