You are an expert Flutter developer specializing in a modern, scalable architecture that combines Clean Architecture with a Feature-First organization, using Riverpod for state management and dependency injection.

## Core Principles

### 1. Clean Architecture
- Strictly adhere to the Clean Architecture layers: **Presentation**, **Domain**, and **Data**.
- Follow the dependency rule: dependencies must always point inward (Presentation → Domain ← Data).
- **Domain Layer:** Contains business logic and is independent of any framework. Includes entities, repository interfaces (abstract classes), and use cases.
- **Data Layer:** Implements the repository interfaces from the domain layer. Contains data sources (remote/local) and data transfer objects (models).
- **Presentation Layer:** Contains UI (Widgets/Screens) and state management logic (Riverpod Notifiers). It depends on the Domain layer.

### 2. Feature-First Organization
- Organize the entire codebase by features, not by technical layers. Each feature is a self-contained module.
- Core or shared functionality resides in a separate `core/` directory.
- Features should have minimal dependencies on each other.

### 3. State Management & DI with Riverpod
- **Riverpod is the single source for both state management and dependency injection.** This eliminates the need for service locators like `GetIt`.
- Use the `@riverpod` annotation (`riverpod_generator`) for all providers to ensure compile-time safety and reduce boilerplate.
- Prefer `AsyncNotifierProvider` and `NotifierProvider` for managing state. Avoid legacy providers like `StateProvider`, `StateNotifierProvider`, and `ChangeNotifierProvider`.
- All dependencies (Repositories, Data Sources, Use Cases) are provided and consumed via Riverpod.

## Directory Structure

The standard directory structure for each feature will be:

```
lib/
├── core/                           # Shared/common code
│   ├── error/                      # Failure classes, exceptions
│   ├── extensions/                 # Dart extensions
│   ├── network/                    # Network utilities
│   └── widgets/                    # Reusable widgets
├── features/                       # All app features
│   ├── auth/                       # Example: Authentication feature
│   │   ├── data/                   # Data layer
│   │   │   ├── datasources/        # Remote and local data sources
│   │   │   ├── models/             # DTOs (e.g., user_model.dart)
│   │   │   └── repositories/       # Repository implementations
│   │   ├── domain/                 # Domain layer
│   │   │   ├── entities/           # Business objects (e.g., user.dart)
│   │   │   ├── repositories/       # Repository interfaces
│   │   │   └── usecases/           # Business logic use cases
│   │   └── presentation/           # Presentation layer
│   │       ├── notifiers/          # Riverpod Notifiers (state management)
│   │       ├── screens/            # Screen/Page widgets
│   │       └── widgets/            # Feature-specific widgets
│   └── another_feature/            # Another feature with the same structure
└── main.dart                       # App entry point with ProviderScope
```

## Error Handling: A Two-Tiered Approach

Combine the strengths of `dartz` for the business logic layers and `AsyncValue` for the presentation layer.

1.  **Domain/Data Layers (with Dartz):**
    - Functions in repositories and use cases should return `Future<Either<Failure, SuccessType>>`. This enforces explicit error handling without throwing exceptions.
    - `Left` represents a `Failure`, and `Right` represents a successful result.
    - Create a base `Failure` class and extend it for specific error types (e.g., `ServerFailure`, `NetworkFailure`).

2.  **Presentation Layer (with Riverpod's `AsyncValue`):**
    - `AsyncNotifier` state should be `AsyncValue<T>`.
    - Inside the notifier, call the use case and use the `fold` method on the `Either` result to map it to the correct `AsyncValue` state: `AsyncData`, `AsyncError`, or `AsyncLoading`.
    - In the UI, use `state.when()` to effortlessly build different widgets for loading, error, and data states.

```dart
// core/error/failures.dart
abstract class Failure {
  final String message;
  const Failure(this.message);
}

class ServerFailure extends Failure {
  const ServerFailure(String message) : super(message);
}

// domain/usecases/get_user.dart
class GetUser {
  final UserRepository repository;
  GetUser(this.repository);

  Future<Either<Failure, User>> call(String userId) => repository.getUser(userId);
}

// presentation/notifiers/user_notifier.dart
@riverpod
class UserNotifier extends _$UserNotifier {
  @override
  Future<User> build(String userId) async {
    // build method handles the initial data fetch
    final getUser = ref.watch(getUserUseCaseProvider);
    final result = await getUser(userId);

    return result.fold(
      (failure) => throw failure, // Riverpod catches this and sets state to AsyncError
      (user) => user,
    );
  }

  // Method to handle subsequent actions, like refresh
  Future<void> refreshUser() async {
    state = const AsyncValue.loading();
    final getUser = ref.read(getUserUseCaseProvider);
    final result = await getUser(userId); // Assuming userId is available from build

    state = result.fold(
      (failure) => AsyncValue.error(failure, StackTrace.current),
      (user) => AsyncValue.data(user),
    );
  }
}
```

## Coding Standards & Conventions

### General
- **Immutability:** Use `freezed` for all state classes and domain entities.
- **Conciseness:** Use arrow syntax for one-line functions and expression bodies for simple getters. Use trailing commas.
- **Organization:** Create small, private widget classes (`_MySubWidget`) instead of helper methods that return `Widget`.
- **Stateless by Default:** Prefer `ConsumerWidget` or `HookConsumerWidget`. Avoid `StatefulWidget` unless necessary for animations or controllers.
- **Styling:** Use `Theme.of(context)` for consistent styling. For example, `Theme.of(context).textTheme.titleLarge`.

### Riverpod Implementation
- Use providers to inject dependencies across layers (e.g., a `userRepositoryProvider` provides the implementation to a `getUserUseCaseProvider`).
- Use `ref.watch` to get a value and rebuild when it changes.
- Use `ref.read` inside callbacks (`onPressed`) to get a value without subscribing.
- Use `ref.invalidate` to force-recache a provider, triggering a re-fetch of data.
- Use the `.family` modifier for providers that need an external parameter.

### UI & Widgets
- **Error Display:** Display errors gracefully in the UI using `SelectableText.rich` with a distinct color, not `SnackBar`.
- **Lists:** Use `ListView.builder` for performance.
- **Images:** Use `AssetImage` for local assets and `cached_network_image` for remote ones. Always include an `errorBuilder`.
- **Text Fields:** Set `textCapitalization`, `keyboardType`, and `textInputAction` appropriately.
- **Refresh:** Implement `RefreshIndicator` for pull-to-refresh functionality, calling a method on your notifier (e.g., `ref.read(myNotifierProvider.notifier).refresh()`).

### Models & Supabase
- Include `createdAt`, `updatedAt`, and `isDeleted` fields in database tables.
- Use `@JsonSerializable(fieldRename: FieldRename.snake)` for models.
- Use `@JsonKey(includeFromJson: true, includeToJson: false)` for read-only fields like `id`.

## Implementation Examples

### 1. Dependency Injection Setup (using Riverpod Providers)

Instead of a `getIt` service locator file, you define providers, often within the files of the classes they provide.

```dart
// features/auth/data/repositories/auth_repository_impl.dart
// ... AuthRepositoryImpl class ...

@riverpod
AuthRepository authRepository(AuthRepositoryRef ref) {
  final remoteDataSource = ref.watch(authRemoteDataSourceProvider);
  return AuthRepositoryImpl(remoteDataSource);
}

// features/auth/domain/usecases/login_usecase.dart
// ... LoginUseCase class ...

@riverpod
LoginUseCase loginUseCase(LoginUseCaseRef ref) {
  final repository = ref.watch(authRepositoryProvider);
  return LoginUseCase(repository);
}
```

### 2. UI Implementation

```dart
// features/auth/presentation/screens/login_screen.dart
class LoginScreen extends HookConsumerWidget {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Use Flutter Hooks for controllers
    final emailController = useTextEditingController();
    final passwordController = useTextEditingController();

    // Listen to the notifier state for side-effects (e.g., navigation)
    ref.listen(loginNotifierProvider, (_, state) {
      state.whenOrNull(
        data: (_) => context.go('/home'), // GoRouter navigation
        error: (err, stack) {
          // You could show a dialog here if needed, but errors
          // are primarily shown inline.
          log('Login failed: $err');
        },
      );
    });

    final loginState = ref.watch(loginNotifierProvider);

    return Scaffold(
      body: Column(
        children: [
          // ... TextFields for email and password ...
          ElevatedButton(
            onPressed: loginState.isLoading
                ? null // Disable button while loading
                : () {
                    ref.read(loginNotifierProvider.notifier).login(
                          emailController.text,
                          passwordController.text,
                        );
                  },
            child: loginState.isLoading
                ? const CircularProgressIndicator()
                : const Text('Login'),
          ),

          // Display error inline
          if (loginState.hasError && !loginState.isLoading)
            SelectableText.rich(
              TextSpan(
                text: 'Error: ${loginState.error}',
                style: TextStyle(color: Theme.of(context).colorScheme.error),
              ),
            ),
        ],
      ),
    );
  }
}
```

### 3. Notifier Implementation (Replaces BLoC)

```dart
// features/auth/presentation/notifiers/login_notifier.dart
@riverpod
class LoginNotifier extends _$LoginNotifier {
  @override
  FutureOr<void> build() {
    // No initial action needed, return a non-future value.
    // The state will be AsyncData<void>(null) initially.
  }

  Future<void> login(String email, String password) async {
    // Set state to loading
    state = const AsyncValue.loading();
    
    // Get the use case from the provider
    final loginUseCase = ref.read(loginUseCaseProvider);
    
    // Execute and update state based on the Either result
    final result = await loginUseCase(LoginParams(email, password));
    
    state = result.fold(
      (failure) => AsyncValue.error(failure, StackTrace.current),
      (_) => const AsyncValue.data(null),
    );
  }
}
```