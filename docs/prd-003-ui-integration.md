# PRD-003: UI Integration Guide

**Date:** 2025-10-04

This document provides instructions for UI developers on how to integrate the PDF Page Management features into the application.

---

## 1. Available Features & Routes

The following features have been implemented and are available via `go_router` navigation. They are self-contained and do not require any route parameters.

| Feature | Route Path | Navigation Code |
| :--- | :--- | :--- |
| **Merge PDFs** | `/merge-pdf` | `context.go('/merge-pdf');` |
| **Split PDF** | `/split-pdf` | `context.go('/split-pdf');` |
| **Manage Pages** | `/manage-pages`| `context.go('/manage-pages');` |
| **Extract Pages**| `/extract-pages`| `context.go('/extract-pages');` |

All routes are defined in `lib/core/navigation/app_router.dart`.

---

## 2. Recommended Integration Point

As per the project plan, these features are intended to be launched from the main **"Discover"** hub of the application.

It is recommended to create UI elements (e.g., `Card`, `ListTile`, or `ElevatedButton`) for each of the features listed above and use the corresponding navigation code in the `onTap` or `onPressed` callbacks.

### Example Widget

```dart
// Example for a button to launch the Merge PDF screen
ElevatedButton(
  onPressed: () => context.go('/merge-pdf'),
  child: const Text('Merge PDFs'),
)
```

---

## 3. Notes

- The screens currently use dummy data for file and page selection. The logic to select a source PDF will need to be wired up within each feature screen as a next step.
- All screens are built with Material 3 design principles and will adapt to the application's theme.
