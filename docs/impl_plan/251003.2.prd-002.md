# Implementation Plan: PRD-002 - File Conversion Suite

## PHASE 1: REQUIREMENTS ANALYSIS

### 1.1 Established Architecture Constraint
- **Architecture**: Feature-First Clean Architecture with Riverpod state management
- **Core Principle**: All features must follow the three-layer structure (Presentation → Domain ← Data)
- **State Management**: Riverpod with `@riverpod` annotations and code generation
- **Error Handling**: Dartz `Either<Failure, Success>` in domain/data layers, `AsyncValue` in presentation
- **Models**: Freezed for immutable entities and models

### 1.2 User Stories & EARS-Formatted Requirements

#### US2.1: Scan Document
**Story**: As a student, I want to quickly scan my classmate's handwritten notes with my phone camera and save them as a single PDF so I can study them later.

**EARS Requirements**:
- **REQ-2.1.1**: WHEN the user selects "Scan Document" from Discover tab THE SYSTEM SHALL open the device camera with a scanning interface
- **REQ-2.1.2**: WHEN the camera is active THE SYSTEM SHALL display edge detection overlay and capture controls
- **REQ-2.1.3**: WHEN the user captures an image THE SYSTEM SHALL navigate to a review screen showing the captured image
- **REQ-2.1.4**: WHEN on the review screen THE SYSTEM SHALL provide options to crop, rotate, and apply filters (B&W, Grayscale, Color)
- **REQ-2.1.5**: WHEN the user captures multiple images THE SYSTEM SHALL allow adding more pages or proceeding to finalize
- **REQ-2.1.6**: WHEN the user finalizes the scan THE SYSTEM SHALL convert all captured images into a single PDF
- **REQ-2.1.7**: WHEN the PDF is created THE SYSTEM SHALL save it in a "Scanned" folder with timestamp naming
- **REQ-2.1.8**: WHEN the scan conversion completes THE SYSTEM SHALL offer options to open or share the PDF

#### US2.2: Convert DOCX to PDF
**Story**: As a professional, I want to convert my Word resume into a PDF directly on my phone so I can send a universally compatible version to a recruiter.

**EARS Requirements**:
- **REQ-2.2.1**: WHEN the user selects "DOCX to PDF" from Discover tab THE SYSTEM SHALL open a file picker filtered for .doc/.docx files
- **REQ-2.2.2**: WHEN the user selects a DOCX file THE SYSTEM SHALL display the conversion options screen with file details
- **REQ-2.2.3**: WHEN the user taps "Convert to PDF" THE SYSTEM SHALL process the DOCX file and generate a PDF
- **REQ-2.2.4**: WHEN the conversion completes THE SYSTEM SHALL save the PDF in a "Converted" folder with the same filename
- **REQ-2.2.5**: WHEN the conversion fails THE SYSTEM SHALL display an error message with failure reason
- **REQ-2.2.6**: WHEN the conversion succeeds THE SYSTEM SHALL offer options to open or share the PDF

#### US2.3: Convert Images to PDF
**Story**: As a user, I want to combine multiple screenshots of a recipe into one PDF file so I can easily share it as a single, organized document.

**EARS Requirements**:
- **REQ-2.3.1**: WHEN the user selects "Images to PDF" from Discover tab THE SYSTEM SHALL open an image picker supporting multi-selection
- **REQ-2.3.2**: WHEN images are selected THE SYSTEM SHALL display the conversion options screen with image previews
- **REQ-2.3.3**: WHEN on the options screen THE SYSTEM SHALL allow the user to reorder images via drag-and-drop
- **REQ-2.3.4**: WHEN on the options screen THE SYSTEM SHALL provide page orientation options (Portrait/Landscape/Auto)
- **REQ-2.3.5**: WHEN the user taps "Convert to PDF" THE SYSTEM SHALL combine all images into a single PDF maintaining the order
- **REQ-2.3.6**: WHEN the conversion completes THE SYSTEM SHALL save the PDF in a "Converted" folder
- **REQ-2.3.7**: WHEN the conversion succeeds THE SYSTEM SHALL offer options to open or share the PDF

#### Additional Conversion Features
**PPT to PDF & XLSX to PDF** follow the same pattern as DOCX conversion with different file filters.

**EARS Requirements**:
- **REQ-2.4.1**: WHEN the user selects "PPT to PDF" THE SYSTEM SHALL open a file picker filtered for .ppt/.pptx files
- **REQ-2.4.2**: WHEN the user selects "XLSX to PDF" THE SYSTEM SHALL open a file picker filtered for .xls/.xlsx files
- **REQ-2.4.3**: WHEN any conversion type is selected THE SYSTEM SHALL follow the same conversion flow as DOCX

### 1.3 Technical Constraints
- **Platform**: Android only (MVP scope)
- **Camera Access**: Requires CAMERA permission
- **Storage Access**: Requires storage permissions for file access and saving
- **Output Format**: PDF files compatible with existing PDF viewer
- **Performance**: Conversion should provide progress feedback for operations > 2 seconds

### 1.4 Non-Functional Requirements (EARS Format)
- **REQ-2.NF.1**: WHEN a conversion operation exceeds 2 seconds THE SYSTEM SHALL display a progress indicator
- **REQ-2.NF.2**: WHEN a conversion fails THE SYSTEM SHALL preserve the original file unchanged
- **REQ-2.NF.3**: WHEN saving converted files THE SYSTEM SHALL use atomic file operations to prevent corruption
- **REQ-2.NF.4**: WHEN the camera is in use THE SYSTEM SHALL maintain at least 15 FPS for edge detection overlay

---

## PHASE 2: SPECIFICATION CREATION

### 2.1 Feature Module Structure

Following Feature-First Clean Architecture, create a new feature module:

```
lib/features/file_conversion/
├── data/
│   ├── datasources/
│   │   ├── camera_data_source.dart          # Camera & image capture
│   │   ├── file_picker_data_source.dart     # File selection
│   │   └── pdf_converter_data_source.dart   # PDF conversion operations
│   ├── models/
│   │   ├── scanned_page_model.dart          # Scanned page with filters
│   │   ├── conversion_request_model.dart    # Conversion parameters
│   │   └── conversion_result_model.dart     # Conversion output
│   └── repositories/
│       └── file_conversion_repository_impl.dart
├── domain/
│   ├── entities/
│   │   ├── scanned_page.dart                # Domain entity for scanned page
│   │   ├── conversion_request.dart          # Conversion parameters
│   │   ├── conversion_result.dart           # Conversion result
│   │   └── conversion_type.dart             # Enum: imageToPdf, docxToPdf, etc.
│   ├── repositories/
│   │   └── file_conversion_repository.dart  # Abstract repository
│   └── usecases/
│       ├── scan_document_usecase.dart       # Scan & convert to PDF
│       ├── convert_images_to_pdf_usecase.dart
│       ├── convert_docx_to_pdf_usecase.dart
│       ├── convert_ppt_to_pdf_usecase.dart
│       └── convert_xlsx_to_pdf_usecase.dart
└── presentation/
    ├── notifiers/
    │   ├── scan_notifier.dart               # Manages scanning state
    │   ├── image_conversion_notifier.dart   # Images to PDF
    │   └── document_conversion_notifier.dart # DOCX/PPT/XLSX to PDF
    ├── providers/
    │   └── file_conversion_providers.dart   # All Riverpod providers
    ├── screens/
    │   ├── scan_camera_screen.dart          # Camera with edge detection
    │   ├── scan_review_screen.dart          # Review & edit scanned pages
    │   ├── image_conversion_screen.dart     # Images to PDF options
    │   └── document_conversion_screen.dart  # Generic doc conversion
    └── widgets/
        ├── camera_controls_widget.dart      # Camera capture controls
        ├── edge_detection_overlay.dart      # Visual edge detection
        ├── page_filter_selector.dart        # B&W/Grayscale/Color filters
        ├── page_reorder_widget.dart         # Drag-drop page reordering
        └── conversion_progress_widget.dart  # Progress indicator
```

### 2.2 Domain Layer Design

#### 2.2.1 Entities

**`conversion_type.dart`** (Enum):
```dart
enum ConversionType {
  imageToPdf,
  docxToPdf,
  pptToPdf,
  xlsxToPdf,
  scanToPdf,
}
```

**`scanned_page.dart`** (Freezed Entity):
```dart
@freezed
abstract class ScannedPage with _$ScannedPage {
  const factory ScannedPage({
    required String imagePath,
    required PageFilter filter,
    required double rotationAngle,
    Rect? cropRect,
  }) = _ScannedPage;
}

enum PageFilter { color, grayscale, blackAndWhite }
```

**`conversion_request.dart`** (Freezed Entity):
```dart
@freezed
abstract class ConversionRequest with _$ConversionRequest {
  const factory ConversionRequest({
    required ConversionType type,
    required List<String> sourcePaths,
    required String outputFileName,
    PageOrientation? orientation,
    List<ScannedPage>? scannedPages,
  }) = _ConversionRequest;
}

enum PageOrientation { portrait, landscape, auto }
```

**`conversion_result.dart`** (Freezed Entity):
```dart
@freezed
abstract class ConversionResult with _$ConversionResult {
  const factory ConversionResult({
    required String outputPath,
    required String fileName,
    required int fileSize,
    required DateTime createdAt,
  }) = _ConversionResult;
}
```

#### 2.2.2 Repository Interface

**`file_conversion_repository.dart`**:
```dart
abstract class FileConversionRepository {
  // Scanning operations
  Future<Either<Failure, List<String>>> captureImages();
  Future<Either<Failure, String>> applyFiltersToImage(
    String imagePath,
    PageFilter filter,
    double rotation,
    Rect? cropRect,
  );

  // Conversion operations
  Future<Either<Failure, ConversionResult>> convertImagesToPdf(
    List<String> imagePaths,
    String outputFileName,
    PageOrientation orientation,
  );

  Future<Either<Failure, ConversionResult>> convertDocxToPdf(
    String docxPath,
    String outputFileName,
  );

  Future<Either<Failure, ConversionResult>> convertPptToPdf(
    String pptPath,
    String outputFileName,
  );

  Future<Either<Failure, ConversionResult>> convertXlsxToPdf(
    String xlsxPath,
    String outputFileName,
  );

  // File operations
  Future<Either<Failure, List<String>>> pickImages();
  Future<Either<Failure, String>> pickDocument(List<String> extensions);
}
```

#### 2.2.3 Use Cases

Each use case follows the simple pattern:
```dart
class ConvertImagesToPdfUseCase {
  final FileConversionRepository repository;
  ConvertImagesToPdfUseCase(this.repository);

  Future<Either<Failure, ConversionResult>> call(
    List<String> imagePaths,
    String outputFileName,
    PageOrientation orientation,
  ) => repository.convertImagesToPdf(imagePaths, outputFileName, orientation);
}
```

### 2.3 Data Layer Design

#### 2.3.1 Data Sources

**`camera_data_source.dart`**:
- Uses `camera` package for camera access
- Uses `google_mlkit_document_scanner` or `edge_detection` for edge detection
- Returns captured image paths

**`file_picker_data_source.dart`**:
- Uses `file_picker` package for file/image selection
- Supports multi-selection for images
- Filters by file extensions

**`pdf_converter_data_source.dart`**:
- Uses `pdf` package for PDF creation and manipulation
- For DOCX/PPT/XLSX: Research required for conversion libraries
  - Option 1: Platform channel to native Android libraries
  - Option 2: Cloud-based conversion API (if allowed)
  - Option 3: Flutter packages (if available)
- Handles image-to-PDF conversion with orientation and quality settings

#### 2.3.2 Repository Implementation

**`file_conversion_repository_impl.dart`**:
- Implements the domain repository interface
- Delegates to appropriate data sources
- Handles data source exceptions and converts to `Failure` types
- Manages temporary files and cleanup

#### 2.3.3 Models

Models mirror entities with JSON serialization:
```dart
@freezed
abstract class ScannedPageModel with _$ScannedPageModel {
  const factory ScannedPageModel({
    required String imagePath,
    required String filter,
    required double rotationAngle,
    String? cropRect,
  }) = _ScannedPageModel;

  factory ScannedPageModel.fromJson(Map<String, dynamic> json) =>
      _$ScannedPageModelFromJson(json);
}
```

### 2.4 Presentation Layer Design

#### 2.4.1 Notifiers

**`scan_notifier.dart`**:
```dart
@riverpod
class ScanNotifier extends _$ScanNotifier {
  @override
  FutureOr<List<ScannedPage>> build() => [];

  Future<void> captureImage() async { /* ... */ }
  Future<void> applyFilter(int index, PageFilter filter) async { /* ... */ }
  Future<void> rotatePage(int index, double angle) async { /* ... */ }
  Future<void> cropPage(int index, Rect cropRect) async { /* ... */ }
  Future<void> finalizeScan(String fileName) async { /* ... */ }
}
```

**`document_conversion_notifier.dart`**:
```dart
@riverpod
class DocumentConversionNotifier extends _$DocumentConversionNotifier {
  @override
  FutureOr<ConversionResult?> build() => null;

  Future<void> convertDocx(String filePath) async { /* ... */ }
  Future<void> convertPpt(String filePath) async { /* ... */ }
  Future<void> convertXlsx(String filePath) async { /* ... */ }
}
```

#### 2.4.2 Screens

**`scan_camera_screen.dart`**:
- Full-screen camera preview
- Edge detection overlay
- Camera controls (capture, flash, switch camera)
- Navigation to review screen after capture

**`scan_review_screen.dart`**:
- Grid/carousel view of captured pages
- Filter selector per page
- Rotation controls
- Crop tool
- "Add More" and "Finalize" buttons

**`image_conversion_screen.dart`**:
- Display selected images
- Drag-drop reordering
- Orientation selector
- Convert button
- Progress indicator during conversion

**`document_conversion_screen.dart`**:
- Display selected file name and size
- Convert button
- Progress indicator
- Success/error message display

#### 2.4.3 Widgets

All reusable UI components as small, focused widgets:
- `CameraControlsWidget`: Capture, flash, switch camera buttons
- `EdgeDetectionOverlay`: Visual overlay showing detected edges
- `PageFilterSelector`: Chips/buttons for Color/Grayscale/B&W
- `PageReorderWidget`: Drag-drop enabled grid of pages
- `ConversionProgressWidget`: Linear progress with percentage/status

### 2.5 Navigation Integration

Update `core/navigation/app_router.dart`:
```dart
GoRoute(
  path: '/scan-document',
  builder: (context, state) => const ScanCameraScreen(),
),
GoRoute(
  path: '/scan-review',
  builder: (context, state) => const ScanReviewScreen(),
),
GoRoute(
  path: '/convert-images',
  builder: (context, state) => const ImageConversionScreen(),
),
GoRoute(
  path: '/convert-document',
  builder: (context, state) {
    final type = state.uri.queryParameters['type']!;
    return DocumentConversionScreen(conversionType: type);
  },
),
```

### 2.6 Error Handling Strategy

**Failure Types** (add to `core/error/failures.dart`):
```dart
class CameraFailure extends Failure {
  const CameraFailure(super.message);
}

class ConversionFailure extends Failure {
  const ConversionFailure(super.message);
}

class FilePickerFailure extends Failure {
  const FilePickerFailure(super.message);
}

class StorageFailure extends Failure {
  const StorageFailure(super.message);
}
```

**Error Mapping in Presentation**:
- `CameraFailure` → "Cannot access camera. Please check permissions."
- `ConversionFailure` → "Conversion failed: [detailed message]"
- `FilePickerFailure` → "Could not select file. Please try again."
- `StorageFailure` → "Cannot save file. Check storage permissions."

### 2.7 Testing Strategy

#### Unit Tests
- **Domain Layer**: Test all use cases with mocked repositories
- **Data Layer**: Test repository implementation with mocked data sources
- **Models**: Test JSON serialization/deserialization

#### Integration Tests
- Test complete conversion flows end-to-end
- Test file picker → conversion → save flow
- Test camera → scan → review → finalize flow

#### Widget Tests
- Test each screen's UI rendering
- Test user interactions (button taps, drag-drop)
- Test state changes and error displays

#### Platform-Specific Tests
- Camera functionality on physical device
- File system operations
- Permission handling

### 2.8 Required Packages Research

**Research Required** (use WebSearch):
1. **Camera & Edge Detection**:
   - `camera` package (official)
   - `google_mlkit_document_scanner` vs `edge_detection` vs `cunning_document_scanner`
   - Performance and compatibility comparison

2. **Office Document Conversion**:
   - Flutter packages for DOCX/PPT/XLSX to PDF
   - Native platform channel options
   - Cloud API options (if acceptable)

3. **PDF Creation**:
   - `pdf` package capabilities
   - Image quality and compression options
   - Multi-page PDF creation best practices

4. **File Picking**:
   - `file_picker` vs `image_picker` for different use cases
   - Multi-selection support

---

## PHASE 3: IMPLEMENTATION PLANNING (TDD)

### 3.1 Implementation Task Hierarchy

**ROOT TASK**: Implement PRD-002: File Conversion Suite

#### 3.2 Phase 1: Setup & Research
- **TASK 1.1**: Research and document conversion library options
  - **TASK 1.1.1**: Research camera and edge detection packages
  - **TASK 1.1.2**: Research DOCX/PPT/XLSX to PDF conversion options
  - **TASK 1.1.3**: Research PDF creation libraries and best practices
  - **TASK 1.1.4**: Document findings and make technology decisions

- **TASK 1.2**: Add required dependencies to pubspec.yaml
  - Add camera, file_picker, pdf packages
  - Add selected conversion packages
  - Run `flutter pub get`

- **TASK 1.3**: Create feature module directory structure
  - Create `lib/features/file_conversion/` with all subdirectories
  - Create placeholder files for all classes

#### 3.3 Phase 2: Domain Layer (TDD)

**TASK 2.1**: Implement Domain Entities (Test-Driven)
- **TASK 2.1.1 [RED]**: Write failing test for ConversionType enum values
- **TASK 2.1.2 [GREEN]**: Implement ConversionType enum
- **TASK 2.1.3 [REFACTOR]**: Review and optimize ConversionType

- **TASK 2.2.1 [RED]**: Write failing test for ScannedPage entity creation and copyWith
- **TASK 2.2.2 [GREEN]**: Implement ScannedPage entity with Freezed
- **TASK 2.2.3 [REFACTOR]**: Review ScannedPage implementation

- **TASK 2.3.1 [RED]**: Write failing test for ConversionRequest entity
- **TASK 2.3.2 [GREEN]**: Implement ConversionRequest entity with Freezed
- **TASK 2.3.3 [REFACTOR]**: Review ConversionRequest implementation

- **TASK 2.4.1 [RED]**: Write failing test for ConversionResult entity
- **TASK 2.4.2 [GREEN]**: Implement ConversionResult entity with Freezed
- **TASK 2.4.3 [REFACTOR]**: Review ConversionResult implementation

**TASK 2.5**: Run code generation for domain entities
- Execute `dart run build_runner build --delete-conflicting-outputs`
- Verify generated files

**TASK 2.6**: Implement Repository Interface
- **TASK 2.6.1**: Define FileConversionRepository abstract class
- **TASK 2.6.2**: Add all required method signatures
- **TASK 2.6.3**: Add documentation comments

**TASK 2.7**: Implement Use Cases (Test-Driven)
- **TASK 2.7.1 [RED]**: Write failing test for ConvertImagesToPdfUseCase
- **TASK 2.7.2 [GREEN]**: Implement ConvertImagesToPdfUseCase
- **TASK 2.7.3 [REFACTOR]**: Review and optimize

- **TASK 2.8.1 [RED]**: Write failing test for ConvertDocxToPdfUseCase
- **TASK 2.8.2 [GREEN]**: Implement ConvertDocxToPdfUseCase
- **TASK 2.8.3 [REFACTOR]**: Review and optimize

- **TASK 2.9.1 [RED]**: Write failing test for ConvertPptToPdfUseCase
- **TASK 2.9.2 [GREEN]**: Implement ConvertPptToPdfUseCase
- **TASK 2.9.3 [REFACTOR]**: Review and optimize

- **TASK 2.10.1 [RED]**: Write failing test for ConvertXlsxToPdfUseCase
- **TASK 2.10.2 [GREEN]**: Implement ConvertXlsxToPdfUseCase
- **TASK 2.10.3 [REFACTOR]**: Review and optimize

- **TASK 2.11.1 [RED]**: Write failing test for ScanDocumentUseCase
- **TASK 2.11.2 [GREEN]**: Implement ScanDocumentUseCase
- **TASK 2.11.3 [REFACTOR]**: Review and optimize

#### 3.4 Phase 3: Data Layer (TDD)

**TASK 3.1**: Implement Data Models (Test-Driven)
- **TASK 3.1.1 [RED]**: Write failing test for ScannedPageModel JSON serialization
- **TASK 3.1.2 [GREEN]**: Implement ScannedPageModel with Freezed + JSON
- **TASK 3.1.3 [REFACTOR]**: Review model implementation

- **TASK 3.2.1 [RED]**: Write failing test for ConversionRequestModel JSON serialization
- **TASK 3.2.2 [GREEN]**: Implement ConversionRequestModel
- **TASK 3.2.3 [REFACTOR]**: Review model implementation

- **TASK 3.3.1 [RED]**: Write failing test for ConversionResultModel JSON serialization
- **TASK 3.3.2 [GREEN]**: Implement ConversionResultModel
- **TASK 3.3.3 [REFACTOR]**: Review model implementation

**TASK 3.4**: Run code generation for data models
- Execute `dart run build_runner build --delete-conflicting-outputs`
- Verify JSON serialization code

**TASK 3.5**: Implement Camera Data Source (Test-Driven)
- **TASK 3.5.1 [RED]**: Write failing test for camera initialization
- **TASK 3.5.2 [GREEN]**: Implement camera initialization in CameraDataSource
- **TASK 3.5.3 [REFACTOR]**: Review implementation

- **TASK 3.6.1 [RED]**: Write failing test for image capture
- **TASK 3.6.2 [GREEN]**: Implement captureImage method
- **TASK 3.6.3 [REFACTOR]**: Optimize capture logic

- **TASK 3.7.1 [RED]**: Write failing test for edge detection
- **TASK 3.7.2 [GREEN]**: Implement edge detection integration
- **TASK 3.7.3 [REFACTOR]**: Optimize edge detection

**TASK 3.8**: Implement File Picker Data Source (Test-Driven)
- **TASK 3.8.1 [RED]**: Write failing test for image picking
- **TASK 3.8.2 [GREEN]**: Implement pickImages method
- **TASK 3.8.3 [REFACTOR]**: Review implementation

- **TASK 3.9.1 [RED]**: Write failing test for document picking with filters
- **TASK 3.9.2 [GREEN]**: Implement pickDocument method
- **TASK 3.9.3 [REFACTOR]**: Review and optimize

**TASK 3.10**: Implement PDF Converter Data Source (Test-Driven)
- **TASK 3.10.1 [RED]**: Write failing test for images to PDF conversion
- **TASK 3.10.2 [GREEN]**: Implement convertImagesToPdf method
- **TASK 3.10.3 [REFACTOR]**: Optimize PDF generation

- **TASK 3.11.1 [RED]**: Write failing test for DOCX to PDF conversion
- **TASK 3.11.2 [GREEN]**: Implement convertDocxToPdf method
- **TASK 3.11.3 [REFACTOR]**: Review implementation

- **TASK 3.12.1 [RED]**: Write failing test for PPT to PDF conversion
- **TASK 3.12.2 [GREEN]**: Implement convertPptToPdf method
- **TASK 3.12.3 [REFACTOR]**: Review implementation

- **TASK 3.13.1 [RED]**: Write failing test for XLSX to PDF conversion
- **TASK 3.13.2 [GREEN]**: Implement convertXlsxToPdf method
- **TASK 3.13.3 [REFACTOR]**: Review implementation

- **TASK 3.14.1 [RED]**: Write failing test for image filter application
- **TASK 3.14.2 [GREEN]**: Implement applyFiltersToImage method (grayscale, B&W)
- **TASK 3.14.3 [REFACTOR]**: Optimize filter algorithms

**TASK 3.15**: Implement Repository Implementation (Test-Driven)
- **TASK 3.15.1 [RED]**: Write failing test for FileConversionRepositoryImpl initialization
- **TASK 3.15.2 [GREEN]**: Implement repository constructor with data sources
- **TASK 3.15.3 [REFACTOR]**: Review dependency injection

- **TASK 3.16.1 [RED]**: Write failing test for captureImages with error handling
- **TASK 3.16.2 [GREEN]**: Implement captureImages method with Either return
- **TASK 3.16.3 [REFACTOR]**: Review error mapping

- **TASK 3.17.1 [RED]**: Write failing test for convertImagesToPdf with error handling
- **TASK 3.17.2 [GREEN]**: Implement convertImagesToPdf method
- **TASK 3.17.3 [REFACTOR]**: Review implementation

- **TASK 3.18.1 [RED]**: Write failing tests for all document conversion methods
- **TASK 3.18.2 [GREEN]**: Implement convertDocxToPdf, convertPptToPdf, convertXlsxToPdf
- **TASK 3.18.3 [REFACTOR]**: Review and deduplicate code

- **TASK 3.19.1 [RED]**: Write failing test for file picker methods
- **TASK 3.19.2 [GREEN]**: Implement pickImages and pickDocument methods
- **TASK 3.19.3 [REFACTOR]**: Review implementation

#### 3.5 Phase 4: Core Error Handling

**TASK 4.1**: Add Conversion-Specific Failures
- **TASK 4.1.1 [RED]**: Write failing test for CameraFailure
- **TASK 4.1.2 [GREEN]**: Add CameraFailure class to core/error/failures.dart
- **TASK 4.1.3 [REFACTOR]**: Review implementation

- **TASK 4.2.1 [RED]**: Write failing tests for ConversionFailure, FilePickerFailure, StorageFailure
- **TASK 4.2.2 [GREEN]**: Add all failure classes
- **TASK 4.2.3 [REFACTOR]**: Review and document failure types

#### 3.6 Phase 5: Presentation Layer (TDD)

**TASK 5.1**: Create Riverpod Providers
- **TASK 5.1.1**: Create data source providers (camera, file picker, PDF converter)
- **TASK 5.1.2**: Create repository provider
- **TASK 5.1.3**: Create use case providers for all conversion types
- **TASK 5.1.4**: Run code generation for providers

**TASK 5.2**: Implement Scan Notifier (Test-Driven)
- **TASK 5.2.1 [RED]**: Write failing test for ScanNotifier initial state
- **TASK 5.2.2 [GREEN]**: Implement ScanNotifier build method
- **TASK 5.2.3 [REFACTOR]**: Review implementation

- **TASK 5.3.1 [RED]**: Write failing test for captureImage method
- **TASK 5.3.2 [GREEN]**: Implement captureImage with AsyncValue handling
- **TASK 5.3.3 [REFACTOR]**: Review state management

- **TASK 5.4.1 [RED]**: Write failing test for applyFilter method
- **TASK 5.4.2 [GREEN]**: Implement applyFilter method
- **TASK 5.4.3 [REFACTOR]**: Optimize filter application

- **TASK 5.5.1 [RED]**: Write failing test for finalizeScan method
- **TASK 5.5.2 [GREEN]**: Implement finalizeScan with conversion logic
- **TASK 5.5.3 [REFACTOR]**: Review implementation

**TASK 5.6**: Implement Image Conversion Notifier (Test-Driven)
- **TASK 5.6.1 [RED]**: Write failing test for ImageConversionNotifier
- **TASK 5.6.2 [GREEN]**: Implement notifier with image selection and conversion
- **TASK 5.6.3 [REFACTOR]**: Review implementation

**TASK 5.7**: Implement Document Conversion Notifier (Test-Driven)
- **TASK 5.7.1 [RED]**: Write failing tests for all document conversion methods
- **TASK 5.7.2 [GREEN]**: Implement convertDocx, convertPpt, convertXlsx methods
- **TASK 5.7.3 [REFACTOR]**: Review and deduplicate logic

**TASK 5.8**: Run code generation for notifiers
- Execute `dart run build_runner build --delete-conflicting-outputs`
- Verify all notifier providers are generated

#### 3.7 Phase 6: UI Implementation (Widget Tests)

**TASK 6.1**: Implement Reusable Widgets (Test-Driven)
- **TASK 6.1.1 [RED]**: Write failing widget test for CameraControlsWidget
- **TASK 6.1.2 [GREEN]**: Implement CameraControlsWidget UI
- **TASK 6.1.3 [REFACTOR]**: Review widget implementation

- **TASK 6.2.1 [RED]**: Write failing widget test for EdgeDetectionOverlay
- **TASK 6.2.2 [GREEN]**: Implement EdgeDetectionOverlay with CustomPaint
- **TASK 6.2.3 [REFACTOR]**: Optimize rendering

- **TASK 6.3.1 [RED]**: Write failing widget test for PageFilterSelector
- **TASK 6.3.2 [GREEN]**: Implement PageFilterSelector with chips
- **TASK 6.3.3 [REFACTOR]**: Review UI/UX

- **TASK 6.4.1 [RED]**: Write failing widget test for PageReorderWidget
- **TASK 6.4.2 [GREEN]**: Implement PageReorderWidget with ReorderableListView
- **TASK 6.4.3 [REFACTOR]**: Optimize drag-drop interactions

- **TASK 6.5.1 [RED]**: Write failing widget test for ConversionProgressWidget
- **TASK 6.5.2 [GREEN]**: Implement ConversionProgressWidget
- **TASK 6.5.3 [REFACTOR]**: Review progress display

**TASK 6.6**: Implement Scan Camera Screen (Test-Driven)
- **TASK 6.6.1 [RED]**: Write failing widget test for ScanCameraScreen layout
- **TASK 6.6.2 [GREEN]**: Implement ScanCameraScreen UI structure
- **TASK 6.6.3 [REFACTOR]**: Review screen implementation

- **TASK 6.7.1 [RED]**: Write failing test for camera preview integration
- **TASK 6.7.2 [GREEN]**: Integrate CameraPreview widget
- **TASK 6.7.3 [REFACTOR]**: Optimize camera rendering

- **TASK 6.8.1 [RED]**: Write failing test for edge detection overlay integration
- **TASK 6.8.2 [GREEN]**: Integrate EdgeDetectionOverlay
- **TASK 6.8.3 [REFACTOR]**: Review overlay performance

- **TASK 6.9.1 [RED]**: Write failing test for capture button interaction
- **TASK 6.9.2 [GREEN]**: Connect capture button to ScanNotifier
- **TASK 6.9.3 [REFACTOR]**: Review user interaction

**TASK 6.10**: Implement Scan Review Screen (Test-Driven)
- **TASK 6.10.1 [RED]**: Write failing widget test for ScanReviewScreen layout
- **TASK 6.10.2 [GREEN]**: Implement ScanReviewScreen UI with page grid
- **TASK 6.10.3 [REFACTOR]**: Review screen structure

- **TASK 6.11.1 [RED]**: Write failing test for filter application UI
- **TASK 6.11.2 [GREEN]**: Integrate PageFilterSelector for each page
- **TASK 6.11.3 [REFACTOR]**: Review filter UX

- **TASK 6.12.1 [RED]**: Write failing test for page rotation controls
- **TASK 6.12.2 [GREEN]**: Implement rotation buttons and connect to notifier
- **TASK 6.12.3 [REFACTOR]**: Review rotation interaction

- **TASK 6.13.1 [RED]**: Write failing test for finalize button
- **TASK 6.13.2 [GREEN]**: Connect finalize button to ScanNotifier.finalizeScan
- **TASK 6.13.3 [REFACTOR]**: Review finalization flow

**TASK 6.14**: Implement Image Conversion Screen (Test-Driven)
- **TASK 6.14.1 [RED]**: Write failing widget test for ImageConversionScreen
- **TASK 6.14.2 [GREEN]**: Implement ImageConversionScreen UI
- **TASK 6.14.3 [REFACTOR]**: Review screen implementation

- **TASK 6.15.1 [RED]**: Write failing test for image reordering
- **TASK 6.15.2 [GREEN]**: Integrate PageReorderWidget
- **TASK 6.15.3 [REFACTOR]**: Review reorder UX

- **TASK 6.16.1 [RED]**: Write failing test for orientation selector
- **TASK 6.16.2 [GREEN]**: Implement orientation selector UI
- **TASK 6.16.3 [REFACTOR]**: Review selector implementation

- **TASK 6.17.1 [RED]**: Write failing test for convert button
- **TASK 6.17.2 [GREEN]**: Connect convert button to ImageConversionNotifier
- **TASK 6.17.3 [REFACTOR]**: Review conversion flow

**TASK 6.18**: Implement Document Conversion Screen (Test-Driven)
- **TASK 6.18.1 [RED]**: Write failing widget test for DocumentConversionScreen
- **TASK 6.18.2 [GREEN]**: Implement DocumentConversionScreen generic UI
- **TASK 6.18.3 [REFACTOR]**: Review screen implementation

- **TASK 6.19.1 [RED]**: Write failing test for file details display
- **TASK 6.19.2 [GREEN]**: Display selected file name and size
- **TASK 6.19.3 [REFACTOR]**: Review file info display

- **TASK 6.20.1 [RED]**: Write failing test for convert button
- **TASK 6.20.2 [GREEN]**: Connect convert button to DocumentConversionNotifier
- **TASK 6.20.3 [REFACTOR]**: Review conversion trigger

- **TASK 6.21.1 [RED]**: Write failing test for progress indicator
- **TASK 6.21.2 [GREEN]**: Integrate ConversionProgressWidget
- **TASK 6.21.3 [REFACTOR]**: Review progress display

**TASK 6.22**: Implement Error Display (Test-Driven)
- **TASK 6.22.1 [RED]**: Write failing test for error message display on all screens
- **TASK 6.22.2 [GREEN]**: Add error display using SelectableText.rich
- **TASK 6.22.3 [REFACTOR]**: Review error UX

#### 3.8 Phase 7: Navigation Integration

**TASK 7.1**: Update App Router
- **TASK 7.1.1 [RED]**: Write failing navigation test for scan route
- **TASK 7.1.2 [GREEN]**: Add /scan-document route to app_router.dart
- **TASK 7.1.3 [REFACTOR]**: Review route configuration

- **TASK 7.2.1 [RED]**: Write failing navigation test for scan review route
- **TASK 7.2.2 [GREEN]**: Add /scan-review route
- **TASK 7.2.3 [REFACTOR]**: Review navigation flow

- **TASK 7.3.1 [RED]**: Write failing navigation tests for conversion routes
- **TASK 7.3.2 [GREEN]**: Add /convert-images and /convert-document routes
- **TASK 7.3.3 [REFACTOR]**: Review route parameters

**TASK 7.4**: Implement Navigation from Discover Screen
- **TASK 7.4.1**: Add "Scan Document" button to Discover screen
- **TASK 7.4.2**: Add "Images to PDF" button to Discover screen
- **TASK 7.4.3**: Add "DOCX to PDF", "PPT to PDF", "XLSX to PDF" buttons
- **TASK 7.4.4**: Test navigation flow from Discover

#### 3.9 Phase 8: Permissions Handling

**TASK 8.1**: Add Camera Permission
- **TASK 8.1.1 [RED]**: Write failing test for camera permission check
- **TASK 8.1.2 [GREEN]**: Implement camera permission request before opening camera
- **TASK 8.1.3 [REFACTOR]**: Review permission flow

- **TASK 8.2.1 [RED]**: Write failing test for permission denial handling
- **TASK 8.2.2 [GREEN]**: Handle permission denial with user-friendly message
- **TASK 8.2.3 [REFACTOR]**: Review error messaging

**TASK 8.3**: Update Android Manifest
- **TASK 8.3.1**: Add CAMERA permission to AndroidManifest.xml
- **TASK 8.3.2**: Verify manifest configuration

#### 3.10 Phase 9: Integration Testing

**TASK 9.1**: Create Integration Test Suite
- **TASK 9.1.1 [RED]**: Write failing integration test for complete scan flow
- **TASK 9.1.2 [GREEN]**: Implement full scan → review → convert → save flow
- **TASK 9.1.3 [REFACTOR]**: Review test coverage

- **TASK 9.2.1 [RED]**: Write failing integration test for image conversion flow
- **TASK 9.2.2 [GREEN]**: Implement full image selection → convert → save flow
- **TASK 9.2.3 [REFACTOR]**: Review test coverage

- **TASK 9.3.1 [RED]**: Write failing integration test for document conversion flow
- **TASK 9.3.2 [GREEN]**: Implement full document selection → convert → save flow
- **TASK 9.3.3 [REFACTOR]**: Review test coverage

- **TASK 9.4.1 [RED]**: Write failing integration test for error scenarios
- **TASK 9.4.2 [GREEN]**: Test permission denial, conversion failure, storage errors
- **TASK 9.4.3 [REFACTOR]**: Review error handling coverage

#### 3.11 Phase 10: Final Validation & Documentation

**TASK 10.1**: Run All Tests
- **TASK 10.1.1**: Execute `flutter test` and ensure all tests pass
- **TASK 10.1.2**: Review test coverage report
- **TASK 10.1.3**: Add missing tests if coverage < 80%

**TASK 10.2**: Code Quality Checks
- **TASK 10.2.1**: Run `flutter analyze` and fix all issues
- **TASK 10.2.2**: Run `dart format .` to format all code
- **TASK 10.2.3**: Review and remove any unused imports

**TASK 10.3**: Verify EARS Requirements
- **TASK 10.3.1**: Create requirements traceability matrix
- **TASK 10.3.2**: Verify each EARS requirement has corresponding tests
- **TASK 10.3.3**: Confirm all requirements are implemented

**TASK 10.4**: Documentation
- **TASK 10.4.1**: Add inline documentation to all public APIs
- **TASK 10.4.2**: Update README if needed (only if explicitly required)
- **TASK 10.4.3**: Document known limitations or future improvements

**TASK 10.5**: Manual Testing on Device
- **TASK 10.5.1**: Test scan flow on physical Android device
- **TASK 10.5.2**: Test all conversion types with various file sizes
- **TASK 10.5.3**: Test edge cases (permission denial, low storage, large files)
- **TASK 10.5.4**: Verify performance meets NFR requirements (>15 FPS, <2s feedback)

---

## 4. REQUIREMENTS TRACEABILITY MATRIX

| EARS Requirement | Use Case | Repository Method | Test File | Status |
|------------------|----------|-------------------|-----------|--------|
| REQ-2.1.1 | ScanDocumentUseCase | captureImages | scan_notifier_test.dart | Planned |
| REQ-2.1.2 | ScanDocumentUseCase | captureImages | camera_data_source_test.dart | Planned |
| REQ-2.1.3 | ScanDocumentUseCase | captureImages | scan_camera_screen_test.dart | Planned |
| REQ-2.1.4 | ScanDocumentUseCase | applyFiltersToImage | scan_review_screen_test.dart | Planned |
| REQ-2.1.5 | ScanDocumentUseCase | captureImages | scan_notifier_test.dart | Planned |
| REQ-2.1.6 | ScanDocumentUseCase | convertImagesToPdf | scan_notifier_test.dart | Planned |
| REQ-2.1.7 | ScanDocumentUseCase | convertImagesToPdf | pdf_converter_data_source_test.dart | Planned |
| REQ-2.1.8 | ScanDocumentUseCase | convertImagesToPdf | scan_review_screen_test.dart | Planned |
| REQ-2.2.1 | ConvertDocxToPdfUseCase | pickDocument | file_picker_data_source_test.dart | Planned |
| REQ-2.2.2 | ConvertDocxToPdfUseCase | convertDocxToPdf | document_conversion_screen_test.dart | Planned |
| REQ-2.2.3 | ConvertDocxToPdfUseCase | convertDocxToPdf | pdf_converter_data_source_test.dart | Planned |
| REQ-2.2.4 | ConvertDocxToPdfUseCase | convertDocxToPdf | document_conversion_notifier_test.dart | Planned |
| REQ-2.2.5 | ConvertDocxToPdfUseCase | convertDocxToPdf | document_conversion_notifier_test.dart | Planned |
| REQ-2.2.6 | ConvertDocxToPdfUseCase | convertDocxToPdf | document_conversion_screen_test.dart | Planned |
| REQ-2.3.1 | ConvertImagesToPdfUseCase | pickImages | file_picker_data_source_test.dart | Planned |
| REQ-2.3.2 | ConvertImagesToPdfUseCase | pickImages | image_conversion_screen_test.dart | Planned |
| REQ-2.3.3 | ConvertImagesToPdfUseCase | N/A (UI only) | page_reorder_widget_test.dart | Planned |
| REQ-2.3.4 | ConvertImagesToPdfUseCase | convertImagesToPdf | image_conversion_screen_test.dart | Planned |
| REQ-2.3.5 | ConvertImagesToPdfUseCase | convertImagesToPdf | pdf_converter_data_source_test.dart | Planned |
| REQ-2.3.6 | ConvertImagesToPdfUseCase | convertImagesToPdf | image_conversion_notifier_test.dart | Planned |
| REQ-2.3.7 | ConvertImagesToPdfUseCase | convertImagesToPdf | image_conversion_screen_test.dart | Planned |
| REQ-2.4.1 | ConvertPptToPdfUseCase | pickDocument | file_picker_data_source_test.dart | Planned |
| REQ-2.4.2 | ConvertXlsxToPdfUseCase | pickDocument | file_picker_data_source_test.dart | Planned |
| REQ-2.4.3 | All conversion use cases | All convert methods | Integration tests | Planned |
| REQ-2.NF.1 | All use cases | N/A (UI) | All screen tests | Planned |
| REQ-2.NF.2 | All use cases | All convert methods | Repository tests | Planned |
| REQ-2.NF.3 | All use cases | All convert methods | pdf_converter_data_source_test.dart | Planned |
| REQ-2.NF.4 | ScanDocumentUseCase | captureImages | Performance test (manual) | Planned |

---

## 5. TECHNOLOGY DECISIONS (TO BE FINALIZED IN TASK 1.1)

### Pending Research:
1. **Camera & Edge Detection**: Choose between google_mlkit_document_scanner, edge_detection, cunning_document_scanner
2. **Office Conversion**: Determine DOCX/PPT/XLSX to PDF approach (platform channel vs package vs API)
3. **PDF Library**: Confirm `pdf` package capabilities for all conversion requirements

### Known Packages:
- `camera`: Official Flutter camera plugin ✓
- `file_picker`: File selection ✓
- `image_picker`: Image selection ✓
- `pdf`: PDF creation and manipulation ✓
- `image`: Image processing for filters ✓

---

## 6. SIMPLICITY VALIDATION CHECKLIST

- [ ] All planned components are required by Feature-First Clean Architecture ✓
- [ ] No unnecessary abstractions beyond architecture requirements ✓
- [ ] Each class has a single, clear responsibility ✓
- [ ] Use cases are simple delegators to repository ✓
- [ ] Notifiers contain only state management logic ✓
- [ ] Widgets are small and focused ✓
- [ ] Error handling uses established patterns (Either/AsyncValue) ✓
- [ ] No premature optimization in design ✓
- [ ] Dependencies are minimal and justified ✓
- [ ] Junior developer can understand the structure ✓

---

## 7. NOTES FOR IMPLEMENTATION

1. **Start with Research (Task 1.1)**: The technology choices for camera/edge detection and office conversion will significantly impact implementation details. This must be completed first.

2. **TDD Discipline**: Every implementation task follows Red-Green-Refactor. Do not skip writing tests first.

3. **Code Generation**: After creating/modifying Freezed models or Riverpod providers, always run build_runner.

4. **Permission Handling**: Camera permission must be checked before accessing camera. Graceful degradation if denied.

5. **File Management**: Converted files should be saved in organized folders (Scanned/, Converted/) with proper naming conventions.

6. **Progress Feedback**: All operations >2s must show progress indicators (REQ-2.NF.1).

7. **Error Recovery**: All errors must be user-friendly and suggest corrective actions.

8. **Performance**: Edge detection must maintain >15 FPS (REQ-2.NF.4) - may require optimization or lower-resolution processing.

---

## SUMMARY

This implementation plan provides a complete, test-driven roadmap for PRD-002 (File Conversion Suite) following Feature-First Clean Architecture. The plan:

- ✅ Adheres to the established architecture (non-negotiable)
- ✅ Applies TDD methodology with Red-Green-Refactor cycles
- ✅ Maintains simplicity within architectural boundaries
- ✅ Provides full traceability from EARS requirements to tests
- ✅ Follows KISS and YAGNI principles
- ✅ Creates a hierarchical task structure ready for execution

**Next Steps**:
1. User review and approval of this plan
2. Begin implementation with Task 1.1 (Research)
3. Follow the task hierarchy sequentially
4. Update task status as work progresses
