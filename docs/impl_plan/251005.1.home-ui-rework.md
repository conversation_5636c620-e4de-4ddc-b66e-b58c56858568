I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

The current home screen has a simple structure with basic AppBar, permission handling, and document list. The JSON specification requires significant UI enhancements including:
- Updated AppBar with menu and crown icons
- TabBar-like appearance for document type filtering (but using FilterChips for performance)
- A banner about File Manager
- Updated permission request widget with illustration
- Always-visible FloatingActionButton
- 4-item BottomNavigationBar (currently only 2 items in AppScaffold)
- Ad banner footer

The codebase has Recent use cases implemented but no screens. Favorite functionality doesn't exist. No assets are currently defined, and no ad SDK is integrated. The existing FilterChip implementation performs well and should be retained but styled to look like a TabBar with underline indicator.


### Approach

The implementation will be split into modular, reusable widgets following the existing architecture patterns:

1. **Create new reusable widgets**: FileManagerBanner, AdBannerPlaceholder, and update PermissionRequestWidget
2. **Refactor FileTypeTabs**: Keep FilterChips for performance but style them to look like TabBar with animated underline indicator
3. **Refactor HomeScreen**: Update AppBar styling, integrate banner and ad footer, adjust layout structure
4. **Create placeholder screens**: RecentScreen and FavoriteScreen for navigation
5. **Update navigation**: Extend AppScaffold to support 4 bottom nav items with badge support, add new routes
6. **Add assets**: Define assets section in pubspec.yaml for future illustrations

This approach maintains the performance benefits of FilterChips while achieving the TabBar visual appearance through custom styling and an animated underline indicator.


### Reasoning

I explored the project structure and identified this as a Flutter PDF reader app using clean architecture with Riverpod for state management. I examined the current `HomeScreen` implementation and found it uses FilterChips for tabs, which the user wants to keep for performance reasons. I checked the navigation structure in `AppScaffold` and `app_router.dart`, discovering only 2 bottom nav items exist (Home, Discover). I verified that Recent use cases exist but Favorite doesn't, confirmed no assets are defined, and found the Document entity structure. The user requested to keep FilterChips but style them to look like a TabBar with underline indicator.


## Proposed File Changes

### lib/features/file_discovery/presentation/widgets/file_manager_banner.dart(NEW)

Create a new widget `FileManagerBanner` that displays an informational banner with:
- A leading folder icon (Icons.folder)
- Text: "You can use File Manager to open PDF files"
- A trailing "Go" button (ElevatedButton or TextButton styled as primary)
- Use Material 3 styling with appropriate padding and colors
- Accept an `onGoPressed` callback parameter for the button action
- Use a Container or Card with horizontal layout (Row) for the banner structure
- Add appropriate margins (horizontal: 16, vertical: 8) to match the existing UI spacing
- Make the banner dismissible optionally (can be enhanced later)

### lib/core/widgets/ad_banner_placeholder.dart(NEW)

Create a placeholder widget `AdBannerPlaceholder` for the persistent footer ad banner:
- Display a Container with fixed height (50-60px)
- Use a light gray background color with border on top
- Show centered text "Ad Space" or "Advertisement" in a muted color
- Add a comment indicating this is a placeholder for future ad SDK integration
- Make it visually distinct but non-intrusive
- Accept optional height parameter with default value
- This widget will be placed in the core/widgets directory since it's app-wide functionality

### lib/features/file_discovery/presentation/widgets/permission_request_widget.dart(MODIFY)

References: 

- lib/features/file_discovery/presentation/screens/home_screen.dart(MODIFY)

Update the `PermissionRequestWidget` to match the JSON specification:
- Replace the current storage icon with a placeholder for `illustration_person_with_clipboard` (use Icons.assignment_ind or similar as temporary replacement until asset is added)
- Update the title text to: "To read and edit your files, please allow All PDF Reader to access all your files."
- Remove the "Storage Permission Required" heading
- Replace the two-button layout with a single full-width "Allow" primary button
- Remove the "Select a File" outlined button (this functionality will be handled by FAB)
- Update the `onPermissionRequested` callback to be triggered by the Allow button
- Remove the `onManualFileSelect` parameter since it's no longer needed
- Adjust spacing and alignment to center the content vertically
- Ensure the button has `isFullWidth: true` styling (use SizedBox with double.infinity width)
- Keep the responsive layout logic but simplify the UI structure

### lib/features/file_discovery/presentation/widgets/file_type_tabs.dart(MODIFY)

References: 

- lib/features/file_discovery/presentation/screens/home_screen.dart(MODIFY)

Refactor `FileTypeTabs` to keep FilterChips but style them to look like a TabBar with underline indicator:

**Keep existing structure:**
- Maintain the FilterChip-based implementation for performance
- Keep the `selectedType` and `onTypeSelected` parameters
- Keep the horizontal ListView structure

**Update FilterChip styling to look like tabs:**
- Remove the rounded border and background colors from FilterChips
- Set `backgroundColor: Colors.transparent` for unselected chips
- Set `selectedColor: Colors.transparent` for selected chips
- Remove the checkmark icon by setting `showCheckmark: false`
- Use text color changes instead: selected tabs use primary color, unselected use grey
- Remove padding and shape customization to make chips look like plain text labels
- Set `side: BorderSide.none` to remove borders

**Add animated underline indicator:**
- Wrap the ListView in a Stack
- Add a Positioned widget at the bottom for the underline indicator
- Use AnimatedPositioned or AnimatedContainer to animate the indicator position
- Calculate indicator position based on selected tab index
- The indicator should be a Container with:
  - Height: 2-3px
  - Width: approximately the width of the selected tab text
  - Color: theme primary color
  - Positioned at the bottom of the tab area
- Use GlobalKey or LayoutBuilder to measure tab widths for accurate indicator positioning
- Animate the indicator's horizontal position when selection changes (duration: 200-300ms)

**Layout adjustments:**
- Maintain the horizontal scrolling capability
- Keep appropriate spacing between tabs (8-16px)
- Ensure the indicator animates smoothly between tabs
- The overall height should remain around 48-50px to match TabBar height

This approach provides TabBar-like appearance with FilterChip performance benefits.

### lib/features/file_discovery/presentation/screens/home_screen.dart(MODIFY)

References: 

- lib/features/file_discovery/presentation/widgets/file_type_tabs.dart(MODIFY)
- lib/features/file_discovery/presentation/widgets/permission_request_widget.dart(MODIFY)
- lib/features/file_discovery/presentation/widgets/file_manager_banner.dart(NEW)
- lib/core/widgets/ad_banner_placeholder.dart(NEW)
- lib/features/file_discovery/presentation/widgets/document_list_item.dart

Refactor `HomeScreen` to match the JSON specification while keeping the FilterChip-based tabs:

**AppBar updates:**
- Add `leading: IconButton(icon: Icon(Icons.menu), onPressed: () {})` for menu icon
- Change title from 'Documents' to 'PDF READER'
- Add `actions: [IconButton(icon: Icon(Icons.workspace_premium), onPressed: () {})]` for crown icon (using workspace_premium as crown)

**State management:**
- Keep the existing `_selectedType` state variable
- Keep the existing FilterChip-based tab selection logic (no TabController needed)
- Maintain the `_scanInitialized` flag and scan management logic

**Body structure updates:**
- Restructure the body to show: FileTypeTabs, FileManagerBanner, then ConditionalView
- The TabBar and Banner should be visible regardless of permission state
- Only the content area (permission request vs file list) should be conditional

**Layout structure:**
- Wrap the body in a Column containing:
  1. FileTypeTabs widget (with updated styling from the refactored widget)
  2. FileManagerBanner widget
  3. Expanded widget containing the conditional view (permission request or document list)

**Banner integration:**
- Add the new `FileManagerBanner` widget below the FileTypeTabs
- Pass `_selectFileAndNavigate` as the onGoPressed callback
- Show the banner always (or conditionally based on permission state if preferred)

**Conditional view updates:**
- Keep the existing permission check logic using `permissionProvider`
- When permission denied: show updated `PermissionRequestWidget` (remove the onManualFileSelect parameter)
- When permission granted: show the existing `_buildDocumentList()` method

**FloatingActionButton updates:**
- Remove the conditional logic that only shows FAB when no permission
- Always show the FAB with the add icon
- Keep the existing `_selectFileAndNavigate` callback
- Position at bottomRight (default)

**Document list updates:**
- Keep the existing `_buildDocumentList()` method structure
- Remove the FileTypeTabs from inside this method since it's now at the top level
- The RefreshIndicator wrapping should now only wrap the ListView, not the tabs
- Keep loading states, error handling, and empty state logic
- The Expanded widget should contain just the document list content

**Optional footer:**
- Consider adding the `AdBannerPlaceholder` at the bottom of the Column
- Ensure it doesn't conflict with BottomNavigationBar spacing
- This can be added as a fixed-height widget at the bottom of the body Column

**Maintain existing functionality:**
- Keep AutomaticKeepAliveClientMixin for performance
- Keep the scan initialization logic
- Keep document filtering via `filteredDocumentListProvider`
- Keep navigation logic in `_openDocument` method
- Keep error handling in `_selectFileAndNavigate`

The key change is restructuring the layout hierarchy while maintaining the existing state management and FilterChip-based tabs.

### lib/features/file_discovery/presentation/screens/recent_screen.dart(NEW)

References: 

- lib/features/file_discovery/domain/usecases/get_recent_documents.dart
- lib/features/file_discovery/presentation/widgets/document_list_item.dart
- lib/features/file_discovery/presentation/screens/home_screen.dart(MODIFY)

Create a placeholder `RecentScreen` for the Recent tab in bottom navigation:
- Extend ConsumerWidget for Riverpod integration
- Build a Scaffold with AppBar titled "Recent"
- Use the existing `GetRecentDocuments` use case from `lib/features/file_discovery/domain/usecases/get_recent_documents.dart`
- Create a provider that calls the use case and returns AsyncValue<List<Document>>
- Display the recent documents list using the existing `DocumentListItem` widget from `lib/features/file_discovery/presentation/widgets/document_list_item.dart`
- Handle loading, error, and empty states similar to `HomeScreen`
- Reuse the `_openDocument` navigation logic from HomeScreen (consider extracting to a shared utility)
- Add a RefreshIndicator for pull-to-refresh
- Show a message like "No recent documents" when the list is empty
- This screen will display documents from the recent documents storage (currently returns empty list, but infrastructure is ready)
- Add TODO comments indicating this uses the existing recent documents infrastructure

### lib/features/file_discovery/presentation/screens/favorite_screen.dart(NEW)

Create a placeholder `FavoriteScreen` for the Favorite tab in bottom navigation:
- Extend ConsumerWidget for Riverpod integration
- Build a Scaffold with AppBar titled "Favorites"
- For now, display a centered placeholder UI with:
  - An icon (Icons.favorite_border) sized 80px
  - Title text: "Favorites"
  - Subtitle text: "Mark documents as favorites to see them here"
  - Use theme colors for styling
- Add TODO comments indicating this feature needs to be implemented:
  - Domain layer: Favorite entity, repository, use cases (add/remove/get favorites)
  - Data layer: Local storage for favorites (SharedPreferences or SQLite)
  - Presentation layer: Provider for favorites list, UI for marking/unmarking
- This placeholder allows the navigation to work while the feature is built incrementally
- Consider adding a "Coming Soon" badge or similar visual indicator

### lib/features/file_discovery/presentation/providers/file_discovery_providers.dart(MODIFY)

References: 

- lib/features/file_discovery/domain/usecases/get_recent_documents.dart
- lib/features/file_discovery/presentation/screens/recent_screen.dart(NEW)

Add a new provider for recent documents to support the RecentScreen:
- Create `recentDocumentsProvider` that returns `AsyncValue<List<Document>>`
- Use the existing `GetRecentDocuments` use case from the providers file
- Call `getRecentDocuments.call()` and handle the Either result
- Map the result to AsyncValue (data, loading, error states)
- This provider will be used by the new `RecentScreen`
- Follow the same pattern as the existing `documentListProvider` for consistency
- Add appropriate error handling and logging

### lib/core/navigation/app_scaffold.dart(MODIFY)

References: 

- lib/core/navigation/app_router.dart(MODIFY)

Update `AppScaffold` to support 4 bottom navigation items instead of 2:

**NavigationBar destinations:**
- Update the destinations list to include 4 items: Home, Recent, Favorite, Discover
- Home: Keep existing (Icons.home_outlined / Icons.home, label: 'Home')
- Recent: Add new (Icons.history / Icons.history, label: 'Recent')
- Favorite: Add new (Icons.favorite_border / Icons.favorite, label: 'Favorite')
- Discover: Keep existing but add badge (Icons.travel_explore_outlined / Icons.travel_explore, label: 'Discover')

**Badge implementation for Discover:**
- Wrap the Discover icon in a Badge widget with label "NEW"
- Use `Badge(label: Text('NEW'), child: Icon(...))` for both outlined and selected icons
- Style the badge with small text and appropriate colors from theme

**Navigation logic updates:**
- Update `_onDestinationSelected` to handle 4 branches instead of 2
- Keep the existing scan cancellation logic when leaving Home (index 0)
- Update index checks to account for new indices: 0=Home, 1=Recent, 2=Favorite, 3=Discover
- Ensure the navigationShell.goBranch calls work correctly with 4 branches

**Performance considerations:**
- Keep the existing performance logging and timeline tasks
- The pre-warming logic for Discover should now check index 3 instead of 1

This update maintains the existing navigation performance optimizations while expanding to support the full bottom navigation structure.

### lib/core/navigation/app_router.dart(MODIFY)

References: 

- lib/features/file_discovery/presentation/screens/recent_screen.dart(NEW)
- lib/features/file_discovery/presentation/screens/favorite_screen.dart(NEW)
- lib/core/navigation/app_scaffold.dart(MODIFY)

Extend the router configuration to support 4 navigation branches:

**Add new imports:**
- Import the new `RecentScreen` from `lib/features/file_discovery/presentation/screens/recent_screen.dart`
- Import the new `FavoriteScreen` from `lib/features/file_discovery/presentation/screens/favorite_screen.dart`

**Update StatefulShellRoute branches:**
- Keep the existing Home branch (index 0) with all its child routes unchanged
- Add a new Recent branch (index 1) with route path '/recent' pointing to RecentScreen
- Add a new Favorite branch (index 2) with route path '/favorite' pointing to FavoriteScreen
- Move the existing Discover branch to index 3 (was index 1)

**Branch structure:**
- Branch 0: Home (/) - existing with all viewer sub-routes
- Branch 1: Recent (/recent) - new, simple route to RecentScreen
- Branch 2: Favorite (/favorite) - new, simple route to FavoriteScreen  
- Branch 3: Discover (/discover) - existing, moved from index 1

**Maintain existing routes:**
- Keep all the standalone routes (file-info, scan-document, convert-*, merge-pdf, etc.) unchanged
- Keep the error builder unchanged
- Ensure the splash screen initial location remains '/splash'

This configuration allows the StatefulShellRoute to manage 4 branches with proper state preservation for each tab, following the existing pattern established for Home and Discover.

### pubspec.yaml(MODIFY)

References: 

- lib/features/file_discovery/presentation/widgets/permission_request_widget.dart(MODIFY)

Uncomment and configure the assets section to prepare for future image assets:

**Assets configuration:**
- Uncomment the `assets:` section in the flutter configuration
- Add a placeholder path for images: `- assets/images/`
- Add a comment indicating that `illustration_person_with_clipboard.png` should be placed here
- This allows the app to reference assets even if they're not immediately available

**Directory structure note:**
- Add a comment suggesting to create the `assets/images/` directory at the project root
- For now, the app will use icon placeholders, but this prepares for future asset integration
- When the illustration asset is added, update `PermissionRequestWidget` to use `Image.asset('assets/images/illustration_person_with_clipboard.png')` instead of the icon placeholder

This change is minimal and non-breaking, allowing assets to be added incrementally without requiring code changes.