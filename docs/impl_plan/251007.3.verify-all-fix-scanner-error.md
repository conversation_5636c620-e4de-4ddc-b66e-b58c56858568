I have the following verification comments after thorough review and exploration of the codebase. Implement the comments by following the instructions in the comments verbatim.

---
## Comment 1: Finalize scan sets loading/error and drops pages, breaking retry and losing user data.

Update `finalizeScan()` in `lib/features/file_conversion/presentation/notifiers/scan_notifier.dart` to avoid losing pages on error. Remove the `state = loading` transition, and ensure that on any failure you preserve the current pages (e.g., keep `state` as `AsyncValue.data(currentPages)` and return `null`). Also ensure the UI can still access an error message (e.g., return a typed error or expose a lastError field) without relying on `state.error`.

### Referred Files
- /home/<USER>/myapp/lib/features/file_conversion/presentation/notifiers/scan_notifier.dart
---
## Comment 2: Repository can throw ConversionFailure, violating Either contract.

Refactor `lib/features/file_conversion/data/repositories/file_conversion_repository_impl.dart` so that `_createConversionResultFromFile` does NOT throw. Either (a) make it return `Either<Failure, ConversionResult>` and propagate `Left` upstream, or (b) wrap the call in `try/catch` in `convertImagesToPdf/convertDocxToPdf/convertPptToPdf/convertXlsxToPdf` and return `Left(ConversionFailure(...))` on error.

### Referred Files
- /home/<USER>/myapp/lib/features/file_conversion/data/repositories/file_conversion_repository_impl.dart
---
## Comment 3: Retry flow relies on reading error from provider state which may no longer hold pages.

Refactor `lib/features/file_conversion/presentation/screens/scan_review_screen.dart` `_onFinalizePressed` to rely on a typed result from `finalizeScan()` (e.g., change notifier method to return `Either<Failure, ConversionResult?>`), and use that for error messaging instead of reading `scanProvider.error`. Update tests accordingly.

### Referred Files
- /home/<USER>/myapp/lib/features/file_conversion/presentation/screens/scan_review_screen.dart
- /home/<USER>/myapp/lib/features/file_conversion/presentation/notifiers/scan_notifier.dart
---
## Comment 4: Planned tests and docs missing for critical finalize flow and data source behaviors.

Add the missing tests and docs per plan:
- Create `test/features/file_conversion/presentation/notifiers/scan_notifier_finalize_flow_test.dart`
- Create `test/features/file_conversion/presentation/screens/scan_review_screen_test.dart`
- Create `test/features/file_conversion/data/datasources/pdf_converter_data_source_impl_test.dart` with real temp files
- Create `test/features/file_conversion/integration/scan_flow_integration_test.dart`
- Add `test/run_scan_tests.sh` and `README_SCAN_TESTS.md`
Use `test/test_helpers/test_image_generator.dart` to build images.

### Referred Files
- /home/<USER>/myapp/test/features/file_conversion/presentation/notifiers/scan_notifier_test.dart
- /home/<USER>/myapp/test/test_helpers/test_image_generator.dart
---
## Comment 5: MANAGE_EXTERNAL_STORAGE is unnecessary and risky; prefer scoped storage/media permissions.

Update `android/app/src/main/AndroidManifest.xml`: remove `<uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />`. If media reading is needed, add `<uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />` (Android 13+). Continue writing output to app-specific directories to avoid broad storage access.

### Referred Files
- /home/<USER>/myapp/android/app/src/main/AndroidManifest.xml
---
## Comment 6: Potential double-rotation if processed images are saved but rotationAngle not reset.

In `lib/features/file_conversion/presentation/notifiers/scan_notifier.dart` `finalizeScan()`, when adding to `updatedPages`, also reset `rotationAngle` to `0.0` and optionally set `filter` to `PageFilter.color` to avoid double-applying effects in the preview if state is preserved.

### Referred Files
- /home/<USER>/myapp/lib/features/file_conversion/presentation/notifiers/scan_notifier.dart
---
## Comment 7: B&W filter achieved with high contrast may produce low-quality dithering.

Improve B&W filter in `lib/features/file_conversion/data/datasources/pdf_converter_data_source_impl.dart`: after grayscale, apply a threshold (e.g., Otsu or fixed 128) to produce cleaner black/white output instead of high contrast. Add tests in a new `pdf_converter_data_source_impl_test.dart`.

### Referred Files
- /home/<USER>/myapp/lib/features/file_conversion/data/datasources/pdf_converter_data_source_impl.dart
---
## Comment 8: Repository methods lack expanded test coverage for filters and PDF conversions.

Expand `test/features/file_conversion/data/repositories/file_conversion_repository_impl_test.dart` to add test groups for `applyFiltersToImage`, `convertImagesToPdf`, and `pickImages`. Mock `PDFConverterDataSource` to return Left/Right and assert repository returns proper Either values and delegates parameters correctly.

### Referred Files
- /home/<USER>/myapp/test/features/file_conversion/data/repositories/file_conversion_repository_impl_test.dart
- /home/<USER>/myapp/lib/features/file_conversion/data/repositories/file_conversion_repository_impl.dart
---
## Comment 9: Camera cancellation relies on string matching message, which is brittle.

Refine camera cancellation handling in `lib/features/file_conversion/presentation/notifiers/scan_notifier.dart` by defining a dedicated `UserCancelledFailure` or a `CameraFailure` with a `isCancelled` flag, and use that instead of matching the message string.

### Referred Files
- /home/<USER>/myapp/lib/features/file_conversion/presentation/notifiers/scan_notifier.dart
- /home/<USER>/myapp/lib/core/error/failures.dart
---