I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

## Key Issues Identified

1. **Main Thread Blocking (590 frames skipped)**: The `FileSystemDataSourceImpl._scanDirectory()` performs recursive file system traversal with synchronous I/O operations (`file.stat()`, `Directory.list()`) on the main thread, causing severe UI jank.

2. **RenderFlex Overflow (29 pixels)**: The `ToolCard` widget in `discover_screen.dart` uses a grid with `childAspectRatio: 0.9`, resulting in only ~133px vertical space. The Column contains Icon (48px) + spacing (12px) + title text + spacing (4px) + description (2 lines) + spacing (8px) + "Coming Soon" badge, which exceeds available space.

3. **No State Caching**: Each tab switch triggers provider rebuilds. The `documentListProvider` rescans the file system on every Home tab visit.

4. **Heavy Memory Operations**: PDF viewer loads entire documents into memory as `Uint8List`, which can cause memory pressure for large files.

5. **Inefficient Filtering**: The `HomeScreen` filters documents in the widget layer on every rebuild instead of at the provider level.


### Approach

## Solution Strategy

**Performance Optimizations:**
- Move file system scanning to a background isolate to prevent main thread blocking
- Implement document list caching with TTL (time-to-live) to avoid redundant scans
- Add `keepAlive` mechanism for tab state preservation
- Optimize filtering logic at the provider level
- Add debouncing for rapid tab switches

**UI Fixes:**
- Adjust grid `childAspectRatio` from 0.9 to 1.1 for more vertical space
- Make ToolCard content flexible with proper constraints
- Add overflow handling with ellipsis for long text

**Best Practices:**
- Use `compute()` for isolate-based file scanning
- Implement proper error boundaries
- Add performance monitoring hooks
- Follow Flutter's performance best practices for lists and grids


### Reasoning

I started by examining the error logs showing 590 skipped frames and RenderFlex overflow. I then explored the navigation structure in `app_scaffold.dart` and `app_router.dart`, identifying the StatefulShellRoute setup. Next, I investigated the problematic `tool_card.dart` widget and the `discover_screen.dart` grid layout. I then traced the performance bottleneck to `file_system_datasource_impl.dart` where synchronous file I/O operations occur. I reviewed the state management in `document_list_notifier.dart` and confirmed no caching exists. Finally, I checked the pubspec.yaml for dependencies and examined the PDF viewer implementation to understand memory usage patterns.


## Mermaid Diagram

sequenceDiagram
    participant User
    participant AppScaffold
    participant HomeScreen
    participant DocumentListNotifier
    participant FileSystemDataSource
    participant Isolate
    participant Cache

    User->>AppScaffold: Tap Home Tab
    AppScaffold->>HomeScreen: Navigate (with keepAlive)
    HomeScreen->>DocumentListNotifier: Watch documentListProvider
    
    alt Cache Valid
        DocumentListNotifier->>Cache: Check cache TTL
        Cache-->>DocumentListNotifier: Return cached data
        DocumentListNotifier-->>HomeScreen: Display documents (instant)
    else Cache Expired/Missing
        DocumentListNotifier->>Cache: Check cache
        Cache-->>DocumentListNotifier: Cache invalid
        DocumentListNotifier->>FileSystemDataSource: scanDocuments()
        FileSystemDataSource->>Isolate: compute(_scanDirectoryInIsolate)
        Note over Isolate: Scan files in background<br/>(no main thread blocking)
        Isolate-->>FileSystemDataSource: Return document list
        FileSystemDataSource-->>DocumentListNotifier: Documents ready
        DocumentListNotifier->>Cache: Store with timestamp
        DocumentListNotifier-->>HomeScreen: Display documents
    end

    User->>AppScaffold: Tap Discover Tab
    AppScaffold->>HomeScreen: Preserve state (keepAlive)
    Note over HomeScreen: State preserved,<br/>no rebuild needed

    User->>AppScaffold: Tap Home Tab again
    AppScaffold->>HomeScreen: Restore preserved state
    HomeScreen->>DocumentListNotifier: Watch provider
    DocumentListNotifier->>Cache: Check cache
    Cache-->>DocumentListNotifier: Return cached data
    DocumentListNotifier-->>HomeScreen: Display instantly

### Testing Strategy (TDD)

## Goals

- Lock in behavior before refactors: file scanning, provider caching/filters, PDF loading mode, and Discover grid layout.
- Provide fast feedback as we move scanning to an isolate and apply UI/layout tweaks.

## Scope

- Unit tests for pure logic (file scanning, performance utils, caching TTL decisions).
- Provider tests with Riverpod overrides (cache-first, refresh/invalidations, filtered views).
- Widget tests to guard against Discover grid overflow and to verify PDF viewer uses efficient loading.

## Test-first Milestones

1. Pure scanner logic: validate extension filtering, metadata mapping, and error tolerance independent of I/O.
2. Provider caching: assert cache-with-TTL prevents duplicate scans and `refresh()` bypasses cache.
3. Filtering at provider: ensure `filterByType` (or filtered provider) returns correct subsets.
4. Discover grid layout: no overflow when rendering `ToolCard` with long text under tight height.
5. PDF viewer loading: prefer `.file` path rendering over `.memory` for large PDFs.
6. Performance utilities: `Debouncer`, `Throttler`, `measurePerformance`, and `computeWithTimeout` behaviors.

## Acceptance Signals

- All new tests pass before beginning refactors; they remain green after each optimization commit.
- Widget tests detect any reintroduction of overflow or inefficient loading APIs.

## Task Status

- lib/features/file_discovery/data/utils/file_scanner.dart: COMPLETED (new pure scanner utility implemented)
- lib/core/utils/performance_utils.dart: COMPLETED (Debouncer/Throttler/measurePerformance/computeWithTimeout)
- lib/features/document_viewer/presentation/screens/pdf_viewer_screen.dart: COMPLETED (switched to `SfPdfViewer.file` with sentinel key)
- lib/features/discover/presentation/screens/discover_screen.dart: COMPLETED (aspect ratio adjusted; ToolCard made flexible; overflow test passes)
- lib/features/file_discovery/presentation/notifiers/document_list_notifier.dart: COMPLETED (TTL caching, keepAlive, refresh semantics)
- lib/features/file_discovery/presentation/providers/file_discovery_providers.dart: COMPLETED (added filtered document providers)
- lib/features/file_discovery/data/datasources/file_system_datasource_impl.dart: IN PROGRESS (isolate entry wired via computeWithTimeout; batching added; cancellation TBD)
- lib/features/file_discovery/presentation/widgets/document_list_item.dart: COMPLETED (RepaintBoundary + minor density/const tweaks)
- lib/core/navigation/app_scaffold.dart: COMPLETED (debounced navigation + animation duration)
- lib/features/file_discovery/presentation/screens/home_screen.dart: COMPLETED (AutomaticKeepAliveClientMixin)

## Proposed File Changes

### lib/features/file_discovery/data/datasources/file_system_datasource_impl.dart(MODIFY)

References: 

- lib/features/file_discovery/data/models/document_model.dart

## Optimize File System Scanning with Isolate

**Problem**: The `_scanDirectory()` method performs synchronous I/O operations on the main thread, causing severe frame drops.

**Changes**:

1. **Add isolate-based scanning method**:
   - Create a new static method `_scanDirectoryInIsolate(String directoryPath)` that can run in a separate isolate
   - This method should accept a directory path as a String and return a List of Map<String, dynamic> (serializable document data)
   - Move the entire scanning logic (recursive traversal, stat calls, file filtering) into this static method

2. **Modify `scanDocuments()` method**:
   - Replace the direct `_scanDirectory()` calls with a top-level isolate entry that delegates to a pure scanner util
   - Wrap with `computeWithTimeout()` for safety and log timing with `measurePerformance()`
   - Convert the returned Map data back to `DocumentModel` objects in the main thread

3. **Add batch processing**:
   - Instead of scanning the entire storage at once, implement a limit (e.g., 500 files per scan)
   - Add a parameter to control scan depth (e.g., max 3 levels deep)
   - This prevents extremely long-running operations even in isolates

4. **Optimize file stat calls**:
   - Cache stat results temporarily to avoid redundant calls
   - Consider using `FileStat.statSync()` only when necessary
   - Skip hidden directories (starting with '.') early to reduce I/O

5. **Add cancellation support**:
   - Use notifier-level soft-cancel (ignore in-flight results) and a visible Cancel button in the UI
   - Rely on timeout for isolate operations; no hard isolate termination is required

**Implementation Notes**:
- The isolate function is top-level and delegates to a pure scanner util
- All data passed to/from isolate are serializable (Maps)
- Proper error handling: falls back to direct call if isolate/timeout fails
- Timeout acts as cancellation; progress callbacks are optional

### lib/features/file_discovery/data/utils/file_scanner.dart(NEW)

## Extract Pure Scanning Logic

**Purpose**: Provide a pure, testable function for scanning a set of paths and mapping to serializable document data. The isolate entrypoint and the data source will delegate to this utility.

**Contents**:

1. `List<Map<String, dynamic>> scanPaths(List<String> roots)`
   - Iterates through provided directories using safe traversal
   - Filters by supported extensions
   - Maps to serializable maps: `{ id, name, path, type, sizeInBytes, dateModifiedMillis }`
   - Skips hidden/system directories and handles permission errors gracefully

2. Helpers for extension-to-DocumentType mapping (string-based) and lightweight stat reads encapsulated for mocking.

**Implementation Notes**:
- Keep functions pure and decoupled from Flutter; no platform channels.
- The isolate method in the data source will call into this utility and then convert to `DocumentModel` on the main thread.

### lib/features/file_discovery/presentation/notifiers/document_list_notifier.dart(MODIFY)

References: 

- lib/features/file_discovery/domain/entities/document.dart
- lib/features/file_discovery/presentation/providers/file_discovery_providers.dart(MODIFY)

## Add Caching and State Preservation

**Problem**: The provider rescans the file system on every rebuild, causing redundant expensive operations.

**Changes**:

1. **Implement cache with TTL**:
   - Add a `DateTime? _lastScanTime` field to track when the last scan occurred
   - Add a `Duration _cacheDuration = const Duration(minutes: 5)` constant
   - In the `build()` method, check if cached data is still valid before triggering a new scan
   - Only call `scanDocuments()` if cache is expired or doesn't exist

2. **Add manual refresh control**:
   - Keep the existing `refresh()` method for pull-to-refresh functionality
   - This should bypass the cache and force a new scan
   - Update `_lastScanTime` after successful refresh

3. **Implement filtered providers**:
   - Create a new method `getFilteredDocuments(DocumentType type)` that returns filtered results from cached data
   - This avoids re-filtering on every widget rebuild
   - Use Riverpod's family modifier if needed for type-specific providers

4. **Add loading state optimization**:
   - When returning cached data, set state to `AsyncValue.data()` immediately
   - Show a subtle refresh indicator if background refresh is happening
   - Avoid showing full loading screen when cache exists

5. **Implement keepAlive**:
   - Add `ref.keepAlive()` in the build method to prevent provider disposal when navigating away
   - This ensures the scanned document list persists across tab switches
   - Consider using `ref.cacheFor(Duration(minutes: 10))` for automatic cleanup

**Implementation Notes**:
- The cache should be invalidated when a new document is picked via `pickDocument()`
- Consider persisting the cache to disk for app restart scenarios (future enhancement)
- Add a method to manually invalidate cache if needed

### lib/features/file_discovery/presentation/screens/home_screen.dart(MODIFY)

References: 

- lib/features/file_discovery/presentation/widgets/file_type_tabs.dart
- lib/features/file_discovery/presentation/widgets/document_list_item.dart(MODIFY)
- lib/features/file_discovery/presentation/notifiers/document_list_notifier.dart(MODIFY)

## Optimize Widget Rebuilds and Filtering

**Problem**: The screen rebuilds frequently and filters documents in the widget layer, causing unnecessary computations.

**Changes**:

1. **Implement AutomaticKeepAliveClientMixin**:
   - Make `_HomeScreenState` extend `AutomaticKeepAliveClientMixin<HomeScreen>`
   - Override `wantKeepAlive` to return `true`
   - Call `super.build(context)` at the start of the build method
   - This preserves the widget state when switching tabs, preventing unnecessary rebuilds

2. **Move filtering to provider level**:
   - Instead of filtering in `_buildDocumentList()`, create a filtered provider
   - Use `ref.watch(filteredDocumentListProvider(_selectedType))` to get pre-filtered results
   - This moves the filtering computation out of the widget layer

3. **Optimize ListView.builder**:
   - Add `itemExtent` or `prototypeItem` to ListView.builder for better performance
   - This helps Flutter optimize scrolling by knowing item heights in advance
   - Consider using `ListView.separated` if dividers are needed

4. **Add const constructors**:
   - Make `FileTypeTabs` and other child widgets const where possible
   - This prevents unnecessary widget rebuilds
   - Ensure all static widgets use const constructors

5. **Debounce tab selection**:
   - Add a small debounce (100ms) to the `onTypeSelected` callback
   - This prevents rapid filter changes from causing multiple rebuilds
   - Use a Timer to implement the debounce logic

6. **Optimize RefreshIndicator**:
   - Ensure the refresh callback properly awaits the scan completion
   - Show a subtle loading indicator during background refresh
   - Don't block the UI while refreshing if cached data exists

**Implementation Notes**:
- The `AutomaticKeepAliveClientMixin` is crucial for tab state preservation
- Consider using `RepaintBoundary` around expensive widgets like document list items
- Profile the app after changes to verify performance improvements

### lib/features/discover/presentation/screens/discover_screen.dart(MODIFY)

References: 

- lib/features/discover/presentation/widgets/tool_card.dart(MODIFY)

## Fix Grid Layout and Overflow Issues

**Problem**: The grid's `childAspectRatio: 0.9` creates insufficient vertical space (133.3px) for ToolCard content, causing 29px overflow.

**Changes**:

1. **Adjust childAspectRatio**:
   - Change `childAspectRatio` from `0.9` to `1.1` in the `SliverGridDelegateWithFixedCrossAxisCount`
   - This provides approximately 165px of vertical space instead of 133px
   - The extra ~32px will accommodate the content without overflow

2. **Add responsive grid configuration**:
   - Consider using `MediaQuery` to adjust aspect ratio based on screen size
   - For smaller screens, use `childAspectRatio: 1.0`
   - For larger screens (tablets), use `childAspectRatio: 1.2`

3. **Optimize GridView performance**:
   - Add `cacheExtent: 100` to GridView.builder to reduce off-screen widget builds
   - Since the tools list is static and small, consider using a regular Grid instead of GridView.builder
   - This eliminates the builder overhead for a small, fixed list

4. **Add const optimization**:
   - Make the `SliverGridDelegateWithFixedCrossAxisCount` a const variable outside the build method
   - This prevents recreating the delegate on every rebuild

5. **Implement AutomaticKeepAliveClientMixin** (if needed):
   - If the Discover tab becomes more complex in the future, add keepAlive
   - For now, since it's just static data, this is optional

**Implementation Notes**:
- Test the new aspect ratio on multiple device sizes (small phones, large phones, tablets)
- The aspect ratio change should be the primary fix for the overflow issue
- Consider adding a `RepaintBoundary` around each ToolCard if performance issues persist

### lib/features/discover/presentation/widgets/tool_card.dart(MODIFY)

References: 

- lib/features/discover/domain/entities/tool_entry.dart

## Make ToolCard Content Flexible and Overflow-Safe

**Problem**: The Column widget has fixed-size children that don't adapt to available space, causing overflow when space is constrained.

**Changes**:

1. **Replace mainAxisAlignment with flexible layout**:
   - Change `mainAxisAlignment: MainAxisAlignment.center` to `MainAxisAlignment.start`
   - Wrap the Column in a `SizedBox.expand()` or use `mainAxisSize: MainAxisSize.min`
   - This ensures the Column doesn't try to expand beyond available space

2. **Make text widgets flexible**:
   - Wrap the title `Text` widget with `Flexible` or `Expanded`
   - Ensure `overflow: TextOverflow.ellipsis` is set (already present)
   - Add `maxLines: 2` to the title text to prevent excessive height

3. **Optimize description text**:
   - The description already has `maxLines: 2` and `overflow: TextOverflow.ellipsis`, which is good
   - Consider reducing `maxLines: 1` for the description to save space
   - Or make the description section `Flexible` so it can shrink if needed

4. **Adjust spacing**:
   - Reduce the spacing between elements slightly:
     - Icon to title: change from `12` to `8`
     - Title to description: keep at `4`
     - Description to badge: change from `8` to `6`
   - This saves approximately 6px of vertical space

5. **Optimize icon size**:
   - Consider reducing icon size from `48` to `40` on smaller screens
   - Use `MediaQuery` to adjust icon size based on available space
   - This is optional but can help on very small devices

6. **Add safety constraints**:
   - Wrap the entire Column content in a `ConstrainedBox` with `maxHeight`
   - This ensures the content never exceeds the card's available space
   - Use `SingleChildScrollView` as a last resort if content is truly too large (not recommended for cards)

**Implementation Notes**:
- The combination of grid aspect ratio change (in `discover_screen.dart`) and these layout improvements should completely eliminate the overflow
- Test with both enabled and disabled tools to ensure both states render correctly
- Verify that the "Coming Soon" badge doesn't cause overflow when present

### lib/core/navigation/app_scaffold.dart(MODIFY)

References: 

- lib/core/navigation/app_router.dart

## Optimize Bottom Navigation Performance

**Problem**: Rapid tab switching may cause unnecessary rebuilds and navigation overhead.

**Changes**:

1. **Add debouncing to navigation**:
   - Implement a debounce mechanism in `_onDestinationSelected()` to prevent rapid tab switches
   - Use a `Timer` with a 200ms delay to batch rapid taps
   - Cancel the previous timer if a new tap occurs within the delay period
   - This prevents the navigation system from being overwhelmed by rapid switches

2. **Optimize NavigationBar configuration**:
   - Add `animationDuration: const Duration(milliseconds: 200)` to NavigationBar
   - This ensures smooth, consistent transitions between tabs
   - Consider adding `labelBehavior: NavigationDestinationLabelBehavior.alwaysShow` for consistency

3. **Add performance monitoring hooks** (optional):
   - Wrap the navigation callback with performance tracking
   - Log navigation events to identify any bottlenecks
   - Use Flutter DevTools timeline to verify smooth transitions

4. **Ensure const constructors**:
   - Make the `NavigationDestination` widgets const (already done, verify)
   - This prevents unnecessary widget rebuilds

5. **Consider adding a loading state**:
   - If tab content is heavy, show a subtle loading indicator during tab switch
   - This provides visual feedback to users during transitions
   - Only implement if tab switches still feel sluggish after other optimizations

**Implementation Notes**:
- The debouncing is a defensive measure; the main performance gains come from optimizing the tab content (HomeScreen and DiscoverScreen)
- The `StatefulNavigationShell` already handles state preservation, so focus on preventing unnecessary rebuilds in child screens
- Test navigation performance after implementing all other optimizations to see if debouncing is truly necessary

### lib/features/file_discovery/presentation/providers/file_discovery_providers.dart(MODIFY)

References: 

- lib/features/file_discovery/domain/entities/document.dart
- lib/features/file_discovery/presentation/notifiers/document_list_notifier.dart(MODIFY)

## Add Filtered Document Providers

**Problem**: Document filtering happens in the widget layer, causing unnecessary computations on every rebuild.

**Changes**:

1. **Create a filtered document provider**:
   - Add a new provider `filteredDocumentListProvider` that accepts a `DocumentType` parameter
   - Use Riverpod's `.family` modifier to create type-specific providers
   - This provider should watch `documentListProvider` and return filtered results

2. **Implement efficient filtering**:
   - The provider should filter the document list based on the provided type
   - For `DocumentType.unknown`, return all documents
   - For specific types, use `where()` to filter matching documents
   - Cache the filtered results to avoid recomputing on every access

3. **Add document count providers**:
   - Create providers for document counts by type (e.g., `pdfCountProvider`, `docxCountProvider`)
   - These can be used to show counts in the file type tabs
   - Compute counts from the main document list to avoid redundant scans

4. **Optimize provider dependencies**:
   - Ensure providers only rebuild when their dependencies change
   - Use `select()` to watch specific parts of the state
   - Avoid unnecessary provider invalidations

**Implementation Notes**:
- The filtered providers should be lightweight since they're just filtering an in-memory list
- Consider using `@riverpod` annotation with code generation for type safety
- The `HomeScreen` should watch the filtered provider instead of filtering in the widget

### lib/features/file_discovery/presentation/widgets/document_list_item.dart(MODIFY)

References: 

- lib/features/file_discovery/domain/entities/document.dart

## Optimize Document List Item Performance

**Problem**: List items may be rebuilding unnecessarily, contributing to scroll jank.

**Changes**:

1. **Add const constructor**:
   - Make the `DocumentListItem` constructor const if possible
   - Ensure all child widgets use const constructors where applicable
   - This prevents unnecessary rebuilds when the parent rebuilds

2. **Implement efficient equality**:
   - If the widget accepts a `Document` object, ensure it has proper equality implementation
   - This allows Flutter to skip rebuilds when the document data hasn't changed
   - The `Document` entity should implement `==` and `hashCode` (likely already done with Freezed)

3. **Add RepaintBoundary**:
   - Wrap the list item content in a `RepaintBoundary`
   - This isolates the item's rendering from the rest of the list
   - Helps prevent unnecessary repaints when scrolling

4. **Optimize image/icon loading**:
   - If the list item displays file type icons, ensure they're cached
   - Use const icons where possible
   - Avoid loading images synchronously

5. **Reduce widget depth**:
   - Review the widget tree and flatten where possible
   - Fewer nested widgets = better performance
   - Combine multiple Container/Padding widgets into one where possible

**Implementation Notes**:
- These optimizations are secondary to the main file scanning optimization
- Profile the list scrolling performance to verify improvements
- Consider using `ListView.builder` with `itemExtent` in the parent widget for better performance

### lib/core/utils/performance_utils.dart(NEW)

## Create Performance Utility Helpers

**Purpose**: Provide reusable utilities for performance optimization across the app.

**Contents**:

1. **Debouncer class**:
   - Create a `Debouncer` class that wraps a callback with debounce logic
   - Constructor should accept a `Duration` parameter for the debounce delay
   - Provide a `call()` method that executes the callback after the delay
   - Cancel any pending callbacks if a new call is made within the delay period
   - Include a `dispose()` method to clean up timers

2. **Throttler class**:
   - Create a `Throttler` class for throttling rapid function calls
   - Unlike debouncer, throttler executes immediately and then blocks subsequent calls for a duration
   - Useful for preventing rapid repeated actions

3. **Performance monitoring helpers**:
   - Add a `measurePerformance()` function that wraps async operations with timing
   - Logs the execution time to console in debug mode
   - Returns the result of the wrapped operation
   - Example: `final result = await measurePerformance('File Scan', () => scanFiles())`

4. **Isolate helpers**:
   - Add a `computeWithTimeout()` function that wraps Flutter's `compute()` with a timeout
   - Prevents isolate operations from hanging indefinitely
   - Throws a `TimeoutException` if the operation exceeds the timeout

5. **Cache helper**:
   - Create a simple `CachedValue<T>` class for caching values with TTL
   - Stores a value, timestamp, and duration
   - Provides `isValid()` method to check if cache is still fresh
   - Provides `get()` and `set()` methods for value access

**Implementation Notes**:
- These utilities should be pure Dart with no Flutter dependencies where possible
- Add comprehensive documentation for each utility
- Include usage examples in comments
- These utilities will be used across multiple features for consistent performance optimization

### lib/features/document_viewer/presentation/notifiers/document_viewer_notifier.dart(MODIFY)

References: 

- lib/features/document_viewer/presentation/screens/pdf_viewer_screen.dart
- lib/features/document_viewer/domain/entities/document_content.dart

## Optimize Document Loading Performance

**Problem**: Loading entire documents into memory as `Uint8List` can cause memory pressure and slow initial load times.

**Changes**:

1. **Implement lazy loading**:
   - Instead of loading the entire document immediately, load it in chunks
   - For PDF viewer, consider using file path directly if Syncfusion supports it
   - Check if `SfPdfViewer.file()` can be used instead of `SfPdfViewer.memory()`

2. **Add document size checks**:
   - Before loading, check the file size
   - For very large files (>50MB), show a warning or use streaming approach
   - Consider implementing a size limit with user confirmation for large files

3. **Implement caching**:
   - Cache loaded document content in memory for quick re-access
   - Use a LRU (Least Recently Used) cache to limit memory usage
   - Clear cache when memory pressure is detected

4. **Add loading progress**:
   - For large documents, show loading progress instead of just a spinner
   - Use `Stream` to emit progress updates during loading
   - Update the UI with percentage or bytes loaded

5. **Optimize memory usage**:
   - Dispose of document content when the viewer is closed
   - Use `Uint8List.view()` instead of copying data where possible
   - Consider using memory-mapped files for very large documents

**Implementation Notes**:
- Check Syncfusion documentation for optimal loading strategies
- The `SfPdfViewer.file()` constructor is preferred over `.memory()` for better performance
- Profile memory usage before and after changes
- These optimizations are secondary to the main file scanning issue but important for overall app performance

### test/features/file_discovery/data/utils/file_scanner_test.dart(NEW)

## Unit Tests: File Scanner

**Covers**:
- Filters by supported extensions only.
- Maps file metadata to serializable structure consistently.
- Skips hidden/system directories and tolerates permission errors.

**Notes**:
- Mock filesystem interactions with fakes; avoid real I/O.
- Validate deterministic ordering if applicable (e.g., by path).

### test/features/file_discovery/presentation/notifiers/document_list_notifier_cache_test.dart(NEW)

## Provider Tests: Cache and TTL

**Covers**:
- Returns cached data within TTL without triggering a new scan.
- `refresh()` bypasses cache and updates `_lastScanTime`.
- Cache invalidation when `pickDocument()` succeeds.

**Notes**:
- Use Riverpod overrides to inject a fake repository and controllable clock.

### test/features/file_discovery/presentation/notifiers/document_list_notifier_filter_test.dart(NEW)

## Provider Tests: Filtering

**Covers**:
- `filterByType` returns all for `unknown` and correct subsets for specific types.
- Filtered providers (if added) emit expected lists and avoid unnecessary rebuilds.

### test/features/discover/presentation/screens/discover_screen_layout_test.dart(NEW)

## Widget Test: Grid Overflow Regression

**Covers**:
- Renders `DiscoverScreen` grid with long titles/descriptions without overflow.
- Adjusted `childAspectRatio` eliminates the previous 29px overflow.

**Notes**:
- Pump within a constrained `MediaQuery` to simulate small screens.

### test/features/document_viewer/presentation/screens/pdf_viewer_screen_loading_test.dart(NEW)

## Widget Test: PDF Loading Mode

**Covers**:
- Verifies `.file` is used for large documents (or equivalent efficient path-based loading).
- Continues to display loading and error states correctly.

**Notes**:
- Override `documentViewerProvider` to return a fake `DocumentContent` and assert the constructed viewer widget.

### test/core/utils/performance_utils_test.dart(NEW)

## Unit Tests: Performance Utilities

**Covers**:
- `Debouncer` delays and coalesces calls; `dispose()` cancels timers.
- `Throttler` executes immediately then suppresses calls within window.
- `measurePerformance` returns result and logs timing (in debug builds).
- `computeWithTimeout` completes under normal conditions and throws on timeout.

**Notes**:
- Use short durations to keep tests fast and deterministic.
