# Implementation Plan — PRD-003: PDF Page Management Tools

**Author:** G<PERSON><PERSON><PERSON> Copilot (Henry spec-driven agent)

**Date:** 2025-10-03

---

## Phase 0 — Research Log

- **Codebase context:** Reviewed existing Clean Architecture layout under `lib/features/` (permissions, file_discovery, document_viewer) and prior implementation plans (`docs/impl_plan/251002.*`). Confirmed heavy use of Riverpod with generated providers, dartz-based error handling, and Freezed models. Verified current dependencies in `pubspec.yaml` and availability of `unit_test_assets` for PDF fixtures.
- **External research:** No web search capability available in this environment. Proceeding with architecture-aligned best practices drawn from Syncfusion PDF manipulation APIs (`syncfusion_flutter_pdf` package documentation) already familiar from prior work with `syncfusion_flutter_pdfviewer`.

---

## Phase 1 — Requirements Analysis

### 1.1 Goal Recap

Deliver a feature-complete PDF page management toolkit (merge, split, reorder, extract) that integrates with the Document Reader app while preserving the established feature-first Clean Architecture and Riverpod-driven DI/TDD guidelines.

### 1.2 Architectural Constraint (Non-Negotiable)

- **The system shall follow the existing feature-first Clean Architecture** with Presentation → Domain ← Data layering, Riverpod for state and DI, dartz `Either` for domain/data error handling, Freezed for immutable models, and dependency direction pointing inward. New code must live under `lib/features/pdf_page_management/`.

### 1.3 Assumptions & Dependencies

- Operations run on-device using offline PDFs accessible via storage permissions already handled by existing features.
- Newly generated PDFs are stored inside the app-specific documents directory (`path_provider`), with metadata exposed via existing file discovery mechanisms after processing.
- Syncfusion community license is available for `syncfusion_flutter_pdf` (same vendor as existing viewer package); legal validation outside scope.
- Large PDFs (≤ 200 MB, ≤ 1,000 pages) must complete operations without UI freezes; background isolates may be needed if profiling warrants (not planned unless Red tests show failure).

### 1.4 Functional Requirements (EARS Notation)

| ID | Requirement |
|----|-------------|
| **FR1** | WHEN the user selects **Merge PDFs** from the Discover hub and picks two or more PDF files in a defined order THE SYSTEM SHALL generate a single merged PDF copy saved to the managed output directory and present its details for opening or sharing. |
| **FR2** | WHEN the user provides fewer than two PDFs for merging THE SYSTEM SHALL block the action, display a validation message, and keep the merge button disabled. |
| **FR3** | WHEN the user selects **Split PDF**, chooses a source PDF, and inputs a valid page range or pattern THE SYSTEM SHALL create a new PDF containing the selected pages while keeping the source intact. |
| **FR4** | WHEN the user enters an invalid range (e.g., non-numeric, outside bounds, start greater than end) in the Split UI THE SYSTEM SHALL reject the input, highlight the error, and prevent export. |
| **FR5** | WHEN the user opens **Manage Pages** for a PDF, reorders pages via drag-and-drop, rotates, or deletes selections and taps **Save** THE SYSTEM SHALL produce a new PDF reflecting the modified order and transformations. |
| **FR6** | WHEN the user taps **Extract Pages**, selects specific page thumbnails, and confirms THE SYSTEM SHALL produce a new PDF containing only the chosen pages. |
| **FR7** | WHEN any long-running PDF manipulation exceeds two seconds THE SYSTEM SHALL surface progress feedback and keep the UI responsive using async operations. |
| **FR8** | WHEN an operation fails (e.g., corrupt file, insufficient storage) THE SYSTEM SHALL display a descriptive inline error, log the failure, and avoid partial file writes. |
| **FR9** | WHEN a new PDF is generated THE SYSTEM SHALL return a structured result (path, name, page count, size) ready for downstream use by file discovery and viewer features. |

### 1.5 Non-Functional Requirements

- **Security:** No original file modifications; all outputs are copies. Validate file paths to prevent traversal. Clear temporary files after use.
- **Reliability:** Support batch operations up to 1,000 pages with deterministic ordering and no data loss.
- **Performance:** Target < 5s for 500-page operations on mid-tier Android device; show incremental progress for longer tasks.
- **UX:** Maintain Material 3 styling, provide drag handles for thumbnails, and conform to existing navigation patterns (Discover hub entry).
- **Testability:** All use cases and repositories must expose deterministic APIs enabling mock-based unit tests.

### 1.6 Simplicity Gate (Phase 1)

- Requirements stay tightly scoped to PRD-003 features only.
- No premature background-isolate abstraction — consider only if tests reveal blocking issues.
- Reuse existing file picker and document viewer assets.

---

## Phase 2 — Specification Creation

### 2.1 Feature Module Layout

```
lib/features/pdf_page_management/
  data/
    datasources/
      pdf_page_datasource.dart          # abstract
      syncfusion_pdf_page_datasource.dart
    repositories/
      pdf_page_repository_impl.dart
    models/
      pdf_operation_result_model.dart   # DTOs if needed
  domain/
    entities/
      pdf_operation_result.dart
      page_selection.dart
    repositories/
      pdf_page_repository.dart
    usecases/
      merge_pdfs.dart
      split_pdf.dart
      reorder_pdf_pages.dart
      extract_pdf_pages.dart
  presentation/
    notifiers/
      merge_pdf_notifier.dart
      split_pdf_notifier.dart
      page_management_notifier.dart
      extract_pdf_notifier.dart
    screens/
      merge_pdf_screen.dart
      split_pdf_screen.dart
      page_management_screen.dart
      extract_pdf_screen.dart
    widgets/
      page_thumbnail_grid.dart
      split_range_input.dart
```

- Navigation entry from Discover tab via new tiles; route definitions added in `core/navigation/app_router.dart`.

### 2.2 Domain Layer Specification

- **Entities**
  - `PdfOperationResult` (Freezed): `{String outputPath, String displayName, int pageCount, int fileSize}`.
  - `PageSelection` (Freezed): `{List<int> selectedPageIndices, bool rotationApplied, bool deleteFlag}` used by reorder/extract flows.
- **Repository Interface (`PdfPageRepository`)**
  - `Future<Either<Failure, PdfOperationResult>> mergePdfs(List<PdfDocumentDescriptor> inputs)`.
  - `Future<Either<Failure, PdfOperationResult>> splitPdf({required PdfDocumentDescriptor source, required SplitPattern pattern})`.
  - `Future<Either<Failure, PdfOperationResult>> reorderPages({required PdfDocumentDescriptor source, required List<PageEditAction> edits})`.
  - `Future<Either<Failure, PdfOperationResult>> extractPages({required PdfDocumentDescriptor source, required List<int> pageIndices})`.
- **Use Cases:** Thin wrappers per repository method, validating parameters before delegating.

### 2.3 Data Layer Specification

- **Datasource (`SyncfusionPdfPageDatasource`)**
  - Utilizes `syncfusion_flutter_pdf` to load PDFs, merge documents (`PdfDocument.append`), remove pages, rotate via `PdfPage.rotation`, and export byte arrays.
  - Saves results using `FileSystem` APIs (from `dart:io`) to app documents directory (`path_provider`).
  - Handles temporary storage in `/tmp/pdf_ops/` subdirectory with automatic cleanup.
- **Repository Implementation**
  - Maps datasource exceptions to domain `Failure` subclasses (`FileValidationFailure`, `OperationFailure`, `StorageFailure`).
  - Ensures deterministic output filenames (e.g., `Merged_<timestamp>.pdf`).
  - Emits structured results consumed by use cases.

### 2.4 Presentation Layer Specification

- **Riverpod Notifiers**
  - Implemented with `@riverpod`/`AsyncNotifier`. Each handles state transitions (`AsyncValue`) and exposes command methods (`merge`, `split`, `applyChanges`, `extract`).
  - Validate inputs before hitting use cases (mirrors FR2 & FR4).
  - Provide progress updates (e.g., `AsyncValue.guard` combined with intermediate `state = AsyncValue.loading()` + progress field via `AsyncValue.data(OperationProgress)` where progress is `double` 0–1).
- **Screens**
  - Merge: Multi-file picker summary, drag handle list, CTA button, success bottom sheet.
  - Split: Range input widget (supports patterns like `1-3,5`), preview snippet, output summary.
  - Page Management: Thumbnail grid with drag-and-drop (use `ReorderableWrap` or custom `LongPressDraggable`), rotation buttons, bulk delete.
  - Extract: Reuse page grid with checkbox overlay; confirm button.
- **Widgets**
  - `PageThumbnailGrid` renders cached thumbnails generated via data source helper (`renderPageToImage`).
  - `SplitRangeInput` handles parsing and validation, provides live feedback.

### 2.5 Sequence Diagrams

#### 2.5.1 Merge PDFs Flow

```
User → MergePdfScreen : select files + tap Merge
MergePdfScreen → MergePdfNotifier : merge(request)
MergePdfNotifier → MergePdfsUseCase : execute(request)
MergePdfsUseCase → PdfPageRepository : mergePdfs(descriptors)
PdfPageRepository → SyncfusionPdfPageDatasource : merge(descriptors)
SyncfusionPdfPageDatasource → FileSystem : load bytes, append, save
FileSystem → SyncfusionPdfPageDatasource : output path
SyncfusionPdfPageDatasource → PdfPageRepository : result dto
PdfPageRepository → MergePdfsUseCase : Right(result)
MergePdfsUseCase → MergePdfNotifier : Right(result)
MergePdfNotifier → MergePdfScreen : AsyncData(result)
MergePdfScreen → User : success sheet + open/share options
```

#### 2.5.2 Reorder Pages Flow (with drag feedback)

```
User → PageManagementScreen : drag thumbnails, tap Save
PageManagementScreen → PageManagementNotifier : applyChanges(operations)
PageManagementNotifier → ReorderPdfPagesUseCase : execute(operations)
ReorderPdfPagesUseCase → PdfPageRepository : reorderPages(payload)
PdfPageRepository → SyncfusionPdfPageDatasource : reorderAndSave(payload)
SyncfusionPdfPageDatasource → FileSystem : write reordered PDF
FileSystem → SyncfusionPdfPageDatasource : output path
SyncfusionPdfPageDatasource → PdfPageRepository : Right(result)
PdfPageRepository → ReorderPdfPagesUseCase : Right(result)
ReorderPdfPagesUseCase → PageManagementNotifier : Right(result)
PageManagementNotifier → PageManagementScreen : AsyncData(result)
PageManagementScreen → User : success toast + view button
```

### 2.6 Testing Strategy

- **Unit Tests (Domain):** Mock repository for each use case verifying validation rules and success/failure propagation.
- **Unit Tests (Data):** Mock filesystem (via `package:mocktail`) to ensure datasource handles exceptions. Use sample PDFs placed in `unit_test_assets/pdf/` for golden operations (merge, split) executed within temp directories.
- **Widget Tests:** Cover validation UI (`SplitRangeInput`, merge screen button enablement, page selection toggles).
- **Integration Test:** New scenario in `integration_test/app_test.dart` simulating merge + open flow using small PDFs.
- **Performance Test (Manual):** Documented run instructions for merging 500-page sample in QA build; not automated at this stage.

### 2.7 Simplicity Gate (Phase 2)

- Single repository/datasource pair per feature; avoid additional layers.
- Reuse existing file picker and navigation rather than bespoke flows.
- Progress updates handled within notifiers (no separate service).

---

## Phase 3 — Implementation Planning (TDD-Oriented)

### 3.1 Task Hierarchy (All tasks start as `NOT_STARTED`)

**Root Task: Implement PRD-003 PDF Page Management Tools (NOT_STARTED)**

1. **Task 0.0 — Finalize research artifacts (NOT_STARTED)**
   - Document Syncfusion API references and legal note in project wiki.

2. **Task 1.0 — Feature scaffolding & dependencies (NOT_STARTED)**
   - Update `pubspec.yaml` to add `syncfusion_flutter_pdf` and run `flutter pub get`.
   - Create `lib/features/pdf_page_management/` directory structure (data/domain/presentation) with placeholder files.
   - Register new routes in `core/navigation/app_router.dart` and Discover entry tile in existing hub screen.
   - KISS check before continuing.

3. **Task 2.0 — Requirement FR1 (Merge PDFs) (NOT_STARTED)**
   - **[RED]** Write failing unit tests for `MergePdfsUseCase` validating: requires ≥2 inputs, preserves order, propagates repository failures.
   - **[RED]** Write failing widget test ensuring merge button disabled until two files selected and shows progress indicator when state is loading.
   - **[GREEN]** Implement `MergePdfsUseCase`, repository method, datasource merge logic (append documents, save output), notifier flow, and screen UI wiring.
   - **[GREEN]** Add happy-path integration test merging two sample PDFs and verifying output metadata.
   - **[REFACTOR]** Clean up duplication, extract helper for filename generation, ensure tests green.

4. **Task 3.0 — Requirement FR2 (Merge validation) (NOT_STARTED)**
   - **[RED]** Add failing tests verifying notifier rejects <2 files and surfaces validation message via state.
   - **[GREEN]** Implement UI-level guards and domain validation exceptions (use case returning `Left(FileValidationFailure)`).
   - **[REFACTOR]** Consolidate validation logic shared with FR1.

5. **Task 4.0 — Requirement FR3 (Split PDF) (NOT_STARTED)**
   - **[RED]** Write failing tests for `SplitPdfUseCase` parsing multiple range patterns and delegating properly.
   - **[RED]** Widget test for `SplitRangeInput` highlighting invalid ranges.
   - **[GREEN]** Implement range parser, datasource page extraction (using `PdfDocument.pages.removeAt`), notifier workflow, and screen UI.
   - **[GREEN]** Add integration test splitting sample PDF and verifying resulting page count.
   - **[REFACTOR]** Extract reusable parser utility and ensure documentation updated.

6. **Task 5.0 — Requirement FR4 (Split validation) (NOT_STARTED)**
   - **[RED]** Expand tests for invalid inputs (non-numeric, overlaps).
   - **[GREEN]** Ensure UI disables export and displays inline errors, use cases return `Left(FileValidationFailure)`.
   - **[REFACTOR]** Share validation error mapper with notifier for consistent messaging.

7. **Task 6.0 — Requirement FR5 (Reorder/Manage Pages) (NOT_STARTED)**
   - **[RED]** Unit tests for `ReorderPdfPagesUseCase` verifying order persistence, rotation instructions applied.
   - **[RED]** Widget test for `PageThumbnailGrid` ensuring drag updates local state.
   - **[GREEN]** Implement reorder data models (`PageEditAction`), datasource logic to apply reorder/rotate/delete, notifier + screen interactions, and caching thumbnails.
   - **[GREEN]** Add integration test verifying new PDF page sequence matches action plan.
   - **[REFACTOR]** Optimize thumbnail caching and consolidate progress handling.

8. **Task 7.0 — Requirement FR6 (Extract Pages) (NOT_STARTED)**
   - **[RED]** Tests confirming selected indices exported and duplicates handled.
   - **[GREEN]** Implement extraction use case, datasource slicing logic, notifier + UI for selection.
   - **[REFACTOR]** Merge shared components with reorder feature (grid widget, selection controller).

9. **Task 8.0 — Requirement FR7 (Progress feedback) (NOT_STARTED)**
   - **[RED]** Unit test verifying notifier emits progress values for long-running datasource mocks.
   - **[GREEN]** Implement progress reporting channel (stream/`StreamController<double>`) within datasource and propagate via repository + notifier state.
   - **[REFACTOR]** Ensure progress UI reused across screens.

10. **Task 9.0 — Requirement FR8 (Failure handling) (NOT_STARTED)**
    - **[RED]** Tests asserting failure mapping (corrupt file, disk full) produces `AsyncError` with friendly message.
    - **[GREEN]** Implement failure translation layer and UI messaging (SelectableText per CLAUDE guidelines).
    - **[REFACTOR]** Centralize error copy in presentation layer.

11. **Task 10.0 — Requirement FR9 (Result structuring) (NOT_STARTED)**
    - **[RED]** Unit test verifying repository returns populated `PdfOperationResult` including file metadata.
    - **[GREEN]** Implement result model mapping, integrate with file discovery refresh hook.
    - **[REFACTOR]** Update documentation on metadata structure.

12. **Task 11.0 — Cross-cutting QA & Integration (NOT_STARTED)**
    - Update `integration_test/app_test.dart` with scenario covering merge then open output.
    - Add golden tests for `PageThumbnailGrid` at different sizes if feasible.
    - Run full suite: `flutter analyze`, `flutter test`, instrumentation on physical/emulator for heavy PDF scenario.

13. **Task 12.0 — Documentation & Handoff (NOT_STARTED)**
    - Update `README.md` feature list and usage notes.
    - Document troubleshooting in `docs/mvp/251003.pdf-manipulating.md` appendix.
    - Create QA checklist for PDF ops (input size, failure cases).

### 3.2 Traceability Matrix

| Requirement | Planned Tasks |
|-------------|---------------|
| FR1 | Task 2.0 |
| FR2 | Task 3.0 |
| FR3 | Task 4.0 |
| FR4 | Task 5.0 |
| FR5 | Task 6.0 |
| FR6 | Task 7.0 |
| FR7 | Task 8.0 |
| FR8 | Task 9.0 |
| FR9 | Task 10.0 |

### 3.3 Validation & Quality Gates

- Execute automated checks (`flutter analyze`, `flutter test`, `integration_test`).
- Manual verification for large-file performance.
- Ensure all Riverpod providers regenerated via `dart run build_runner build --delete-conflicting-outputs` post-implementation.

### 3.4 Simplicity Gate (Phase 3)

- Review after each requirement that no extra abstraction introduced.
- Prefer shared widgets/services only when code reuse >2 call sites.
- Defer background isolate optimization unless profiling shows UI jank.

---

## Status & Next Steps

- Awaiting approval to commence implementation per outlined tasks.
- Upon approval, execute tasks sequentially, maintaining Red → Green → Refactor discipline and keeping root task status untouched until kickoff authorization is granted.
