I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

The Flutter PDF reader app follows clean architecture with Riverpod state management. The home screen currently has a FAB that opens a file picker. The file conversion feature is fully implemented with:

- **Existing routes**: `/scan-document`, `/convert-images`, `/convert-document` with type query parameters
- **Notifiers**: `ImageConversionNotifier`, `DocumentConversionNotifier`, `ScanNotifier`
- **Use cases**: All conversion use cases are implemented and wired up via providers

The task is to create a bottom sheet UI that replaces the current FAB behavior, providing quick access to:
- **Create PDF**: Image to PDF, Scan Document
- **PDF Converter**: Docx to PDF, PPT to PDF, XLSX to PDF

The design from `image.png` shows a clean, card-based bottom sheet with rounded corners, a drag handle, and icon-based buttons with specific colors (yellow for image, blue for camera/docx, red for PPT, green for XLSX).


### Approach

I'll create a reusable bottom sheet widget that integrates with the existing file conversion architecture:

1. **Create a new widget** `CreateConvertBottomSheet` in the file_conversion feature's presentation/widgets directory
2. **Modify HomeScreen** to show the bottom sheet when FAB is tapped instead of opening file picker
3. **Design the bottom sheet** with:
   - Drag handle at the top
   - Two sections: "Create PDF" and "PDF Converter"
   - Proper styling matching the app theme and the design from `image.png`
   - Navigation to existing conversion screens with appropriate routes

The bottom sheet will use `showModalBottomSheet` with proper Material 3 styling, rounded corners, and will navigate to the existing conversion screens using GoRouter context.push().


### Reasoning

I explored the project structure to understand the architecture. I examined the home screen to see the current FAB implementation, checked the file conversion feature to understand existing notifiers and providers, reviewed the app router to identify conversion routes, and looked at the theme configuration to ensure consistent styling. I also analyzed the provided design image to understand the exact UI requirements.


## Proposed File Changes

### lib/features/file_conversion/presentation/widgets/create_convert_bottom_sheet.dart(NEW)

Create a new reusable bottom sheet widget that displays the "Create & Convert" UI based on the design from `image.png`.

**Structure:**

1. **Drag Handle**: A small grey horizontal line (width: 40, height: 4) centered at the top with 12px top padding

2. **Title and Sections**: Use proper spacing and typography

3. **Create PDF Section**:
   - Section title: "Create PDF" with `titleMedium` text style
   - Two horizontally aligned rectangular buttons in a Row:
     - **Image to PDF**: Container with yellow background (`Colors.amber.shade100`), rounded corners (12px), padding (16px vertical, 24px horizontal), contains a yellow image icon (`Icons.image`, `Colors.amber.shade700`) and text "Image to PDF"
     - **Scan Document**: Container with light blue background (`Colors.blue.shade100`), rounded corners (12px), padding (16px vertical, 24px horizontal), contains a blue camera icon (`Icons.camera_alt`, `Colors.blue.shade700`) and text "Scan Document"
   - Use `Expanded` widgets for equal width buttons with spacing between them

4. **PDF Converter Section**:
   - Section title: "PDF Converter" with `titleMedium` text style
   - Three circular icon buttons in a Row with `MainAxisAlignment.spaceEvenly`:
     - **Docx to PDF**: Circular container (diameter: 80) with light blue background (`Colors.blue.shade100`), contains a blue document icon (`Icons.description`, `Colors.blue.shade700`) and text "Docx to PDF" below
     - **PPT to PDF**: Circular container (diameter: 80) with light red background (`Colors.red.shade100`), contains a red presentation icon (`Icons.slideshow`, `Colors.red.shade700`) and text "PPT to PDF" below
     - **XLSX to PDF**: Circular container (diameter: 80) with light green background (`Colors.green.shade100`), contains a green spreadsheet icon (`Icons.table_chart`, `Colors.green.shade700`) and text "XLSX to PDF" below
   - Each button should be wrapped in a Column with the icon container and text label

**Navigation Logic:**
- **Image to PDF**: Navigate to `/convert-images` route
- **Scan Document**: Navigate to `/scan-document` route
- **Docx to PDF**: Navigate to `/convert-document?type=docxToPdf` route
- **PPT to PDF**: Navigate to `/convert-document?type=pptToPdf` route
- **XLSX to PDF**: Navigate to `/convert-document?type=xlsxToPdf` route

**Implementation Details:**
- Use `showModalBottomSheet` with `isScrollControlled: false` for standard bottom sheet behavior
- Apply rounded corners to top of bottom sheet using `RoundedRectangleBorder` with `BorderRadius.vertical(top: Radius.circular(20))`
- Add proper padding: 16px horizontal, 12px top for handle, 16px between sections
- Use `SafeArea` at the bottom to handle device notches
- Pop the bottom sheet (Navigator.pop) before navigating to ensure clean navigation stack
- Make buttons tappable with `InkWell` or `GestureDetector` with proper tap feedback
- Add `const` constructors where possible for performance

**Styling:**
- Follow Material 3 design principles
- Use theme colors where appropriate
- Ensure text is readable with proper contrast
- Add subtle elevation or shadow to the bottom sheet
- Icon sizes: 32 for section buttons, 40 for circular converter buttons
- Text styles: Use theme's `titleMedium` for section headers, `bodyMedium` for button labels

### lib/features/file_discovery/presentation/screens/home_screen.dart(MODIFY)

References: 

- lib/features/file_conversion/presentation/widgets/create_convert_bottom_sheet.dart(NEW)

Modify the `HomeScreen` to show the create & convert bottom sheet when the FAB is tapped.

**Changes:**

1. **Add import** for the new bottom sheet widget:
   ```dart
   import '../../../file_conversion/presentation/widgets/create_convert_bottom_sheet.dart';
   ```

2. **Modify the FAB's `onPressed` callback** (around line 102):
   - Change from calling `_selectFileAndNavigate` directly
   - Instead, call a new method `_showCreateConvertBottomSheet()`
   - Update the tooltip to something more appropriate like 'Create or Convert PDF'

3. **Add new method `_showCreateConvertBottomSheet()`**:
   ```dart
   void _showCreateConvertBottomSheet() {
     showModalBottomSheet(
       context: context,
       isScrollControlled: false,
       backgroundColor: Colors.transparent,
       builder: (context) => const CreateConvertBottomSheet(),
     );
   }
   ```

4. **Keep the existing `_selectFileAndNavigate()` method** as it's still used by the `FileManagerBanner` widget (line 92)

**Note:** The bottom sheet widget will handle all navigation to conversion screens internally, so no additional navigation logic is needed in the home screen.