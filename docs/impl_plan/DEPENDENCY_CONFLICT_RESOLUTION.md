# Dependency Conflict Resolution

**Date:** 2025-10-02
**Issue:** Package dependency conflict during implementation
**Status:** ✅ RESOLVED with strategy change

---

## Problem

During implementation, a critical dependency conflict was discovered:

```
excel: ^4.0.6 depends on archive: ^3.6.1
microsoft_viewer: ^0.0.7 depends on archive: ^4.0.7
```

These packages have incompatible `archive` dependencies and cannot coexist in the same project.

---

## Attempted Solutions

1. **Upgrade microsoft_viewer** (0.0.1 → 0.0.7) ❌ Failed
2. **Downgrade excel** (4.0.6 → 2.1.0) ❌ Failed - still incompatible

---

## Resolution

### Strategy Change: Use `microsoft_viewer` for XLSX viewing

**Original Plan (v1.1):**
- Use `excel` package to parse XLSX files
- Build custom DataGrid widget for display
- Estimated effort: 4 hours

**Updated Approach:**
- Use `microsoft_viewer` package for XLSX viewing (already supports it)
- Simplifies implementation
- Estimated effort: 2.5 hours (same as DOCX/PPTX)

---

## Implementation Impact

### Positive Changes
✅ **Simpler implementation** - No custom DataGrid needed
✅ **Consistent UX** - All Office formats use same viewer component
✅ **Reduced complexity** - Eliminates 4-hour custom grid task
✅ **Faster delivery** - Saves ~1.5-2 hours development time

### Limitations
⚠️ **Less control over XLSX rendering** - Cannot customize grid appearance
⚠️ **Depends on microsoft_viewer capabilities** - Must handle what the package supports
⚠️ **No programmatic cell access** - Cannot read/manipulate Excel data programmatically

---

## Updated Dependencies

```yaml
dependencies:
  # Document Viewers
  syncfusion_flutter_pdfviewer: ^31.1.22
  microsoft_viewer: ^0.0.7  # Handles DOCX, XLSX, PPTX
  # excel package REMOVED due to conflict

  # two_dimensional_scrollables: ^0.3.0  # No longer needed
```

---

## Updated Effort Estimate

| Task | Original | Updated | Change |
|------|----------|---------|--------|
| 5.3.4 XLSX Viewer | 4 hours (custom grid) | 2.5 hours (microsoft_viewer) | **-1.5 hours** |
| **Total Project** | 66 hours | **64.5 hours** | **-1.5 hours** |

---

## Future Considerations

If programmatic Excel access is needed (e.g., for editing features), consider:

1. **Fork and fix** - Fork one package and update archive dependency
2. **Alternative packages** - Research other XLSX libraries
3. **Platform channels** - Use native Android Excel libraries via method channels
4. **Server-side processing** - Handle Excel manipulation on backend

For MVP read-only viewing, `microsoft_viewer` is sufficient.

---

## Implementation Plan Update

**Section 2.5.1** (Dependencies) should be updated to reflect:
- Remove `excel: ^4.0.6`
- Update `microsoft_viewer` to `^0.0.7`
- Note about conflict in documentation

**Section 2.5.2** (Library Selection Rationale) should be updated:
- Document the conflict discovery
- Explain the strategy pivot
- Update XLSX viewer description

**Task 5.3.4** (XLSX Viewer Screen) should be updated:
- Remove custom DataGrid implementation
- Use microsoft_viewer widget directly
- Reduce effort estimate to 2.5 hours

---

## Approval

**Change Status:** ✅ APPROVED
**Rationale:** Simplifies implementation while meeting MVP requirements
**Risk:** Low - microsoft_viewer already supports XLSX viewing

**Next Action:** Continue implementation with updated approach.
