# Technology Research for PRD-002: File Conversion Suite

## 1. Camera & Edge Detection

### Options to Evaluate:
1. `google_mlkit_document_scanner` - Google's ML Kit document scanning
2. `edge_detection` - Community package for edge detection
3. `cunning_document_scanner` - Alternative document scanner

### Decision: 
After evaluation of the options:
- `google_mlkit_document_scanner` provides robust document detection capabilities
- `camera` package is needed for camera access
- For now, we'll start with `camera` package and implement edge detection with custom solution

## 2. Office Document Conversion (DOCX/PPT/XLSX to PDF)

### Options to Evaluate:
1. Platform channels to native Android libraries
2. Flutter packages (if available)
3. Cloud-based APIs (if acceptable for privacy)

### Decision:
For MVP scope, we'll start with research on available Flutter packages:
- `docx` for DOCX processing
- Research for PPT/XLSX solutions

## 3. PDF Creation

### Current package:
- `syncfusion_flutter_pdf` - Already available in project (for manipulation)

### For image-to-PDF conversion:
- `pdf` package is recommended for creating PDFs from images

## 4. Image Processing

### For filters (B&W, Grayscale):
- `image` package for image processing operations

## 5. Additional Dependencies

Based on implementation plan requirements:
- `camera` - for camera access
- `image_picker` - for image selection (may be used for testing)
- `pdf` - for PDF creation from images
- `image` - for image processing and filters

## Recommended pubspec.yaml additions:

```yaml
dependencies:
  camera: ^0.11.0+1          # Camera access for document scanning
  image: ^4.2.1              # Image processing for filters
  pdf: ^3.12.0               # PDF creation from images
  # Note: Using existing syncfusion_flutter_pdf for PDF manipulation
  
dev_dependencies:
  # Keep existing build tools
```

For office document conversion (DOCX/PPT/XLSX), we'll need to implement a platform channel approach or find appropriate packages after initial implementation.
