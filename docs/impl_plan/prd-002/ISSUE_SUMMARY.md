---
name: PRD-002 File Conversion Suite
about: Implement PRD-002 per implementation plan with TDD and Feature-First Clean Architecture
title: "PRD-002: File Conversion Suite — Implement per docs/impl_plan/251003.2.prd-002.md"
labels: feature, PRD-002, TDD
assignees: ''
---

## Summary
Implement PRD-002 (File Conversion Suite) following the implementation plan and the TDD approach, strictly adhering to the project’s Feature-First Clean Architecture with Riverpod DI.

## Scope
- Scan Document to PDF (camera, edge detection, review, finalize)
- DOCX to PDF conversion
- Images to PDF (multi-select, reorder, orientation)
- PPT to PDF and XLSX to PDF conversions

## Plans & Specs
- Implementation Plan: `docs/impl_plan/251003.2.prd-002.md`
- Dependency Conflicts: `docs/impl_plan/DEPENDENCY_CONFLICT_RESOLUTION.md`

## Architecture & Guidelines
- Project context and architecture: `CLAUDE.md`
- Developer guidelines (Feature-First Clean Architecture, Riverpod): `docs/agents/dev.md`
- Troubleshooting and repo overview: `README.md`

## Environment (Terminal-Only)
If your environment does not have Flutter/Dart/Android SDK yet, follow the setup steps in `CLAUDE.md` (Environment Setup section). Agents work via terminal/shell; Android Studio or VS Code are NOT required.

## Process & Reporting
- Maintain progress documentation in:
  - `docs/impl_plan/prd-002/IMPLEMENTATION_PROGRESS_REPORT.md` with the same layout in `docs/impl_plan/IMPLEMENTATION_PROGRESS_REPORT.md`
  - `docs/impl_plan/prd-002/IMPLEMENTATION_SESSION_SUMMARY.md` with the same layout in `docs/impl_plan/IMPLEMENTATION_SESSION_SUMMARY.md`
- Record and maintain all dependency issues/resolutions in:
  - `docs/impl_plan/DEPENDENCY_CONFLICT_RESOLUTION.md`
- Follow TDD (Red → Green → Refactor) aligned with the Implementation Plan.

## Acceptance Criteria
- Meets all EARS requirements defined in `docs/impl_plan/251003.2.prd-002.md`
- All unit/widget tests added and passing; CI green
- Routes added and navigable: `/scan-document`, `/scan-review`, `/convert-images`, `/convert-document`
- Conversion flows succeed for Images→PDF and DOCX/PPT/XLSX→PDF (basic happy paths)
- Progress indicator shown for operations > 2 seconds; user-friendly errors
- Proper permission handling (Camera and Storage); converted/scanned files saved to structured folders

## Tasks (High-Level)
- Scaffold feature module `file_conversion/` with domain/data/presentation
- Implement use cases, repositories, and datasources
- Implement notifiers, screens, and widgets (camera, review, conversion)
- Wire DI with Riverpod providers; integrate routes in app router
- Add tests (Red → Green → Refactor per requirement)
- Update progress and session summary docs after each session/milestone
- Update dependency conflict doc if conflicts are discovered/resolved

## Notes
- Open decisions to align with reviewer:
  - Office document conversion approach (native/platform channel vs package vs cloud API)
  - Camera edge detection library choice and performance targets
  - Output file organization and naming conventions (e.g., `Scanned/`, `Converted/`)

## Links
- PRD: `docs/mvp/251003.pdf-manipulating.md#prd-002-file-conversion-suite`

