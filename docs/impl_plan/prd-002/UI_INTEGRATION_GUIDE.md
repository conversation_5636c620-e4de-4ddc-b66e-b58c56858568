# UI Integration Guide: File Conversion Suite (PRD-002)

This document provides UI developers with the information needed to integrate the File Conversion Suite features into the application's UI.

## Available Routes

The following routes have been added to the app router for file conversion features:

### 1. Scan Document to PDF
- **Route**: `/scan-document`
- **Screen**: `ScanCameraScreen`
- **Description**: Allows users to capture images using the device camera and convert them to PDF
- **Usage**: Navigate to this route to start the scanning process
- **Example**: `context.push('/scan-document')`

### 2. Scan Review Screen
- **Route**: `/scan-review`
- **Screen**: `ScanReviewScreen`
- **Description**: Allows users to review, edit, and apply filters to captured images before PDF conversion
- **Usage**: Navigate to this route after capturing images in the scan process
- **Example**: `context.push('/scan-review')`

### 3. Convert Images to PDF
- **Route**: `/convert-images`
- **Screen**: `ImageConversionScreen`
- **Description**: Allows users to convert multiple images to a single PDF file with reordering and filtering capabilities
- **Usage**: Navigate to this route to convert existing images to PDF
- **Example**: `context.push('/convert-images')`

### 4. Generic Document Conversion
- **Route**: `/convert-document`
- **Screen**: `DocumentConversionScreen`
- **Description**: Generic screen for converting various document formats to PDF
- **Usage**: Navigate to this route with a type parameter to specify conversion type
- **Example**: 
  - `context.push('/convert-document?type=docxToPdf')` - Convert DOCX to PDF
  - `context.push('/convert-document?type=pptToPdf')` - Convert PPT to PDF
  - `context.push('/convert-document?type=xlsxToPdf')` - Convert XLSX to PDF
  - `context.push('/convert-document?type=imageToPdf')` - Convert Images to PDF
  - `context.push('/convert-document?type=scanToPdf')` - Scan to PDF

## Riverpod Providers Available

### 1. File Conversion Providers
Import: `lib/features/file_conversion/presentation/providers/file_conversion_providers.dart`

- **`scanNotifierProvider`**: Manages state for document scanning operations
  - Usage: `ref.watch(scanNotifierProvider)`
  - Actions:
    - `ref.read(scanNotifierProvider.notifier).initializeCamera()`
    - `ref.read(scanNotifierProvider.notifier).captureImage()`
    - `ref.read(scanNotifierProvider.notifier).disposeCamera()`

- **`imageConversionNotifierProvider`**: Manages state for image-to-PDF conversion
  - Usage: `ref.watch(imageConversionNotifierProvider)`
  - Actions:
    - `ref.read(imageConversionNotifierProvider.notifier).addImages(imagePaths)`
    - `ref.read(imageConversionNotifierProvider.notifier).removeImage(index)`
    - `ref.read(imageConversionNotifierProvider.notifier).reorderImages(oldIndex, newIndex)`
    - `ref.read(imageConversionNotifierProvider.notifier).startConversion()`

- **`documentConversionNotifierProvider`**: Manages state for document-to-PDF conversion
  - Usage: `ref.watch(documentConversionNotifierProvider)`
  - Actions:
    - `ref.read(documentConversionNotifierProvider.notifier).pickDocument()`
    - `ref.read(documentConversionNotifierProvider.notifier).startConversion()`

## UI Widgets Available

### 1. Camera Controls
File: `lib/features/file_conversion/presentation/widgets/camera_controls_widget.dart`
- **`CameraControlsWidget`**: Pre-built UI controls for camera functionality
- **Props**: `onCapture`, `onSwitchCamera`, `onToggleFlash`, `onClose`

### 2. Edge Detection Overlay
File: `lib/features/file_conversion/presentation/widgets/edge_detection_overlay.dart`
- **`EdgeDetectionOverlay`**: Custom painting widget that shows document boundary detection
- **Props**: `quadPoints` (List of points for document corners)

### 3. Page Filter Selector
File: `lib/features/file_conversion/presentation/widgets/page_filter_selector.dart`
- **`PageFilterSelector`**: UI for selecting image filters
- **Props**: `currentFilter`, `onFilterChanged`

### 4. Page Reorder Widget
File: `lib/features/file_conversion/presentation/widgets/page_reorder_widget.dart`
- **`PageReorderWidget`**: Drag-and-drop interface for reordering pages
- **Props**: `pages`, `onReorder`

### 5. Conversion Progress Widget
File: `lib/features/file_conversion/presentation/widgets/conversion_progress_widget.dart`
- **`ConversionProgressWidget`**: Shows progress during conversion operations
- **Props**: `isVisible`, `progress`, `statusMessage`

## Integration Examples

### Example 1: Add a "Scan Document" Button to Home Screen
```dart
ElevatedButton(
  onPressed: () => context.push('/scan-document'),
  child: const Text('Scan Document'),
)
```

### Example 2: Add a "Convert Images" Button with Provider Access
```dart
Consumer(
  builder: (context, ref, child) {
    final state = ref.watch(imageConversionNotifierProvider);
    
    return ElevatedButton(
      onPressed: state.status == ConversionStatus.idle 
        ? () => context.push('/convert-images')
        : null,
      child: const Text('Convert Images to PDF'),
    );
  },
)
```

### Example 3: Call Document Conversion Directly
```dart
// To convert DOCX to PDF
ref.read(documentConversionNotifierProvider.notifier).pickAndConvert(ConversionType.docxToPdf);

// To convert PPT to PDF
ref.read(documentConversionNotifierProvider.notifier).pickAndConvert(ConversionType.pptToPdf);
```

## State Management Patterns

### Conversion Status States
All conversion providers expose a `status` property with the following possible values:
- `ConversionStatus.idle` - Ready for action
- `ConversionStatus.loading` - Operation in progress
- `ConversionStatus.success` - Operation completed successfully
- `ConversionStatus.error` - Operation failed

### Error Handling
When a conversion operation fails, the providers will have:
- `error` property containing the error message
- `status` property set to `ConversionStatus.error`

## Permissions Required

The file conversion features require the following permissions:
- **Camera Permission**: For scan document functionality
- **Storage Permission**: For accessing documents and saving converted files

These permissions are requested automatically when needed, but UI developers should be aware of potential permission denial scenarios and provide appropriate user feedback.

## Testing Considerations

When integrating these features into your UI:
1. Verify navigation works correctly between conversion screens
2. Test error states and ensure appropriate UI feedback is provided
3. Test loading states with the ConversionProgressWidget
4. Ensure proper resource cleanup (especially camera resources)
5. Test with different document types and sizes

## Additional Notes

- All providers are fully type-safe and follow Riverpod best practices
- Widget tests exist for all UI components (see test files for usage examples)
- Integration tests are available for end-to-end testing
- All features follow the same architecture patterns as the existing app features