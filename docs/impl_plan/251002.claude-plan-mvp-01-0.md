# Implementation Plan: Document Reader MVP

**Date**: October 2, 2025
**Project**: Flutter Document Reader Mobile Application (Android MVP)
**Planner**: Henry - Specification-Driven Development Agent
**Framework**: Flutter 3.x with Dart SDK 3.9.0+
**Target Platform**: Android (API 21+)

---

## Executive Summary

This implementation plan provides a comprehensive, specification-driven approach to building a Document Reader MVP for Android. The application will enable users to discover, filter, and view documents in PDF, DOCX, XLSX, and PPTX formats. The plan adheres to anti-over-engineering principles (KISS & YAGNI), prioritizes security and reliability, and maintains complete traceability from requirements to implementation tasks.

**Core Philosophy**: "Spec First, Plan Second, Keep It Simple" - Deliver only what's needed for the MVP with clean, maintainable code.

---

## PHASE 1: REQUIREMENTS ANALYSIS (EARS NOTATION)

### 1.1 Target Environment Analysis

**Programming Language**: Dart 3.9.0+
**Framework**: Flutter (latest stable)
**Platform**: Android (API Level 21+, targeting API 33+)
**Architecture**: MVVM with Provider (simple, appropriate for MVP scope)
**Build System**: Gradle (Kotlin DSL)

### 1.2 Functional Requirements (EARS Format)

#### FR-1: Storage Permission Management
- **FR-1.1**: WHEN the app launches for the first time THE SYSTEM SHALL request storage permissions using platform-appropriate APIs
- **FR-1.2**: WHEN the user grants storage permission THE SYSTEM SHALL scan device storage for compatible documents
- **FR-1.3**: WHEN the user denies storage permission THE SYSTEM SHALL display a message explaining the permission requirement and provide options to grant permission or manually select files
- **FR-1.4**: WHEN the user taps "Grant Permission" THE SYSTEM SHALL re-trigger the permission request dialog
- **FR-1.5**: WHEN the user taps "Select a File" THE SYSTEM SHALL open the system file picker

#### FR-2: File Discovery
- **FR-2.1**: WHEN storage permission is granted THE SYSTEM SHALL scan device storage for files with extensions: .pdf, .docx, .xlsx, .pptx
- **FR-2.2**: WHEN scanning completes THE SYSTEM SHALL populate the home screen with discovered documents
- **FR-2.3**: WHEN a user manually selects a file THE SYSTEM SHALL add it to the recent files list
- **FR-2.4**: WHEN scanning files THE SYSTEM SHALL validate file paths to prevent directory traversal attacks

#### FR-3: File Display & Filtering
- **FR-3.1**: WHEN the home screen loads THE SYSTEM SHALL display all discovered documents in a scrollable list
- **FR-3.2**: WHEN displaying each file THE SYSTEM SHALL show: filename, file type icon, date modified, and file size
- **FR-3.3**: WHEN the user taps a format tab (ALL/PDF/WORD/EXCEL/PPT) THE SYSTEM SHALL filter the displayed list accordingly
- **FR-3.4**: WHEN no files match the selected filter THE SYSTEM SHALL display an appropriate empty state message

#### FR-4: Document Viewing
- **FR-4.1**: WHEN the user taps a PDF file THE SYSTEM SHALL open it in a PDF viewer with vertical scrolling and pinch-to-zoom
- **FR-4.2**: WHEN the user taps a DOCX file THE SYSTEM SHALL open it in a Word viewer with text rendering, basic formatting, vertical scrolling, and pinch-to-zoom
- **FR-4.3**: WHEN the user taps an XLSX file THE SYSTEM SHALL open it in an Excel viewer with grid display, horizontal/vertical scrolling
- **FR-4.4**: WHEN the user taps a PPTX file THE SYSTEM SHALL open it in a PowerPoint viewer with slide display and left/right swipe navigation
- **FR-4.5**: WHEN viewing a document THE SYSTEM SHALL provide a back button to return to the home screen
- **FR-4.6**: WHEN a document fails to load THE SYSTEM SHALL display an error message and return to the home screen

#### FR-5: File Security & Validation
- **FR-5.1**: WHEN validating file selection THE SYSTEM SHALL verify file extensions match allowed types
- **FR-5.2**: WHEN opening a file THE SYSTEM SHALL validate the file path contains no directory traversal sequences
- **FR-5.3**: WHEN scanning files THE SYSTEM SHALL ignore files with null bytes in filenames
- **FR-5.4**: WHEN processing files THE SYSTEM SHALL enforce a maximum file size limit to prevent memory issues

### 1.3 Non-Functional Requirements

#### NFR-1: Performance
- **NFR-1.1**: The app SHALL launch within 2 seconds on mid-range Android devices
- **NFR-1.2**: File scanning SHALL complete within 5 seconds for up to 1000 documents
- **NFR-1.3**: Document rendering SHALL begin within 1 second of file selection
- **NFR-1.4**: The app SHALL maintain 60fps during normal scrolling operations

#### NFR-2: Security
- **NFR-2.1**: The app SHALL validate all file paths before access
- **NFR-2.2**: The app SHALL use Android Scoped Storage (API 29+) where applicable
- **NFR-2.3**: The app SHALL not store or transmit file contents outside the device
- **NFR-2.4**: The app SHALL validate MIME types in addition to file extensions
- **NFR-2.5**: The app SHALL implement proper error handling to prevent information disclosure

#### NFR-3: Reliability
- **NFR-3.1**: The app SHALL handle missing or corrupted files gracefully
- **NFR-3.2**: The app SHALL recover from viewer crashes without losing navigation state
- **NFR-3.3**: The app SHALL persist the last selected tab filter across app restarts

#### NFR-4: Usability
- **NFR-4.1**: The UI SHALL follow Material Design 3 guidelines
- **NFR-4.2**: All interactive elements SHALL have a minimum touch target of 48dp
- **NFR-4.3**: Error messages SHALL be clear and actionable
- **NFR-4.4**: The app SHALL support both light and system-default themes

#### NFR-5: Compatibility
- **NFR-5.1**: The app SHALL support Android API 21+ (Lollipop and above)
- **NFR-5.2**: The app SHALL target Android API 33+ for optimal permission handling
- **NFR-5.3**: The app SHALL be compatible with Android 13+ granular media permissions

### 1.4 Constraints & Assumptions

#### Technical Constraints
1. Android-only platform for MVP (no iOS, Web, Desktop)
2. Must use existing Flutter SDK 3.9.0+ without major version changes
3. Must work with current Gradle build configuration
4. Limited to viewing documents (no editing, converting, or advanced features)

#### Security Constraints
1. Must comply with Android 13+ permission model
2. Must prevent directory traversal attacks
3. Must validate file types before processing
4. Must not require MANAGE_EXTERNAL_STORAGE permission (use SAF instead)

#### Design Constraints
1. Simple, flat architecture (no complex abstractions)
2. Minimal dependencies (only essential packages)
3. No custom native platform code unless absolutely necessary
4. Standard Material Design components (no custom UI framework)

#### Assumptions
1. Users have at least Android 5.0 (API 21) devices
2. Device has sufficient memory to load typical office documents
3. Files are stored in accessible locations (not app-specific directories of other apps)
4. Users understand basic file management concepts
5. Internet connection is NOT required (fully offline app)

### 1.5 Out of Scope for MVP

The following features are explicitly excluded from this MVP:
- File editing, annotation, or modification
- File conversion between formats
- Cloud storage integration
- File sharing, renaming, or deletion
- Search within document content
- Bookmarks or favorites
- Recent files beyond manual selection tracking
- AI-powered features (translate, summarize, etc.)
- Advertisement integration
- Folder organization or custom collections
- Dark mode (using system default only)
- Multi-language localization (English only)

---

## PHASE 2: SPECIFICATION CREATION

### 2.1 Technology Stack Selection (Research-Driven)

Based on comprehensive research of Flutter packages in 2025, the following stack has been selected for optimal simplicity, security, and reliability:

#### Document Viewing Libraries

**PDF Viewer**: `pdfrx` (recommended)
- **Rationale**: Built on PDFium, cross-platform, actively maintained, free/open-source
- **Security**: Uses Google's PDFium library (industry-standard, well-audited)
- **Alternative**: `flutter_pdfview` (simpler but mobile-only)
- **Version**: Latest stable from pub.dev

**DOCX Viewer**: `docx_file_viewer` (recommended) or web-based fallback
- **Rationale**: Recently updated (Dec 2024), attempts accurate rendering
- **Security**: Processes files locally without network access
- **Limitation**: May have formatting issues with complex documents (acceptable for MVP)
- **Fallback**: Display rendered HTML using `flutter_html` if needed

**XLSX Viewer**: Custom implementation using `excel` package + `pluto_grid`
- **Rationale**: `excel` parses files, `pluto_grid` renders spreadsheet UI
- **Security**: Pure Dart implementation, no native dependencies
- **Limitation**: Basic rendering only (no advanced formulas or charts)
- **Alternative**: Display as table using `data_table_2`

**PPTX Viewer**: Web view fallback or image extraction approach
- **Rationale**: No mature native PPTX viewer exists for Flutter in 2025
- **Security**: Render slides as images using external converter if needed
- **Limitation**: Limited interactivity (acceptable for MVP viewing-only requirement)
- **MVP Approach**: Use simple slide-by-slide image display or defer PPTX to future version

#### Core Dependencies

**Permission Handling**: `permission_handler` ^11.0.0
- **Rationale**: Industry standard for Flutter permissions, well-maintained
- **Security**: Handles Android 13+ granular permissions correctly
- **Alternative**: Manual platform channel implementation (unnecessary complexity)

**File Picking**: `file_picker` ^6.0.0
- **Rationale**: Cross-platform, uses Android SAF (Storage Access Framework)
- **Security**: Leverages system picker (no custom file browsing code)
- **Benefit**: No permissions needed when user explicitly selects files

**State Management**: `provider` ^6.0.0
- **Rationale**: Simple, official recommendation, sufficient for MVP complexity
- **Alternative**: BLoC (over-engineering for current scope)

**Path Utilities**: `path` (from Dart SDK)
- **Rationale**: Standard library, secure path manipulation
- **Security**: Provides basename, extension, join with validation

**File Size Formatting**: `filesize` or simple custom formatter
- **Rationale**: Human-readable file size display (e.g., "2.3 MB")

#### Development Dependencies
- `flutter_lints` ^5.0.0 (already in project)
- `flutter_test` (SDK)
- `mockito` or `mocktail` for testing (if time permits)

### 2.2 Application Architecture (MVVM with Provider)

```
lib/
├── main.dart                          # App entry point
├── models/                            # Data models
│   ├── document_file.dart             # File metadata model
│   └── file_type.dart                 # Enum for file types
├── providers/                         # State management
│   ├── file_provider.dart             # File scanning & filtering logic
│   └── permission_provider.dart       # Permission state management
├── screens/                           # UI screens
│   ├── home_screen.dart               # Main file list screen
│   └── viewer_screen.dart             # Document viewer screen
├── widgets/                           # Reusable widgets
│   ├── file_list_item.dart            # File list tile
│   ├── filter_tabs.dart               # Format filter tabs
│   ├── permission_request_view.dart   # Permission denied state
│   └── empty_state_view.dart          # No files found state
├── services/                          # Business logic
│   ├── file_scanner_service.dart      # Device file scanning
│   ├── permission_service.dart        # Permission checking/requesting
│   └── file_validator_service.dart    # Security validation
├── utils/                             # Helpers
│   ├── constants.dart                 # App constants
│   └── formatters.dart                # File size formatting
└── viewers/                           # Format-specific viewers
    ├── pdf_viewer_widget.dart         # PDF viewing
    ├── docx_viewer_widget.dart        # Word viewing
    ├── xlsx_viewer_widget.dart        # Excel viewing
    └── pptx_viewer_widget.dart        # PowerPoint viewing (MVP: placeholder)
```

**Architecture Justification**:
- **MVVM Pattern**: Clear separation between UI (View), business logic (ViewModel/Provider), and data (Model)
- **Provider**: Simple state management, no unnecessary boilerplate
- **Service Layer**: Encapsulates file I/O, permissions, and validation logic
- **Widget Composition**: Reusable UI components following Flutter best practices
- **Flat Structure**: No deep nesting, easy navigation, suitable for MVP scope

### 2.3 Data Models

#### DocumentFile Model
```dart
class DocumentFile {
  final String path;           // Absolute file path
  final String name;           // Display name (basename)
  final FileType type;         // Enum: pdf, docx, xlsx, pptx
  final int size;              // File size in bytes
  final DateTime modifiedDate; // Last modified timestamp

  // Security: Validated path (no traversal sequences)
  // Security: Validated extension matching type
}
```

#### FileType Enum
```dart
enum FileType {
  pdf,
  docx,
  xlsx,
  pptx;

  // Extension mapping: .pdf -> FileType.pdf
  // Icon mapping: FileType.pdf -> Icons.picture_as_pdf
}
```

### 2.4 Core Services Specification

#### FileValidatorService
**Responsibility**: Security validation for file operations

**Methods**:
- `bool isValidFilePath(String path)`: Check for directory traversal
- `bool isAllowedExtension(String path)`: Verify extension whitelist
- `bool isValidFileSize(int bytes)`: Check file size limit
- `String? sanitizeFilename(String name)`: Remove dangerous characters
- `bool hasNullByteInjection(String path)`: Detect null byte attacks

**Security Rules**:
- Reject paths containing: `..`, `//`, `\0`, `~`
- Whitelist extensions: `.pdf`, `.docx`, `.xlsx`, `.pptx`
- Max file size: 100MB (configurable)
- Use `path` package for all path operations

#### FileScannerService
**Responsibility**: Discover documents on device

**Methods**:
- `Future<List<DocumentFile>> scanDeviceStorage()`: Find all compatible files
- `DocumentFile createDocumentFile(File file)`: Parse file metadata

**Implementation Strategy**:
- Use `Directory` class to traverse storage
- Filter by extension during traversal (performance optimization)
- Validate each file path using `FileValidatorService`
- Handle permission errors gracefully
- Limit scan depth to prevent infinite recursion
- Use async/await with proper error handling

**Android Storage Paths**:
- Primary: `/storage/emulated/0/`
- Common folders: `/Documents`, `/Downloads`, `/DCIM`
- Respect scoped storage boundaries (API 29+)

#### PermissionService
**Responsibility**: Handle storage permission requests

**Methods**:
- `Future<bool> checkPermission()`: Check current permission status
- `Future<bool> requestPermission()`: Request storage permission
- `Future<void> openAppSettings()`: Navigate to app settings

**Android 13+ Handling**:
- Request `READ_MEDIA_IMAGES` and `READ_MEDIA_VIDEO` if needed
- For older versions: `READ_EXTERNAL_STORAGE`
- Never request `MANAGE_EXTERNAL_STORAGE` (violates Play Store policy)
- Use `file_picker` as permission-free alternative

### 2.5 Provider Specifications

#### FileProvider
**State Management**: File list, filtering, loading states

**State Properties**:
- `List<DocumentFile> allFiles`: All discovered files
- `List<DocumentFile> filteredFiles`: Currently displayed files
- `FileType? selectedFilter`: Current filter (null = ALL)
- `bool isLoading`: Scanning in progress
- `String? errorMessage`: Error state

**Methods**:
- `void setFilter(FileType? type)`: Apply filter
- `Future<void> scanFiles()`: Trigger file scan
- `void addManualFile(DocumentFile file)`: Add from file picker
- `void clearError()`: Reset error state

**Business Logic**:
- Filter implementation: `allFiles.where((f) => f.type == selectedFilter)`
- Sort files: by modified date (newest first)
- Error handling: catch all exceptions, set user-friendly messages

#### PermissionProvider
**State Management**: Permission status

**State Properties**:
- `bool hasPermission`: Permission granted status
- `bool isChecking`: Permission check in progress

**Methods**:
- `Future<void> checkPermission()`: Update permission status
- `Future<void> requestPermission()`: Request and update status

### 2.6 UI/UX Design Specification

#### Home Screen Layout
```
┌─────────────────────────────┐
│ ☰  Document Reader          │ AppBar
├─────────────────────────────┤
│ [ALL][PDF][WORD][EXCEL][PPT]│ Filter Tabs (TabBar)
├─────────────────────────────┤
│ 📄 Filename.pdf         2.3MB│ File List Item
│    Modified: Jan 15, 2025   │
├─────────────────────────────┤
│ 📘 Report.docx          1.5MB│
│    Modified: Jan 14, 2025   │
├─────────────────────────────┤
│ 📊 Data.xlsx            854KB│
│    Modified: Jan 10, 2025   │
└─────────────────────────────┘
```

**Components**:
- **AppBar**: Title "Document Reader", optional menu for settings (future)
- **TabBar**: Horizontal scrollable tabs with file type filters
- **ListView**: Scrollable file list with pull-to-refresh
- **ListTile**: File icon, name, size, date (Material 3 design)
- **FloatingActionButton**: Manual file picker (when permission denied)

#### Permission Denied State
```
┌─────────────────────────────┐
│ ☰  Document Reader          │
├─────────────────────────────┤
│                             │
│         🔒                  │
│  Storage Permission         │
│     Required                │
│                             │
│  Grant access to discover   │
│  documents automatically    │
│                             │
│  [Grant Permission]         │
│  [Select File Manually]     │
│                             │
└─────────────────────────────┘
```

#### Viewer Screen Layout
```
┌─────────────────────────────┐
│ ←  Filename.pdf             │ AppBar with back button
├─────────────────────────────┤
│                             │
│                             │
│   [Document Content]        │
│   (Format-specific viewer)  │
│                             │
│                             │
└─────────────────────────────┘
```

**Viewer Interactions**:
- PDF: Vertical scroll, pinch-to-zoom
- DOCX: Vertical scroll, pinch-to-zoom
- XLSX: Horizontal/vertical scroll, pan
- PPTX: Swipe left/right between slides

### 2.7 Security Design

#### Input Validation Layer
All file paths MUST pass through `FileValidatorService` before any file operation:
1. Extension whitelist check
2. Path traversal pattern detection
3. Null byte injection check
4. File size validation

#### Permission Security
- Use least-privilege principle (SAF file picker preferred)
- Request permissions with clear user explanation
- Gracefully handle permission denial
- Never escalate to `MANAGE_EXTERNAL_STORAGE`

#### Error Handling Strategy
- Catch all file I/O exceptions
- Display user-friendly error messages
- Log errors for debugging (use `debugPrint`)
- Never expose file system paths in error messages
- Implement global error boundary for viewer crashes

#### Data Privacy
- No network requests (fully offline app)
- No analytics or telemetry in MVP
- No file content caching outside system cache
- No clipboard access for sensitive data

### 2.8 Sequence Diagrams

#### File Discovery Flow (Permission Granted)
```
User -> HomeScreen: Open App
HomeScreen -> PermissionProvider: checkPermission()
PermissionProvider -> PermissionService: checkPermission()
PermissionService --> PermissionProvider: true
PermissionProvider --> HomeScreen: hasPermission = true
HomeScreen -> FileProvider: scanFiles()
FileProvider -> FileScannerService: scanDeviceStorage()
FileScannerService -> FileValidator: isValidFilePath()
FileValidator --> FileScannerService: valid files
FileScannerService --> FileProvider: List<DocumentFile>
FileProvider --> HomeScreen: notifyListeners()
HomeScreen: Display file list
```

#### File Selection and Viewing Flow
```
User -> HomeScreen: Tap file item
HomeScreen -> Navigator: push ViewerScreen(file)
ViewerScreen: Load viewer widget based on file.type
ViewerScreen -> PdfViewerWidget: (if PDF)
PdfViewerWidget -> pdfrx: Load file
pdfrx --> PdfViewerWidget: Display PDF
User -> ViewerScreen: Tap back button
ViewerScreen -> Navigator: pop()
Navigator --> HomeScreen: Return
```

#### Manual File Selection Flow (Permission Denied)
```
User -> HomeScreen: Tap "Select File"
HomeScreen -> FilePicker: pickFiles(type: custom)
FilePicker -> Android SAF: Open system picker
User -> Android SAF: Select file
Android SAF --> FilePicker: File path
FilePicker --> HomeScreen: PlatformFile
HomeScreen -> FileProvider: addManualFile()
FileProvider -> FileValidator: isValidFilePath()
FileValidator --> FileProvider: valid
FileProvider --> HomeScreen: notifyListeners()
HomeScreen -> Navigator: push ViewerScreen(file)
```

---

## PHASE 3: IMPLEMENTATION PLANNING

### 3.1 Implementation Tasks Overview

The implementation is broken down into 8 major task groups with clear dependencies and execution order. Each task is designed following anti-over-engineering principles: no unnecessary abstractions, minimal dependencies, and direct implementation approaches.

**Estimated Total Effort**: 5-7 days for experienced Flutter developer
**Critical Path**: Setup → Models → Services → Providers → UI → Viewers → Testing

### 3.2 Task Breakdown (Task Management Tool Format)

#### ROOT TASK: Build Document Reader MVP for Android
**Status**: NOT_STARTED
**Description**: Complete implementation of a Flutter mobile app for viewing PDF, DOCX, XLSX, and PPTX documents on Android with file discovery, filtering, and secure viewing capabilities.

---

#### TASK 1: Project Setup and Dependencies
**Parent**: ROOT
**Status**: NOT_STARTED
**Effort**: 1 hour
**Dependencies**: None

**Subtasks**:
1. **TASK 1.1**: Update pubspec.yaml with required dependencies
   - Add `pdfrx` (latest stable)
   - Add `docx_file_viewer` (latest stable)
   - Add `excel` (latest stable)
   - Add `pluto_grid` (latest stable)
   - Add `file_picker` ^6.0.0
   - Add `permission_handler` ^11.0.0
   - Add `provider` ^6.0.0
   - Add `path` (from SDK)
   - Add `filesize` ^2.0.0 (or use custom formatter)

2. **TASK 1.2**: Update Android manifest for permissions
   - Add `<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32" />`
   - Add `<uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />` (API 33+)
   - Add `<uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />` (API 33+)
   - Verify `minSdkVersion` is 21, `targetSdkVersion` is 33+

3. **TASK 1.3**: Run `flutter pub get` and verify dependency resolution

4. **TASK 1.4**: Update iOS permissions in Info.plist (for future cross-platform)
   - Add `NSPhotoLibraryUsageDescription` (defer to future if Android-only)

**Acceptance Criteria**:
- All dependencies resolve without conflicts
- App builds successfully on Android
- No compilation errors

---

#### TASK 2: Create Data Models
**Parent**: ROOT
**Status**: NOT_STARTED
**Effort**: 1 hour
**Dependencies**: TASK 1

**Subtasks**:
1. **TASK 2.1**: Create `lib/models/file_type.dart`
   - Define enum: `pdf`, `docx`, `xlsx`, `pptx`
   - Add extension mapping method: `static FileType? fromExtension(String ext)`
   - Add icon mapping method: `IconData get icon`
   - Add display name method: `String get displayName`

2. **TASK 2.2**: Create `lib/models/document_file.dart`
   - Define class with properties: `path`, `name`, `type`, `size`, `modifiedDate`
   - Add constructor with validation
   - Add `fromFile(File file)` factory constructor
   - Add `copyWith` method for immutability
   - Override `==` and `hashCode` for comparison

**Acceptance Criteria**:
- Models are immutable (final fields)
- All methods have clear documentation comments
- No business logic in models (pure data classes)
- Type-safe enum usage

---

#### TASK 3: Implement Core Services
**Parent**: ROOT
**Status**: NOT_STARTED
**Effort**: 4 hours
**Dependencies**: TASK 2

**Subtasks**:
1. **TASK 3.1**: Create `lib/services/file_validator_service.dart`
   - Implement `isValidFilePath(String path)` - detect `..`, `//`, `\0`
   - Implement `isAllowedExtension(String path)` - whitelist check
   - Implement `isValidFileSize(int bytes)` - max 100MB
   - Implement `sanitizeFilename(String name)` - remove dangerous chars
   - Add comprehensive unit tests for security validation

2. **TASK 3.2**: Create `lib/services/permission_service.dart`
   - Implement `Future<bool> checkPermission()` using `permission_handler`
   - Implement `Future<bool> requestPermission()` with Android 13+ handling
   - Implement `Future<void> openAppSettings()` using `permission_handler`
   - Handle different Android API levels gracefully

3. **TASK 3.3**: Create `lib/services/file_scanner_service.dart`
   - Implement `Future<List<DocumentFile>> scanDeviceStorage()`
   - Use `Directory` class to traverse common folders
   - Filter by extension during scan (performance)
   - Validate each file with `FileValidatorService`
   - Add error handling for permission errors
   - Limit scan depth to 5 levels (prevent infinite recursion)
   - Use async/await with proper exception catching

**Acceptance Criteria**:
- All services have comprehensive error handling
- File validation prevents directory traversal attacks
- Permission handling works on Android 10, 11, 12, 13+
- File scanning completes within 5 seconds for 1000 files
- Services are testable (pure functions, dependency injection)

---

#### TASK 4: Implement State Management (Providers)
**Parent**: ROOT
**Status**: NOT_STARTED
**Effort**: 3 hours
**Dependencies**: TASK 3

**Subtasks**:
1. **TASK 4.1**: Create `lib/providers/permission_provider.dart`
   - Extend `ChangeNotifier`
   - Add `bool hasPermission`, `bool isChecking` state properties
   - Implement `Future<void> checkPermission()` calling `PermissionService`
   - Implement `Future<void> requestPermission()` with state updates
   - Call `notifyListeners()` after state changes

2. **TASK 4.2**: Create `lib/providers/file_provider.dart`
   - Extend `ChangeNotifier`
   - Add state properties: `allFiles`, `filteredFiles`, `selectedFilter`, `isLoading`, `errorMessage`
   - Implement `Future<void> scanFiles()` calling `FileScannerService`
   - Implement `void setFilter(FileType? type)` with filtering logic
   - Implement `void addManualFile(DocumentFile file)` for manual selection
   - Implement `void clearError()` to reset error state
   - Sort files by modified date (newest first)
   - Call `notifyListeners()` after all state changes

**Acceptance Criteria**:
- Providers follow Flutter best practices (ChangeNotifier pattern)
- State updates trigger UI rebuilds correctly
- Error states are properly managed
- Filtering logic is efficient (no unnecessary re-scans)

---

#### TASK 5: Build Reusable Widgets
**Parent**: ROOT
**Status**: NOT_STARTED
**Effort**: 3 hours
**Dependencies**: TASK 4

**Subtasks**:
1. **TASK 5.1**: Create `lib/widgets/filter_tabs.dart`
   - Build `TabBar` with 5 tabs: ALL, PDF, WORD, EXCEL, PPT
   - Use `FileType` enum for tab generation
   - Add tab selection callback
   - Style with Material 3 design

2. **TASK 5.2**: Create `lib/widgets/file_list_item.dart`
   - Build `ListTile` displaying file icon, name, size, date
   - Format file size using `filesize` package or custom formatter
   - Format date using `DateFormat` (add `intl` package if needed)
   - Add tap callback
   - Ensure 48dp minimum touch target

3. **TASK 5.3**: Create `lib/widgets/permission_request_view.dart`
   - Build centered layout with icon, message, two buttons
   - Add "Grant Permission" button calling `PermissionProvider.requestPermission()`
   - Add "Select File Manually" button opening file picker
   - Use Material 3 elevated buttons

4. **TASK 5.4**: Create `lib/widgets/empty_state_view.dart`
   - Build centered layout with icon and message
   - Display different messages based on context (no files, no filter matches)
   - Use Material 3 design system

**Acceptance Criteria**:
- All widgets are reusable and composable
- Widgets follow Material 3 design guidelines
- Touch targets meet accessibility standards (48dp)
- Clear visual feedback for interactions

---

#### TASK 6: Build Main Screens
**Parent**: ROOT
**Status**: NOT_STARTED
**Effort**: 4 hours
**Dependencies**: TASK 5

**Subtasks**:
1. **TASK 6.1**: Create `lib/screens/home_screen.dart`
   - Build `Scaffold` with `AppBar` and `body`
   - Add `FilterTabs` widget below `AppBar`
   - Use `Consumer<FileProvider>` to listen to file list changes
   - Use `Consumer<PermissionProvider>` for permission state
   - Display `PermissionRequestView` when permission denied
   - Display `ListView` of `FileListItem` when files available
   - Display `EmptyStateView` when no files found
   - Add `RefreshIndicator` for pull-to-refresh
   - Add `FloatingActionButton` for manual file picker
   - Implement navigation to `ViewerScreen` on file tap
   - Handle loading state with `CircularProgressIndicator`
   - Display error state with `SnackBar`

2. **TASK 6.2**: Create `lib/screens/viewer_screen.dart`
   - Accept `DocumentFile` as constructor parameter
   - Build `Scaffold` with `AppBar` showing filename
   - Add back button in `AppBar`
   - Use switch statement on `file.type` to load appropriate viewer widget
   - Handle viewer loading errors with try-catch
   - Display error dialog on failure and pop back to home

**Acceptance Criteria**:
- Home screen displays all states correctly (loading, loaded, empty, error, permission denied)
- Navigation works smoothly between screens
- Pull-to-refresh triggers file re-scan
- Manual file picker adds files to the list
- Error handling prevents app crashes

---

#### TASK 7: Implement Document Viewers
**Parent**: ROOT
**Status**: NOT_STARTED
**Effort**: 6 hours
**Dependencies**: TASK 6

**Subtasks**:
1. **TASK 7.1**: Create `lib/viewers/pdf_viewer_widget.dart`
   - Use `pdfrx` package `PdfViewer` widget
   - Pass file path to `PdfViewer.file()`
   - Enable vertical scrolling and pinch-to-zoom (default in pdfrx)
   - Add error handling for file loading failures
   - Display loading indicator while PDF loads

2. **TASK 7.2**: Create `lib/viewers/docx_viewer_widget.dart`
   - Use `docx_file_viewer` package widget
   - Pass file path to viewer
   - Handle rendering errors gracefully
   - Add fallback message for unsupported formatting
   - Display loading indicator while DOCX loads

3. **TASK 7.3**: Create `lib/viewers/xlsx_viewer_widget.dart`
   - Use `excel` package to parse XLSX file
   - Read file bytes using `File.readAsBytes()`
   - Decode with `Excel.decodeBytes()`
   - Build custom `DataTable` or use `pluto_grid` for rendering
   - Implement horizontal/vertical scrolling
   - Handle large spreadsheets with pagination or lazy loading
   - Display error message for corrupted files

4. **TASK 7.4**: Create `lib/viewers/pptx_viewer_widget.dart`
   - **MVP Approach**: Display placeholder message "PPTX viewer coming soon"
   - Add simple centered text with file information
   - **Future Enhancement Note**: Implement slide rendering when library available

**Acceptance Criteria**:
- PDF viewer displays documents with zoom and scroll
- DOCX viewer renders text and basic formatting
- XLSX viewer displays grid with scrolling
- PPTX viewer shows placeholder (acceptable for MVP)
- All viewers handle errors without crashing
- Loading states provide user feedback

---

#### TASK 8: App Initialization and Configuration
**Parent**: ROOT
**Status**: NOT_STARTED
**Effort**: 2 hours
**Dependencies**: TASK 7

**Subtasks**:
1. **TASK 8.1**: Update `lib/main.dart`
   - Add `MultiProvider` setup for `FileProvider` and `PermissionProvider`
   - Configure `MaterialApp` with Material 3 theme
   - Set `HomeScreen` as the home route
   - Add error handling with global error boundary
   - Initialize providers on app start

2. **TASK 8.2**: Create `lib/utils/constants.dart`
   - Define supported extensions: `['.pdf', '.docx', '.xlsx', '.pptx']`
   - Define max file size: `100 * 1024 * 1024` (100MB)
   - Define scan depth limit: `5`
   - Define app name and version

3. **TASK 8.3**: Create `lib/utils/formatters.dart`
   - Implement `String formatFileSize(int bytes)` returning human-readable size
   - Implement `String formatDate(DateTime date)` returning relative or absolute date

**Acceptance Criteria**:
- App launches successfully
- Providers are initialized correctly
- Material 3 theme is applied
- Constants are centralized and reusable
- Error boundary catches unhandled exceptions

---

#### TASK 9: Testing and Quality Assurance
**Parent**: ROOT
**Status**: NOT_STARTED
**Effort**: 4 hours
**Dependencies**: TASK 8

**Subtasks**:
1. **TASK 9.1**: Write unit tests for services
   - Test `FileValidatorService`: path traversal detection, extension validation, file size limits
   - Test `PermissionService`: permission states (mocked)
   - Test `FileScannerService`: file discovery (mocked file system)

2. **TASK 9.2**: Write unit tests for providers
   - Test `FileProvider`: filtering logic, state updates
   - Test `PermissionProvider`: permission state transitions

3. **TASK 9.3**: Write widget tests for UI components
   - Test `FileListItem`: displays correct information
   - Test `FilterTabs`: tab selection works
   - Test `PermissionRequestView`: buttons trigger callbacks

4. **TASK 9.4**: Perform manual testing on physical Android device
   - Test on Android 10, 11, 12, 13 (if possible)
   - Test permission flow (grant, deny, manual selection)
   - Test file discovery and filtering
   - Test each viewer type (PDF, DOCX, XLSX)
   - Test error scenarios (corrupted files, missing permissions)
   - Test performance with large file lists (100+ files)

5. **TASK 9.5**: Run Flutter linter and fix issues
   - Run `flutter analyze`
   - Fix all linting errors and warnings
   - Ensure code follows `flutter_lints` rules

**Acceptance Criteria**:
- Unit test coverage > 70% for services and providers
- All widget tests pass
- Manual testing completes without crashes
- Linter reports zero errors and warnings
- App performs smoothly on mid-range Android devices

---

#### TASK 10: Documentation and Deployment Preparation
**Parent**: ROOT
**Status**: NOT_STARTED
**Effort**: 2 hours
**Dependencies**: TASK 9

**Subtasks**:
1. **TASK 10.1**: Update README.md
   - Add project description and features
   - Add setup instructions (dependencies, build commands)
   - Add usage instructions
   - Add supported file formats and Android versions
   - Add known limitations (PPTX placeholder)

2. **TASK 10.2**: Add code documentation
   - Ensure all public APIs have doc comments
   - Add examples for complex methods
   - Document security considerations

3. **TASK 10.3**: Build release APK
   - Run `flutter build apk --release`
   - Test release build on physical device
   - Verify app size and performance

4. **TASK 10.4**: Create changelog
   - Document initial release features
   - List known issues and future enhancements

**Acceptance Criteria**:
- README is clear and comprehensive
- All public APIs are documented
- Release APK builds successfully
- Release APK runs without debug dependencies

---

### 3.3 Task Dependencies Graph

```
ROOT
│
├─ TASK 1 (Setup)
│  └─ TASK 2 (Models)
│     └─ TASK 3 (Services)
│        └─ TASK 4 (Providers)
│           └─ TASK 5 (Widgets)
│              └─ TASK 6 (Screens)
│                 └─ TASK 7 (Viewers)
│                    └─ TASK 8 (App Init)
│                       └─ TASK 9 (Testing)
│                          └─ TASK 10 (Documentation)
```

**Critical Path**: TASK 1 → 2 → 3 → 4 → 5 → 6 → 7 → 8 → 9 → 10
**Parallelizable**: None (sequential dependencies due to architecture layers)

### 3.4 Risk Assessment and Mitigation

#### Risk 1: DOCX/PPTX Viewer Library Limitations
- **Probability**: High
- **Impact**: Medium
- **Mitigation**:
  - Use web-based fallback for PPTX (defer to future version)
  - Accept limited formatting support for DOCX in MVP
  - Document limitations clearly in README
  - Plan for alternative rendering approaches in future versions

#### Risk 2: Android Permission Model Changes
- **Probability**: Low
- **Impact**: High
- **Mitigation**:
  - Use `permission_handler` package (abstracts platform differences)
  - Implement fallback with `file_picker` (no permissions needed)
  - Test on multiple Android versions
  - Monitor Android developer documentation for updates

#### Risk 3: Performance Issues with Large Files
- **Probability**: Medium
- **Impact**: Medium
- **Mitigation**:
  - Implement file size limit (100MB)
  - Use lazy loading for XLSX rendering
  - Display loading indicators during file processing
  - Profile app performance with Android Profiler

#### Risk 4: Security Vulnerabilities in File Parsing
- **Probability**: Low
- **Impact**: High
- **Mitigation**:
  - Use well-maintained, popular packages (PDFium, etc.)
  - Implement strict input validation
  - Keep dependencies updated
  - Run security scanning tools (if available)
  - Follow secure coding practices

#### Risk 5: Cross-File Dependency Conflicts
- **Probability**: Low
- **Impact**: Medium
- **Mitigation**:
  - Test `flutter pub get` early in TASK 1
  - Use version ranges cautiously (prefer ^x.y.z)
  - Check package compatibility before adding
  - Maintain `pubspec.lock` in version control

### 3.5 Testing Strategy

#### Unit Testing (Coverage Target: 70%+)
- **Services**: All business logic methods with edge cases
- **Providers**: State transitions and error handling
- **Models**: Factory constructors and validation
- **Utilities**: Formatters and helper functions

#### Widget Testing
- **Widgets**: User interactions and rendering
- **Screens**: State-dependent UI changes
- **Navigation**: Screen transitions

#### Manual Testing Checklist
- [ ] App launches on Android 10, 11, 12, 13
- [ ] Permission request appears on first launch
- [ ] Permission grant triggers file scan
- [ ] Permission deny shows manual selection option
- [ ] Manual file selection adds files to list
- [ ] File filtering works for each type
- [ ] PDF viewer opens and displays correctly
- [ ] DOCX viewer opens and displays correctly
- [ ] XLSX viewer opens and displays correctly
- [ ] PPTX viewer shows placeholder message
- [ ] Back navigation returns to home screen
- [ ] Pull-to-refresh updates file list
- [ ] App handles 100+ files without lag
- [ ] App handles corrupted files gracefully
- [ ] App survives low memory conditions
- [ ] Error messages are clear and actionable

### 3.6 Development Workflow

1. **Setup**: Create feature branch from `master`
2. **Implementation**: Follow task order sequentially
3. **Testing**: Write tests alongside implementation
4. **Code Review**: Self-review before commit (use Flutter linter)
5. **Commit**: Use conventional commits (e.g., "feat: add PDF viewer")
6. **Documentation**: Update comments and README as you go
7. **Integration**: Merge to `master` after all tasks complete

**No hot reloading issues expected**: Flutter supports hot reload for most changes (providers, widgets, UI). Only full restart needed for manifest/dependency changes.

---

## PHASE 4: FUTURE ENHANCEMENTS (OUT OF MVP SCOPE)

These features are documented for future planning but will NOT be implemented in MVP:

1. **PPTX Viewer**: Full slide rendering with animations
2. **File Management**: Rename, delete, share, move files
3. **Search**: Global search and in-document search
4. **Favorites**: Bookmark frequently accessed files
5. **Recent Files**: Track and display recently opened documents
6. **Dark Mode**: Custom dark theme (beyond system default)
7. **Multi-language**: Localization support
8. **Cloud Integration**: Google Drive, Dropbox, OneDrive
9. **Editing**: Annotation, highlighting, text editing
10. **Conversion**: PDF to Word, Word to PDF, etc.
11. **AI Features**: Summarization, translation, OCR
12. **Cross-platform**: iOS, Web, Desktop support

---

## CONCLUSION

This implementation plan provides a complete, specification-driven roadmap for building the Document Reader MVP. The plan adheres to anti-over-engineering principles by:

- Using simple, proven architecture (MVVM with Provider)
- Selecting minimal, well-maintained dependencies
- Avoiding unnecessary abstractions and design patterns
- Focusing exclusively on MVP requirements
- Implementing direct, straightforward solutions

**Key Strengths**:
- Complete traceability from requirements (EARS) to tasks
- Security-first approach with comprehensive input validation
- Research-backed technology choices (Flutter packages 2025)
- Clear task breakdown with dependencies and effort estimates
- Risk assessment and mitigation strategies
- Comprehensive testing strategy

**Next Steps**:
1. Review and approve this plan
2. Begin implementation with TASK 1 (Project Setup)
3. Execute tasks sequentially following the dependency graph
4. Review progress after each major task group
5. Deliver MVP after TASK 10 completion

**Estimated Timeline**: 5-7 days for experienced Flutter developer

This plan is ready for execution. All tasks remain in `NOT_STARTED` status awaiting your approval to begin implementation.
