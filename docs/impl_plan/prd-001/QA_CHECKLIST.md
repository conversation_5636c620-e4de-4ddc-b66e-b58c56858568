# PRD-001 Follow-up QA Checklist & Next Steps

## Manual QA Checklist
- [ ] Verify bottom navigation bar persists across Home ↔ Discover transitions
- [ ] Open Discover tab and confirm grid cards, empty state, and "Coming Soon" sheet behaviour
- [ ] From document list, test `More` menu on each file type:
  - [ ] `Add Password` on unsecured PDF creates `*-protected.pdf`
  - [ ] `Remove Password` on secured PDF creates `*-unprotected.pdf`
  - [ ] Error path for incorrect password shows SnackBar without altering files
  - [ ] `File Info` route loads metadata (name/path/size/mime/dates/author/encryption)
  - [ ] `Share` action launches Android share sheet and returns gracefully on cancel
- [ ] Repeat menu checks from PDF/DOCX/XLSX/PPTX viewers (app bar actions)
- [ ] Confirm File Info reflects encryption state before/after password operations
- [ ] Validate document list refresh surfaces new protected/unprotected copies without duplicates
- [ ] Smoke-test legacy flows (scan, pick, open documents) to ensure regressions not introduced

## Automated Follow-ups
- [x] Regenerate Freezed/Riverpod outputs and re-run `flutter test` (2025-10-04)
- [ ] Add widget tests for `DiscoverScreen`, `FileInfoScreen`, and `PasswordDialog`
- [ ] Add integration test covering password set/remove happy paths with fixtures
- [ ] Add integration smoke test for bottom-nav navigation and actions sheet wiring

## Documentation & Release Prep
- [ ] Update IMPLEMENTATION_PROGRESS_REPORT.md and SESSION_SUMMARY.md after QA sign-off
- [ ] Draft user-facing release notes/help content for Discover, File Info, Security, Share features
- [ ] Capture any dependency observations in `DEPENDENCY_CONFLICT_RESOLUTION.md`

## Upcoming Enhancements
- [ ] Design and scope "Change Password" flow (carry-over from A2.3 note)
- [ ] Evaluate telemetry/analytics for Discover engagement post-release
- [ ] Plan onboarding tips/tooltips for new menu actions if QA feedback suggests
