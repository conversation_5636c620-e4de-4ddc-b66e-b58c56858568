I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

## Current State Analysis

The app has a **partially implemented** document scanning feature:
- Basic `camera` package integration (v0.11.0+1) without automatic edge detection
- Static edge detection overlay (just a visual rectangle, no actual detection)
- Review screen with placeholder UI (shows icon instead of actual images)
- Domain entities ready with filter/rotation/crop support
- PDF conversion working but **not applying filters/crops before conversion**
- Missing: `flutter_doc_scanner` library, "Scanned" folder creation, share/open post-conversion

**Key Issue**: Line 4 of `Todos.md` confirms: *"Scan document to pdf is not working and lacking of edge detection"*

The requirements (REQ-2.1.2 through REQ-2.1.8) map cleanly to existing architecture but need proper implementation with automatic edge detection via `flutter_doc_scanner`.


### Approach

## Implementation Strategy

**Replace camera implementation** with `flutter_doc_scanner` for automatic edge detection while preserving the existing clean architecture:

1. **Add dependency** and update camera data source to use `flutter_doc_scanner`
2. **Simplify camera screen** to leverage plugin's built-in UI (edge detection + capture)
3. **Enhance review screen** to display actual images with crop/rotate/filter controls
4. **Fix PDF pipeline** to apply filters/crops before conversion
5. **Implement proper storage** with "Scanned" folder and timestamp naming
6. **Add share/open actions** after PDF creation

This approach **reuses** existing domain/repository layers, minimizes changes, and leverages the plugin's battle-tested edge detection instead of building custom CV logic.


### Reasoning

I explored the codebase structure and identified:
- Current camera implementation using basic `camera` package
- Existing domain entities (`ScannedPage`, `PageFilter`) and repository interfaces
- Placeholder UI components (edge overlay, review screen)
- PDF conversion logic that doesn't apply image processing
- Navigation routes already registered (`/scan-document`, `/scan-review`)
- Sharing feature already implemented in the app
- PRD-002 documentation with detailed EARS requirements
- Todos.md confirming the scanning feature is broken

I also researched `flutter_doc_scanner` API (v0.0.16) which provides methods like `getScanDocuments()` and `getScannedDocumentAsImages()` with built-in edge detection.


## Mermaid Diagram

sequenceDiagram
    actor User
    participant UI as ScanCameraScreen
    participant Notifier as ScanNotifier
    participant Repo as FileConversionRepository
    participant Scanner as flutter_doc_scanner
    participant Review as ScanReviewScreen
    participant Converter as PDFConverter
    participant Success as ScanSuccessScreen
    
    User->>UI: Tap "Scan Document"
    UI->>Notifier: captureImage()
    Notifier->>Repo: captureImages()
    Repo->>Scanner: getScanDocuments()
    Note over Scanner: Plugin shows camera UI<br/>with edge detection overlay
    User->>Scanner: Capture image(s)
    Scanner-->>Repo: List<String> imagePaths
    Repo-->>Notifier: Success(imagePaths)
    Notifier->>Notifier: Create ScannedPage objects
    UI->>Review: Navigate to review
    
    User->>Review: Apply filters/rotate/crop
    Review->>Notifier: applyFilter(index, filter)
    Review->>Notifier: rotatePage(index, angle)
    
    User->>Review: Tap "Finalize"
    Review->>Notifier: finalizeScan(fileName)
    Notifier->>Repo: applyFiltersToImage() for each page
    Repo-->>Notifier: Processed image paths
    Notifier->>Notifier: Create "Scanned" folder<br/>Generate timestamp filename
    Notifier->>Repo: convertImagesToPdf(paths, outputPath)
    Repo->>Converter: Convert to PDF
    Converter-->>Repo: PDF path
    Repo-->>Notifier: ConversionResult
    Notifier-->>Review: Success
    Review->>Success: Navigate with PDF info
    
    User->>Success: Tap "Open" or "Share"
    alt Open PDF
        Success->>Success: Navigate to PDF viewer
    else Share PDF
        Success->>Success: Trigger share sheet
    end

## Proposed File Changes

### pubspec.yaml(MODIFY) ✅ DONE

Add the `flutter_doc_scanner` dependency to enable automatic edge detection for document scanning.

**Changes:**
- In the `dependencies` section, after the existing camera/PDF packages (around line 64-68), add:
  ```yaml
  flutter_doc_scanner: ^0.0.16  # Document scanning with automatic edge detection
  ```
- This package provides built-in edge detection, document boundary recognition, and image capture capabilities that will replace the manual camera implementation.

**Note:** After adding this dependency, run `flutter pub get` to install the package.

### lib/features/file_conversion/data/datasources/camera_data_source.dart(MODIFY) ✅ DONE

References: 

- lib/features/file_conversion/data/datasources/camera_data_source_impl.dart(MODIFY)

Update the `CameraDataSource` interface to support multiple image capture (as `flutter_doc_scanner` can return multiple scanned images in one session) and remove methods that are no longer needed with the plugin.

**Changes:**
- Change `captureImage()` return type from `Future<Either<Failure, String>>` to `Future<Either<Failure, List<String>>>` to support multi-page scanning in a single session
- Remove `initializeCamera()`, `disposeCamera()`, and `getAvailableCameras()` methods as `flutter_doc_scanner` handles camera lifecycle internally
- The interface should now have a single primary method: `Future<Either<Failure, List<String>>> captureImages()` that launches the scanner and returns paths to captured images

**Rationale:** The plugin provides a complete scanning UI with edge detection, so we don't need manual camera control methods. Multi-image support aligns with REQ-2.1.5 (capturing multiple pages).

### lib/features/file_conversion/data/datasources/camera_data_source_impl.dart(MODIFY) ✅ DONE

References: 

- lib/features/file_conversion/data/datasources/camera_data_source.dart(MODIFY)

Replace the entire implementation to use `flutter_doc_scanner` instead of the manual `camera` package.

**Changes:**
- Remove all imports related to `camera` package
- Add import: `import 'package:flutter_doc_scanner/flutter_doc_scanner.dart';`
- Remove all camera controller logic (`_controller`, `_cameras`, initialization methods)
- Implement `captureImages()` method:
  - Create instance: `final scanner = FlutterDocScanner();`
  - Call `await scanner.getScanDocuments()` which returns `List<String>?` of image file paths
  - Handle null return (user cancelled) by returning `Left(CameraFailure('Scan cancelled by user'))`
  - Handle exceptions and wrap in `CameraFailure`
  - Return `Right(imagePaths)` on success
- Remove `initializeCamera()`, `disposeCamera()`, `getAvailableCameras()`, `startCameraPreview()`, `stopCameraPreview()` methods
- Keep the `CameraFailure` class definition at the bottom

**Implementation notes:**
- `getScanDocuments()` launches a full-screen scanner UI with automatic edge detection (satisfies REQ-2.1.2)
- The plugin handles camera permissions internally
- Returns multiple image paths if user scans multiple pages
- Images are already cropped to detected document boundaries by the plugin

### lib/features/file_conversion/data/repositories/file_conversion_repository_impl.dart(MODIFY) ✅ DONE

References: 

- lib/features/file_conversion/data/datasources/camera_data_source.dart(MODIFY)
- lib/features/file_conversion/domain/repositories/file_conversion_repository.dart(MODIFY)
- lib/features/file_conversion/data/datasources/pdf_converter_data_source.dart(MODIFY)

Update the `captureImages()` method to handle the new multi-image return type from `CameraDataSource`.

**Changes:**
- Modify `captureImages()` method (around line 29-35):
  - Change from calling `cameraDataSource.captureImage()` to `cameraDataSource.captureImages()`
  - Remove the wrapping of single path in a list: `right([imagePath])`
  - Directly return the result: `return await cameraDataSource.captureImages();`
  - The method already returns `Future<Either<Failure, List<String>>>` so the signature is correct

**Rationale:** The data source now returns a list directly, so we don't need to wrap it. This simplifies the repository implementation and properly supports multi-page scanning.
Update the `convertImagesToPdf` method to pass the full output path to the data source instead of just a filename.

**Changes:**
- In the `convertImagesToPdf` method (lines 54-70), change parameter name from `outputFileName` to `outputPath`
- Update the call to `pdfConverterDataSource.convertImagesToPdf` (line 60-64):
  - Change second argument from `outputFileName` to `outputPath`
- In `_createConversionResultFromFile` method (line 149-158):
  - Change parameter name from `fileName` to `filePath` for the first parameter
  - Extract the actual filename from the path: `final fileName = path.basename(filePath);`
  - Get file size: `final file = File(filePath); final fileSize = await file.length();`
  - Update the return to use actual file size instead of 0
- Make `_createConversionResultFromFile` async: `Future<ConversionResult> _createConversionResultFromFile(...)`
- Update all callers of this method to await it

**Rationale:** Propagates the output path change through the repository layer and fixes the file size calculation (currently hardcoded to 0).

### lib/features/file_conversion/presentation/screens/scan_camera_screen.dart(MODIFY) ✅ DONE

References: 

- lib/features/file_conversion/presentation/notifiers/scan_notifier.dart(MODIFY)
- lib/features/file_conversion/presentation/screens/scan_review_screen.dart(MODIFY)

Completely redesign this screen to simply trigger the `flutter_doc_scanner` plugin and navigate to review upon completion, removing all manual camera UI code.

**Changes:**
- Remove all camera-related imports (`camera` package)
- Remove `ConsumerStatefulWidget` and convert to `ConsumerWidget` (no state needed)
- Remove all state variables (`_controller`, `_cameras`)
- Remove `initState`, `dispose`, `_initializeCamera`, `_captureImage`, `_switchCamera` methods
- In the `build` method:
  - Show a simple loading screen with `CircularProgressIndicator` and text "Initializing scanner..."
  - Use `ref.listen` on `scanProvider` to watch for state changes
  - When state becomes `AsyncData` with non-empty list, navigate to `/scan-review` using `context.go('/scan-review')`
  - When state becomes `AsyncError`, show error dialog and pop back
- Add `initState` equivalent using `WidgetsBinding.instance.addPostFrameCallback` in build:
  - Call `ref.read(scanProvider.notifier).captureImage()` to trigger the scanner
- Remove all UI widgets (camera preview, overlay, controls) as the plugin provides its own UI

**Rationale:** The `flutter_doc_scanner` plugin provides a complete scanning UI with edge detection (REQ-2.1.2), so we don't need custom camera controls. This screen becomes a simple orchestrator that launches the scanner and handles navigation.

### lib/features/file_conversion/presentation/screens/scan_review_screen.dart(MODIFY) ✅ DONE

References: 

- lib/features/file_conversion/presentation/notifiers/scan_notifier.dart(MODIFY)
- lib/features/file_conversion/domain/entities/scanned_page.dart
- lib/features/file_conversion/presentation/widgets/page_filter_selector.dart

Enhance the review screen to display actual captured images (not placeholders) with functional crop, rotate, and filter controls, plus add "Add More" and "Finalize" actions.

**Changes:**

**1. Image Display (REQ-2.1.3):**
- In `_buildPagePreview` method (line 83-156), replace the placeholder `Container` with `Icon` (lines 98-108) with:
  - `Image.file(File(page.imagePath), fit: BoxFit.contain)` to display the actual scanned image
  - Wrap in `InteractiveViewer` to allow pinch-to-zoom for better inspection

**2. Crop Functionality (REQ-2.1.4):**
- Add a "Crop" button in the controls section (after rotation controls)
- On tap, navigate to a crop screen or show crop overlay:
  - Use `image_cropper` package (add to pubspec.yaml: `image_cropper: ^5.0.1`)
  - Call `ImageCropper().cropImage(sourcePath: page.imagePath, ...)`
  - On crop completion, call `notifier.updateImagePath(index, croppedPath)` (new method to add)
  - Alternative: Use `cropRect` in `ScannedPage` and apply during PDF conversion

**3. Filter Preview (REQ-2.1.4):**
- The `PageFilterSelector` widget is already present (line 114-119)
- Enhance to show live preview: when filter changes, apply it to the displayed image using `ColorFiltered` widget:
  - For `grayscale`: `ColorFilter.mode(Colors.grey, BlendMode.saturation)`
  - For `blackAndWhite`: Use `ColorFilter.matrix` with high contrast values
  - For `color`: No filter

**4. Add More Pages (REQ-2.1.5):**
- Update `_addMorePages` method (line 158-161):
  - Instead of `Navigator.pushReplacementNamed`, use `context.go('/scan-document')` to return to camera
  - The existing scanned pages are preserved in `scanProvider` state

**5. Finalize with Proper Naming (REQ-2.1.6, REQ-2.1.7):**
- Update `_finalizeScan` method (line 163-170):
  - Remove the save dialog (line 164)
  - Generate filename automatically: `scanned_${DateFormat('yyyyMMdd_HHmmss').format(DateTime.now())}.pdf`
  - Call `notifier.finalizeScan(fileName)` which will handle conversion and saving
  - Show loading dialog during conversion
  - On success, navigate to a success screen or show dialog with Open/Share options (REQ-2.1.8)

**6. Delete Page Option:**
- Add a delete button (trash icon) in the app bar or page controls
- Call `notifier.deletePage(index)` (new method to add to notifier)

**7. Page Reordering:**
- Consider adding `ReorderableListView` or drag handles for reordering pages
- Call `notifier.reorderPages(oldIndex, newIndex)` which already exists (line 63-78 in scan_notifier.dart)

### lib/features/file_conversion/presentation/notifiers/scan_notifier.dart(MODIFY) ✅ DONE

References: 

- lib/features/file_conversion/domain/repositories/file_conversion_repository.dart(MODIFY)
- lib/features/file_conversion/data/datasources/pdf_converter_data_source_impl.dart(MODIFY)

Enhance the `ScanNotifier` to properly process images with filters/crops before PDF conversion, and add helper methods for page management.

**Changes:**

**1. Update `finalizeScan` method (lines 80-99):**
- Before calling `convertImagesToPdf`, process each scanned page to apply filters, rotation, and crop
- For each page in `currentState`:
  - If filter is not `color` OR rotation is not 0 OR cropRect is not null:
    - Call `repository.applyFiltersToImage(page.imagePath, page.filter, page.rotationAngle, page.cropRect)`
    - Collect the processed image path
  - Otherwise, use the original `page.imagePath`
- Create a list of processed image paths
- Determine output directory: `${getApplicationDocumentsDirectory().path}/Scanned`
- Create the "Scanned" folder if it doesn't exist: `await Directory(scannedDir).create(recursive: true)`
- Generate filename with timestamp: `scanned_${DateFormat('yyyyMMdd_HHmmss').format(DateTime.now())}.pdf`
- Construct full output path: `$scannedDir/$fileName`
- Call `repository.convertImagesToPdf(processedImagePaths, outputPath, PageOrientation.auto)`
- On success, store the output path in state or emit a success event for the UI to handle Open/Share

**2. Add new methods:**
- `Future<void> deletePage(int index)`: Remove a page from the list
  - Get current state, create a copy, remove at index, update state
- `Future<void> updateImagePath(int index, String newPath)`: Update image path after cropping
  - Get current state, update the page's imagePath, update state
- `Future<void> clearPages()`: Reset to empty list (for starting a new scan)

**3. Add imports:**
- `import 'package:path_provider/path_provider.dart';`
- `import 'package:intl/intl.dart';`
- `import 'dart:io';`

**Rationale:** This ensures filters/crops are actually applied to images before PDF creation (currently missing), implements proper "Scanned" folder storage (REQ-2.1.7), and provides helper methods for page management.

### lib/features/file_conversion/data/datasources/pdf_converter_data_source_impl.dart(MODIFY) ✅ DONE

References: 

- lib/features/file_conversion/data/datasources/pdf_converter_data_source.dart(MODIFY)

Fix the `convertImagesToPdf` method to accept a full output path (including directory) instead of just a filename, enabling proper "Scanned" folder storage.

**Changes:**

**1. Update method signature and implementation (lines 14-66):**
- Change parameter name from `outputFileName` to `outputPath` for clarity
- Remove lines 56-58 that construct the output path using `getApplicationDocumentsDirectory()`
- Instead, use the provided `outputPath` directly
- Ensure the parent directory exists: `await Directory(path.dirname(outputPath)).create(recursive: true)`
- Write the PDF to `outputPath`: `await File(outputPath).writeAsBytes(outputBytes)`
- Return `Right(outputPath)`

**2. Add import:**
- Ensure `import 'package:path/path.dart' as path;` is present (already at line 8)

**Rationale:** This allows the caller (repository/notifier) to specify the exact output location including the "Scanned" folder, satisfying REQ-2.1.7. The current implementation always saves to the app documents root, which doesn't meet the requirement.

### lib/features/file_conversion/data/datasources/pdf_converter_data_source.dart(MODIFY) ✅ DONE

References: 

- lib/features/file_conversion/data/datasources/pdf_converter_data_source_impl.dart(MODIFY)

Update the interface to reflect that `convertImagesToPdf` now accepts a full output path instead of just a filename.

**Changes:**
- In the `convertImagesToPdf` method signature (lines 11-15), change the parameter name from `outputFileName` to `outputPath`
- Update the doc comment (if any) to clarify that this should be a full file path including directory

**Rationale:** Keeps the interface consistent with the implementation change in `pdf_converter_data_source_impl.dart`.

### lib/features/file_conversion/domain/repositories/file_conversion_repository.dart(MODIFY) ✅ DONE

References: 

- lib/features/file_conversion/data/repositories/file_conversion_repository_impl.dart(MODIFY)

Update the `convertImagesToPdf` method signature to accept a full output path instead of just a filename, aligning with the data source changes.

**Changes:**
- In the `convertImagesToPdf` method signature (lines 20-24), change the parameter name from `outputFileName` to `outputPath`
- Update any doc comments to clarify this is a full file path

**Rationale:** Maintains consistency across the repository interface and implementation layers.

### lib/features/file_conversion/presentation/screens/scan_success_screen.dart(NEW) ✅ DONE

References: 

- lib/core/utils/file_size_formatter.dart
- lib/features/sharing/domain/usecases/share_file.dart
- lib/core/navigation/app_router.dart(MODIFY)

Create a new success screen that displays after PDF creation is complete, offering options to Open or Share the PDF (REQ-2.1.8).

**Structure:**
- Create a `StatelessWidget` named `ScanSuccessScreen`
- Accept parameters in constructor:
  - `required String pdfPath` - the path to the created PDF
  - `required String fileName` - the PDF filename for display

**UI Layout:**
- `Scaffold` with app bar titled "Scan Complete"
- Body with centered column:
  - Success icon: `Icon(Icons.check_circle, size: 80, color: Colors.green)`
  - Text: "PDF created successfully"
  - Text: Display the filename
  - Text: Display file size (get from `File(pdfPath).lengthSync()` formatted with `FileSizeFormatter`)
  - Spacing
  - Two prominent buttons:
    - **"Open PDF"** button: Navigate to PDF viewer using `context.go('/viewer/pdf/${Uri.encodeComponent(pdfPath)}?name=${Uri.encodeComponent(fileName)}')`
    - **"Share PDF"** button: Use the sharing feature to share the file
      - Import: `import 'package:myapp/features/sharing/domain/usecases/share_file.dart';`
      - Get the sharing repository via Riverpod provider
      - Call `shareFile(path: pdfPath, mime: 'application/pdf')`
      - Handle errors with a snackbar
  - "Done" text button at bottom: Pops back to home screen using `context.go('/')`

**Imports needed:**
- `package:flutter/material.dart`
- `package:go_router/go_router.dart`
- `package:flutter_riverpod/flutter_riverpod.dart`
- `dart:io`
- Reference `FileSizeFormatter` from `lib/core/utils/file_size_formatter.dart`

**Rationale:** Provides a clear success state with actionable options (Open/Share) as required by REQ-2.1.8, improving user experience after scan completion.

### lib/core/navigation/app_router.dart(MODIFY) ✅ DONE

References: 

- lib/features/file_conversion/presentation/screens/scan_success_screen.dart(NEW)
- lib/features/file_conversion/presentation/screens/scan_review_screen.dart(MODIFY)

Add a route for the new scan success screen to enable navigation after PDF creation.

**Changes:**
- Add import at the top (around line 22): `import '../../features/file_conversion/presentation/screens/scan_success_screen.dart';`
- Add a new route after the `/scan-review` route (after line 148):
  ```dart
  GoRoute(
    path: '/scan-success',
    builder: (context, state) {
      final pdfPath = state.uri.queryParameters['pdfPath'];
      final fileName = state.uri.queryParameters['fileName'];
      if (pdfPath == null || fileName == null) {
        return Scaffold(
          appBar: AppBar(title: const Text('Error')),
          body: const Center(child: Text('Missing PDF information')),
        );
      }
      return ScanSuccessScreen(
        pdfPath: Uri.decodeComponent(pdfPath),
        fileName: Uri.decodeComponent(fileName),
      );
    },
  ),
  ```

**Rationale:** Enables navigation to the success screen with PDF path and filename as query parameters, completing the scan-to-PDF flow.

### lib/features/file_conversion/presentation/widgets/edge_detection_overlay.dart(DELETE) ✅ DONE

References: 

- lib/features/file_conversion/presentation/screens/scan_camera_screen.dart(MODIFY)

Delete this file as it's no longer needed. The `flutter_doc_scanner` plugin provides its own edge detection overlay with actual computer vision-based detection, making this static visual overlay obsolete.

**Rationale:** Removes dead code. The plugin's built-in edge detection (REQ-2.1.2) is superior to this static rectangle overlay. The `ScanCameraScreen` no longer uses this widget after being refactored to use the plugin.

### lib/features/file_conversion/presentation/widgets/camera_controls_widget.dart(DELETE) ✅ DONE

References: 

- lib/features/file_conversion/presentation/screens/scan_camera_screen.dart(MODIFY)

Delete this file as it's no longer needed. The `flutter_doc_scanner` plugin provides its own camera controls (capture button, flash, etc.) as part of its scanning UI.

**Rationale:** Removes dead code. Since `ScanCameraScreen` now launches the plugin's UI instead of building custom camera controls, this widget is unused.

### android/app/src/main/AndroidManifest.xml(MODIFY) ✅ DONE

Add required permissions for `flutter_doc_scanner` to access the camera and write scanned images to storage.

**Changes:**
- Add the following permissions inside the `<manifest>` tag (before the `<application>` tag):
  ```xml
  <uses-permission android:name="android.permission.CAMERA" />
  <uses-feature android:name="android.hardware.camera" android:required="false" />
  <uses-feature android:name="android.hardware.camera.autofocus" android:required="false" />
  ```
- Note: Storage permissions should already be present from the file discovery feature, but verify `READ_EXTERNAL_STORAGE` and `WRITE_EXTERNAL_STORAGE` are declared

**Rationale:** The `flutter_doc_scanner` plugin requires camera access to capture document images. These permissions are necessary for the plugin to function on Android.

### lib/features/file_conversion/presentation/widgets/scan_result_dialog.dart(NEW) ✅ DONE

References: 

- lib/features/file_conversion/presentation/screens/scan_review_screen.dart(MODIFY)

Create a reusable dialog widget to show scan/conversion progress and results, providing better user feedback during the PDF creation process.

**Structure:**
- Create a `StatelessWidget` named `ScanResultDialog`
- Accept parameters:
  - `required bool isLoading` - whether conversion is in progress
  - `required String? errorMessage` - error message if conversion failed
  - `VoidCallback? onRetry` - callback for retry button
  - `VoidCallback? onDismiss` - callback for dismiss/close button

**UI Layout:**
- Return an `AlertDialog` with:
  - **If `isLoading` is true:**
    - Content: Column with `CircularProgressIndicator` and text "Creating PDF..."
    - No actions (non-dismissible during loading)
  - **If `errorMessage` is not null:**
    - Title: "Conversion Failed"
    - Content: Text displaying the error message
    - Actions: "Retry" button (calls `onRetry`) and "Cancel" button (calls `onDismiss`)
  - **Otherwise (success state - though this dialog might not be shown for success):**
    - Title: "Success"
    - Content: "PDF created successfully"
    - Actions: "OK" button (calls `onDismiss`)

**Usage:**
- This dialog will be shown from `ScanReviewScreen` when the user taps "Finalize"
- Show with `showDialog(context: context, barrierDismissible: false, builder: (context) => ScanResultDialog(...))`
- Update the dialog state by calling `Navigator.pop()` and showing a new one, or use a `StatefulBuilder` inside

**Rationale:** Provides clear feedback during PDF conversion (REQ-2.NF.1 requires progress indicator for operations > 2 seconds) and handles error states gracefully.

### lib/features/file_conversion/domain/entities/conversion_result.dart(MODIFY) ✅ DONE

References: 

- lib/core/utils/file_size_formatter.dart
- lib/features/file_conversion/presentation/screens/scan_success_screen.dart(NEW)

Enhance the `ConversionResult` entity to include additional metadata that will be useful for the success screen and file management.

**Changes:**
- If not already present, ensure the entity has these fields:
  - `required String outputPath` - full path to the created PDF
  - `required String fileName` - just the filename
  - `required int fileSize` - size in bytes
  - `required DateTime createdAt` - timestamp of creation
- If using Freezed (which it should be based on architecture), ensure the `@freezed` annotation is present and the part file is generated
- Add a convenience getter method:
  - `String get formattedSize => FileSizeFormatter.format(fileSize);` (requires importing the formatter)

**Rationale:** Provides all necessary information for displaying results in the success screen and for file management operations. The formatted size getter improves UI code readability.

### Todos.md(MODIFY) ✅ DONE

Update the todos list to reflect the completion of the scan document feature implementation.

**Changes:**
- Update line 4 from:
  `Scan document to pdf is not working and lacking of edge detection`
  to:
  `~~Scan document to pdf is not working and lacking of edge detection~~ (COMPLETED: Implemented with flutter_doc_scanner)`
- Or simply remove line 4 if completed items are not tracked in this file

**Rationale:** Keeps the project documentation up-to-date with implementation progress.