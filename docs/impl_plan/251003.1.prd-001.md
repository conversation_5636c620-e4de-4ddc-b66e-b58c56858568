# PRD-001: Core App Enhancements — Specification and TDD Plan

This document converts PRD-001 into a specification-driven, test-driven plan strictly adhering to the project’s Feature-First Clean Architecture with Riverpod DI. No code here—only specs and tasks with clear traceability.

## Architecture Constraint

- Feature-First Clean Architecture (Presentation, Domain, Data), per docs/agents/dev.md and CLAUDE.md.
- Riverpod for state management and dependency injection (`@riverpod` providers).
- Domain/Data use `Either<Failure, T>` from `dartz`; Presentation uses `AsyncValue`.
- Navigation with GoRouter; theming MD3.

---

## Phase 1: Requirements Analysis (EARS)

Legend: R1.x Discover, R2.x PDF Security, R3.x File Info, R4.x Enhanced Sharing.

### R1 — Discover Tab Implementation
- R1.0 WHEN the app shell is rendered THE SYSTEM SHALL show a bottom navigation bar with `Home` (default) and `Discover` tabs.
- R1.1 WHEN the user taps the `Discover` tab in main navigation THE SYSTEM SHALL navigate to the Discover screen.
- R1.2 WHEN the Discover screen is displayed THE SYSTEM SHALL show a grid/list of available tools (e.g., Merge, Split, Convert, AI) with labels and icons.
- R1.3 WHEN no tools are available to display THE SYSTEM SHALL show an empty state with helpful text.
- R1.4 WHEN the user taps a tool card THE SYSTEM SHALL navigate to the corresponding feature route (or a “Coming soon” sheet when not yet implemented).

Acceptance (R1):
- A1.0 Bottom nav renders with Home selected by default and Discover available.
- A1.1 Tapping `Discover` switches to Discover route.
- A1.2 At least 4 cards appear (Merge, Split, Convert, AI) with icons.
- A1.3 Empty state renders when configured list is empty.
- A1.4 Tapping a card triggers a route push or a bottom sheet “Coming soon”.

### R2 — File Security (Password-Protect PDFs)
- R2.1 WHEN the user selects “Add Password” for a PDF without a password THE SYSTEM SHALL prompt for a new password and confirmation.
- R2.2 WHEN the user submits matching non-empty passwords THE SYSTEM SHALL encrypt the PDF with the provided password and persist the protected file (in-place or to a new file per UX decision).
- R2.3 WHEN the PDF is already password-protected THE SYSTEM SHALL indicate it is protected and provide an option to change/remove password.
- R2.4 WHEN the user enters an incorrect current password for a protected PDF THE SYSTEM SHALL reject the operation with a clear error.
- R2.5 WHEN the user cancels the password dialog THE SYSTEM SHALL perform no changes.

Acceptance (R2):
- A2.1 New password dialog appears for unprotected PDFs.
- A2.2 Submitting valid password sets protection; follow-up open requires password.
- A2.3 Protected PDFs show protected state; remove flow available (change password tracked for next version — see Implementation Notes).
- A2.4 Incorrect current password surfaces a user-visible error (no file changes).

### R3 — File Information Screen
- R3.1 WHEN the user opens File Info for a document THE SYSTEM SHALL display name, path, size, createdAt, modifiedAt, mime/type.
- R3.2 WHEN the file has author metadata THE SYSTEM SHALL display author if available.
- R3.3 WHEN the file is password-protected THE SYSTEM SHALL display an indicator (without exposing the password).
- R3.4 WHEN the file path is invalid or file missing THE SYSTEM SHALL show an error state.

Acceptance (R3):
- A3.1 All base fields render for valid file path.
- A3.2 Author shown only when available.
- A3.3 Protected indicator visible for encrypted PDFs.
- A3.4 Bad paths render an error UI state gracefully.

### R4 — Enhanced Sharing
- R4.1 WHEN the user selects Share on a file THE SYSTEM SHALL invoke the platform share sheet with the file attachment and an appropriate MIME type.
- R4.2 WHEN the file is large (> configurable threshold) THE SYSTEM SHALL still share by file reference without loading entire file into memory.
- R4.3 WHEN the share is canceled THE SYSTEM SHALL return to the app without error.

Acceptance (R4):
- A4.1 Share sheet opens with PDF attached.
- A4.2 Large file sharing does not crash and remains responsive.
- A4.3 Cancel returns to app without error states.

Non-Functional Requirements (NFR)
- N1: Security — For R2, use strong encryption (e.g., AES-256) supported by selected PDF library; never log passwords.
- N2: Performance — Metadata retrieval and share invocation must complete within 200ms on mid-tier devices; encryption operations may exceed but must show progress.
- N3: UX — All destructive or irreversible actions require confirmation; dialogs are accessible and dismissible.
- N4: Offline — All features work offline.

Simplicity Gate (Phase 1)
- Keep Discover as a static, local configuration for MVP.
- Avoid premature generalization of security flows; support “set” and “remove” only.
- Use a single metadata entity for File Info; don’t over-model.

---

## Phase 2: Design Specification (Feature-First Clean Architecture)

New feature modules under `lib/features/`:

1) discover/
- presentation: `screens/discover_screen.dart`, `widgets/tool_card.dart`, `notifiers/discover_tools_notifier.dart`
- domain: `entities/tool_entry.dart`, `usecases/get_discover_tools.dart`
- data: `repositories/discover_tools_repository_impl.dart` (if needed), or direct static provider in presentation for MVP

2) pdf_security/
- presentation: `widgets/password_dialog.dart`, `notifiers/pdf_security_notifier.dart`
- domain: `entities/pdf_security_status.dart`, `repositories/pdf_security_repository.dart`, `usecases/set_pdf_password.dart`, `usecases/remove_pdf_password.dart`, `usecases/is_pdf_encrypted.dart`, `usecases/validate_pdf_password.dart`
- data: `datasources/pdf_security_local_datasource.dart`, `repositories/pdf_security_repository_impl.dart`

3) file_info/
- presentation: `screens/file_info_screen.dart`, `notifiers/file_info_notifier.dart`
- domain: `entities/file_metadata.dart`, `repositories/file_metadata_repository.dart`, `usecases/get_file_metadata.dart`
- data: `datasources/file_system_metadata_datasource.dart`, `repositories/file_metadata_repository_impl.dart`

4) sharing/
- presentation: entry points (menu/buttons) live within consumer screens; a minimal notifier may be included if needed
- domain: `repositories/sharing_repository.dart`, `usecases/share_file.dart`
- data: `datasources/share_platform_datasource.dart`, `repositories/sharing_repository_impl.dart`

Core additions (if not present):
- `core/error/failures.dart` (already present).
- `core/navigation/app_router.dart`: add `Discover` and `FileInfo` routes.
- `core/providers/` DI providers for repositories and use cases or colocate providers using `@riverpod` near implementations per dev.md guidance.

Domain Layer (Interfaces and Use Cases)
- PdfSecurityRepository
  - `Future<Either<Failure, Unit>> setPassword({required String path, required String newPassword});`
  - `Future<Either<Failure, Unit>> removePassword({required String path, required String currentPassword});`
  - `Future<Either<Failure, bool>> isEncrypted(String path);`
  - `Future<Either<Failure, Unit>> validatePassword({required String path, required String password});`

- FileMetadataRepository
  - `Future<Either<Failure, FileMetadata>> getMetadata(String path);`

- SharingRepository
  - `Future<Either<Failure, Unit>> shareFile({required String path, required String mime});`

- Use Cases (one class per action, simple call operator)
  - `SetPdfPassword`, `RemovePdfPassword`, `IsPdfEncrypted`, `ValidatePdfPassword`
  - `GetFileMetadata`
  - `ShareFile`
  - `GetDiscoverTools` (optional; MVP can use static config)

Entities
- `FileMetadata { String path; String name; int sizeBytes; DateTime createdAt; DateTime modifiedAt; String mime; String? author; bool isEncrypted; }`
- `ToolEntry { String id; String title; String iconName; String route; bool enabled; }`
- `PdfSecurityStatus { bool isEncrypted; }` (optional convenience)

Data Layer (Implementations)
- `pdf_security_local_datasource.dart`: wraps a PDF library capable of adding/removing encryption. Library selection to be finalized (e.g., Syncfusion PDF, or an alternative with permissive licensing). Must avoid loading large files fully into memory where possible.
- `file_system_metadata_datasource.dart`: uses `dart:io` stat and a lightweight parser for basic metadata; optionally consult PDF library for author.
- `share_platform_datasource.dart`: integrates with `share_plus` or equivalent.

Presentation Layer
- BottomNavScaffold: wraps Home and Discover routes, wiring stateful tab switching and back-stack behavior.
- DiscoverScreen: grid of `ToolEntry` using Riverpod notifier/provider for entries.
- PasswordDialog: collects password input; interacts with `PdfSecurityNotifier` which calls `SetPdfPassword`/`RemovePdfPassword`.
- FileInfoScreen: watches `FileInfoNotifier` returning `AsyncValue<FileMetadata>`.
- Navigation: add routes `/discover`, `/file-info?path=...`.

Providers and DI (Riverpod)
- For each repository impl, expose a `@riverpod` provider; use case providers depend on repository providers; notifiers depend on use case providers. Follow dev.md examples.

Sequence (Textual)
- Set Password: Document list or viewer menu action → PasswordDialog → Notifier.setPassword(path, pw) → UseCase(SetPdfPassword) → Repository → LocalDataSource → returns `Either` → Document list refreshes and UI updates.
- File Info: Screen build → Notifier.build(path) → UseCase(GetFileMetadata) → Repository → DataSource → `Either` → Notifier maps to `AsyncValue` → UI renders.
- Share: Button tap (list tile overflow or viewer menu) → UseCase(ShareFile) → Repository → PlatformDatasource → `Either` → UI handles success/error.

Testing Strategy
- Unit tests: all use cases (pure logic), repositories (with mocked datasources), datasources (with fakes where feasible for file system paths).
- Widget tests: DiscoverScreen (renders items; tap navigates), FileInfoScreen states (loading/data/error), PasswordDialog validation.
- Integration tests: navigation to Discover; share invocation stubbed; set password flow happy-path using a small fixture file in `test/fixtures`.

Simplicity Gate (Phase 2)
- Discover entries provided from a static list via provider; defer repository for later if dynamic.
- Only implement “Set” and “Remove” password; defer “Change password” to v2 (can be composed by remove+set later).
- Metadata extraction reads only required fields; avoid heavy parsers unless needed for author.

---

## Phase 3: Implementation Planning (TDD Task Breakdown)

Root Task: PRD-001 Core App Enhancements (Do not start until approved)

Scaffold (Architecture)
- T0.1 Create feature directories: `discover/`, `pdf_security/`, `file_info/`, `sharing/` with Presentation/Domain/Data subfolders.
- T0.2 Add routes in `core/navigation/app_router.dart` for `/discover` and `/file-info`.
- T0.3 Add Riverpod providers for repositories and use cases (stubs), wiring only.
- T0.4 Introduce a shared app scaffold with bottom navigation that hosts Home (`/`) and Discover (`/discover`) tabs, ensuring deep links preserve tab selection.

R1 — Discover
- T1.0 Implement Requirement: R1.1–R1.4
  - RED: widget test failing for `DiscoverScreen` renders 4 default tools.
  - GREEN: add `ToolEntry` model/provider and `DiscoverScreen` to pass.
  - REFACTOR: clean widget structure and provider names.
- T1.1
  - RED: tapping a tool card triggers navigation (or “Coming soon”).
  - GREEN: add `onTap` handlers with route or bottom sheet.
  - REFACTOR: extract `ToolCard` widget.
- T1.2
  - RED: integration test drives bottom-nav switching between Home and Discover preserving document state.
  - GREEN: wire bottom-nav tab selection and ensure router updates; Home content remains accessible as first tab.
  - REFACTOR: dedupe shell scaffolds/shared app bars.

R2 — PDF Security
- T2.0 Implement Requirement: R2.1–R2.5 (set password)
  - RED: use case tests for `SetPdfPassword` with success and failures (bad path, IO error).
  - GREEN: implement use case with mocked `PdfSecurityRepository`.
  - REFACTOR: simplify params type.
- T2.1
  - RED: repository tests with mocked datasource for set/remove/isEncrypted/validate.
  - GREEN: implement `PdfSecurityRepositoryImpl` delegating to datasource.
  - REFACTOR: unify error mapping to `Failure`.
- T2.2
  - RED: widget test for `PasswordDialog` validation (non-empty, match confirmation).
  - GREEN: implement dialog and `PdfSecurityNotifier` to call use case; map to `AsyncValue`.
  - REFACTOR: extract form validation helpers.
- T2.3
  - RED: widget/integration tests ensure document list tile menu and viewer overflow menu expose “Add Password”/“Remove Password”.
  - GREEN: hook menus to open `PasswordDialog`, refresh list after success so new `*-protected`/`*-unprotected` files appear without duplicates.
  - REFACTOR: centralize list refresh helper to avoid double scans.

R3 — File Info
- T3.0 Implement Requirement: R3.1–R3.4
  - RED: use case tests for `GetFileMetadata` success and errors.
  - GREEN: implement use case using mocked repository.
  - REFACTOR: tidy entity fields.
- T3.1
  - RED: repository tests with fake `dart:io` stat via datasource.
  - GREEN: implement repository and datasource path handling.
  - REFACTOR: consolidate MIME detection helper in core if needed.
- T3.2
  - RED: widget test for `FileInfoScreen` loading/data/error states.
  - GREEN: implement notifier and screen to satisfy tests.
  - REFACTOR: visual polish with theme usage.
- T3.3
  - RED: UI tests confirm document list overflow and viewer menus route to File Info via GoRouter.
  - GREEN: wire menu actions to `/file-info?path=...` and ensure return keeps previous context.
  - REFACTOR: share menu-building utility between list and viewer screens.

R4 — Enhanced Sharing
- T4.0 Implement Requirement: R4.1–R4.3
  - RED: use case tests for `ShareFile` ensuring it calls repository with correct MIME.
  - GREEN: implement use case mapping path→MIME (simple mapping: “.pdf” → “application/pdf”).
  - REFACTOR: move MIME util to core if shared.
- T4.1
  - RED: repository tests mocking platform datasource (share called once; cancel path returns success).
  - GREEN: implement repository and platform datasource.
  - REFACTOR: memory-friendly path sharing.
- T4.2
  - RED: UI tests drive share actions from document list and viewer menus (including large-file fixture).
  - GREEN: connect menu actions to `SharingNotifier`, surface progress/snackbars, and ensure navigation stack remains intact after share/cancel.
  - REFACTOR: reuse menu action components across entry points.

Validation
- TV.1 Run unit/widget/integration tests; `flutter analyze`; `dart format .`.
- TV.2 Manual QA: set/remove password on a sample PDF; File Info fields; Share flow; Discover navigation.
- TV.3 Manual QA: confirm document list updates when `*-protected`/`*-unprotected` files are created and no duplicate entries persist.

Open Decisions (pre-implementation)
- D1: PDF encryption library selection (e.g., Syncfusion PDF vs. alternative). Criteria: AES-256 support, offline, license compatibility.
- D2: In-place vs. new-file behavior for password-protection. Default proposal: create a new file with suffix `-protected.pdf` to avoid destructive changes.

Traceability
- Each task references R#.x; tests named accordingly (e.g., `set_pdf_password_test.dart` addresses A2.*).

Simplicity Gate (Phase 3)
- Keep Discover static; avoid remote configs.
- Only minimal repository interfaces to meet requirements.
- Avoid generic metadata parsing; target PDF first.

---

## Next Steps
- Review and approve this specification and plan.
- Decide on D1–D2.
- After approval, start with scaffold tasks (T0.*), then proceed requirement-by-requirement using Red-Green-Refactor.

## Implementation Notes (MVP Focus)
- Change password flow (R2 change scenario) is deferred to the next version; keep acceptance A2.3 flagged for follow-up after current MVP scope.
- Document list and viewer menus must expose Add/Remove Password, File Info, and Share actions, each routing through the defined notifiers/providers.
- After password set/remove operations create new `*-protected` or `*-unprotected` files, trigger a document list refresh so users can discover new copies without confusion; consider messaging to explain the additional files.
