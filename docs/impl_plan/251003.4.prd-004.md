I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

I've completed comprehensive research of the codebase and external resources:

**Codebase Analysis:**
- Existing feature-first Clean Architecture with Presentation/Domain/Data layers
- Riverpod for state management/DI, dartz Either for error handling, freezed for models
- Current document_viewer feature provides viewing-only capabilities using Syncfusion PDF Viewer
- No file writing/persistence exists yet (only JSON for recent documents)
- Navigation uses GoRouter with path parameters and query strings
- HomeScreen shows document list; no "Discover" tab yet (planned in PRD-001)

**Technology Research:**
- Syncfusion Flutter PDF library (syncfusion_flutter_pdf) can handle all PRD-004 requirements programmatically
- Current viewer (syncfusion_flutter_pdfviewer v31.1.22) supports text markups and sticky notes in UI
- For signatures, watermarks, page numbers, and form filling: need syncfusion_flutter_pdf library
- Security research confirms simple e-signatures are sufficient for MVP (not PKI-based digital signatures)
- Syncfusion provides PdfViewerController for programmatic annotation control

**Key Architectural Decisions:**
- Create new `pdf_editor` feature module following established Clean Architecture pattern
- Reuse existing document_viewer for display, extend with editing capabilities
- Save edited PDFs to Downloads folder with "_edited" suffix to preserve originals
- Integrate with existing file_discovery to add edited documents to recent list


### Approach

Following the **3-Phase Specification-Driven TDD Workflow** from `docs/agents/en-plan-tdd.md`:

## Phase 1: Requirements Analysis (Architecturally-Aware & Testable)
Convert PRD-004 into EARS-formatted requirements with architectural constraints identified. Validate testability of each requirement.

## Phase 2: Specification Creation (Designing for Architecture)
Design a new `pdf_editor` feature module following Feature-First Clean Architecture with Presentation/Domain/Data layers. Plan for TDD with interfaces, dependency injection via Riverpod, and simple logic within architectural components.

## Phase 3: Implementation Planning (TDD within Architecture)
Create hierarchical task list following Red-Green-Refactor cycle for each EARS requirement. Include architectural scaffolding tasks, test-first development tasks, and integration tasks.

**Technology Stack:**
- Add `syncfusion_flutter_pdf` for programmatic PDF manipulation
- Extend existing `syncfusion_flutter_pdfviewer` with editing modes
- Use `path_provider` for save locations
- Leverage existing Riverpod/dartz/freezed patterns


### Reasoning

I started by reading the instruction documents (en-plan-tdd.md, 251003.pdf-manipulating.md, dev.md, CLAUDE.md) to understand the requirements and architectural constraints. I then listed the repository structure to understand the project layout. I examined existing features (document_viewer, file_discovery, permissions) to understand the established patterns. I read the pubspec.yaml to identify current dependencies. I performed web searches to research Flutter PDF editing libraries, focusing on Syncfusion capabilities and security best practices for digital signatures. I explored the app router, home screen, core providers, and file system data sources to understand navigation patterns, file operations, and integration points. This comprehensive research revealed that the app currently only reads files and needs file persistence capabilities, and that Syncfusion's PDF library can handle all PRD-004 requirements within the existing architecture.


## Mermaid Diagram

sequenceDiagram
    participant User
    participant PdfEditorScreen
    participant PdfEditorNotifier
    participant SaveEditedPdfUseCase
    participant PdfEditorRepository
    participant PdfEditorDataSource
    participant SyncfusionPDF
    participant FileSystem
    participant AddToRecentUseCase

    User->>PdfEditorScreen: Tap Edit button
    PdfEditorScreen->>PdfEditorNotifier: enterEditMode()
    PdfEditorNotifier-->>PdfEditorScreen: isEditMode = true
    PdfEditorScreen->>PdfEditorScreen: Show EditorToolbar

    User->>PdfEditorScreen: Select highlight tool
    PdfEditorScreen->>PdfEditorNotifier: setActiveTool(highlight)
    User->>PdfEditorScreen: Select text in PDF
    PdfEditorScreen->>PdfEditorNotifier: addAnnotation(annotation)
    PdfEditorNotifier->>PdfEditorNotifier: hasUnsavedChanges = true

    User->>PdfEditorScreen: Tap Save button
    PdfEditorScreen->>PdfEditorNotifier: saveDocument()
    PdfEditorNotifier->>SaveEditedPdfUseCase: call(path, edits)
    SaveEditedPdfUseCase->>PdfEditorRepository: saveEditedPdf(path, edits)
    PdfEditorRepository->>PdfEditorDataSource: saveEditedPdf(path, edits)
    
    PdfEditorDataSource->>SyncfusionPDF: Load PDF from file
    SyncfusionPDF-->>PdfEditorDataSource: PdfDocument
    PdfEditorDataSource->>SyncfusionPDF: Apply annotations
    PdfEditorDataSource->>SyncfusionPDF: Apply signatures
    PdfEditorDataSource->>SyncfusionPDF: Apply watermarks
    PdfEditorDataSource->>SyncfusionPDF: Apply page numbers
    PdfEditorDataSource->>SyncfusionPDF: Fill form fields
    PdfEditorDataSource->>SyncfusionPDF: Save document bytes
    SyncfusionPDF-->>PdfEditorDataSource: PDF bytes
    
    PdfEditorDataSource->>FileSystem: Write to Downloads/filename_edited.pdf
    FileSystem-->>PdfEditorDataSource: Success
    PdfEditorDataSource-->>PdfEditorRepository: EditedDocument(newPath)
    PdfEditorRepository-->>SaveEditedPdfUseCase: Right(EditedDocument)
    SaveEditedPdfUseCase-->>PdfEditorNotifier: Right(EditedDocument)
    
    PdfEditorNotifier->>AddToRecentUseCase: call(editedDocument)
    AddToRecentUseCase-->>PdfEditorNotifier: Success
    PdfEditorNotifier->>PdfEditorNotifier: hasUnsavedChanges = false
    PdfEditorNotifier-->>PdfEditorScreen: AsyncData(EditedDocument)
    
    PdfEditorScreen->>User: Show success SnackBar with Open/Share actions

## Proposed File Changes

### docs/plans/prd-004-tdd-plan.md(NEW)

References: 

- docs/agents/en-plan-tdd.md
- docs/mvp/251003.pdf-manipulating.md
- docs/agents/dev.md
- CLAUDE.md
- lib/features/document_viewer/domain/entities/document_content.dart
- lib/features/document_viewer/domain/usecases/read_document_content.dart
- lib/features/document_viewer/presentation/screens/pdf_viewer_screen.dart
- lib/core/navigation/app_router.dart
- lib/features/file_discovery/presentation/screens/home_screen.dart
- lib/features/file_discovery/data/datasources/file_system_datasource_impl.dart
- lib/features/file_discovery/domain/entities/document.dart
- pubspec.yaml

Create comprehensive TDD implementation plan document for PRD-004 (PDF Editor & Annotation Toolkit) following the 3-phase specification-driven workflow.

## Phase 1: Requirements Analysis

### Identified Architecture
**Primary Constraint:** Feature-First Clean Architecture with Presentation/Domain/Data layers, Riverpod for state management/DI, dartz Either for error handling.

### Functional Requirements (EARS Notation)

#### FR-1: Annotation Tools
- **FR-1.1:** WHEN the user selects the highlight tool and selects text in a PDF, THE SYSTEM SHALL apply a yellow highlight annotation to the selected text
- **FR-1.2:** WHEN the user selects the underline tool and selects text in a PDF, THE SYSTEM SHALL apply an underline annotation to the selected text
- **FR-1.3:** WHEN the user selects the strikethrough tool and selects text in a PDF, THE SYSTEM SHALL apply a strikethrough annotation to the selected text
- **FR-1.4:** WHEN the user taps on an existing annotation, THE SYSTEM SHALL display options to change color or delete the annotation
- **FR-1.5:** WHEN the user changes an annotation color, THE SYSTEM SHALL update the annotation appearance immediately
- **FR-1.6:** WHEN the user deletes an annotation, THE SYSTEM SHALL remove it from the PDF immediately

#### FR-2: Signature Tool
- **FR-2.1:** WHEN the user selects the signature tool, THE SYSTEM SHALL display a signature creation canvas
- **FR-2.2:** WHEN the user draws a signature on the canvas, THE SYSTEM SHALL capture the signature as an image
- **FR-2.3:** WHEN the user saves a signature, THE SYSTEM SHALL store it locally for future reuse
- **FR-2.4:** WHEN the user places a saved signature on a PDF page, THE SYSTEM SHALL allow dragging and resizing the signature
- **FR-2.5:** WHEN the user confirms signature placement, THE SYSTEM SHALL embed the signature into the PDF

#### FR-3: Watermark Tool
- **FR-3.1:** WHEN the user selects add watermark, THE SYSTEM SHALL display a text input dialog
- **FR-3.2:** WHEN the user enters watermark text and confirms, THE SYSTEM SHALL apply the watermark to all pages
- **FR-3.3:** WHEN applying a watermark, THE SYSTEM SHALL position it diagonally across the center of each page
- **FR-3.4:** WHEN applying a watermark, THE SYSTEM SHALL use semi-transparent gray text

#### FR-4: Page Numbers Tool
- **FR-4.1:** WHEN the user selects add page numbers, THE SYSTEM SHALL display formatting options (position, format)
- **FR-4.2:** WHEN the user confirms page number settings, THE SYSTEM SHALL add page numbers to all pages
- **FR-4.3:** WHEN adding page numbers, THE SYSTEM SHALL support bottom-center, bottom-left, and bottom-right positions
- **FR-4.4:** WHEN adding page numbers, THE SYSTEM SHALL support formats like "1", "Page 1", "1 of N"

#### FR-5: Form Filling Tool
- **FR-5.1:** WHEN the user opens a PDF with form fields, THE SYSTEM SHALL detect and highlight all fillable fields
- **FR-5.2:** WHEN the user taps a text form field, THE SYSTEM SHALL display a keyboard for text input
- **FR-5.3:** WHEN the user taps a checkbox field, THE SYSTEM SHALL toggle the checkbox state
- **FR-5.4:** WHEN the user taps a dropdown field, THE SYSTEM SHALL display available options
- **FR-5.5:** WHEN the user fills form fields, THE SYSTEM SHALL preserve the entered data when saving

#### FR-6: Save & Persistence
- **FR-6.1:** WHEN the user makes any edit to a PDF, THE SYSTEM SHALL enable a save button
- **FR-6.2:** WHEN the user taps save, THE SYSTEM SHALL create a new PDF file with "_edited" suffix in the Downloads folder
- **FR-6.3:** WHEN the save completes successfully, THE SYSTEM SHALL add the edited document to the recent documents list
- **FR-6.4:** WHEN the save completes successfully, THE SYSTEM SHALL display a success message with option to open or share
- **FR-6.5:** WHEN the save fails, THE SYSTEM SHALL display an error message and preserve unsaved changes

#### FR-7: Editor UI & Navigation
- **FR-7.1:** WHEN the user opens a PDF in view mode, THE SYSTEM SHALL display an "Edit" button in the app bar
- **FR-7.2:** WHEN the user taps the Edit button, THE SYSTEM SHALL enter edit mode and display the editing toolbar
- **FR-7.3:** WHEN in edit mode, THE SYSTEM SHALL display a toolbar with icons for all editing tools
- **FR-7.4:** WHEN the user taps a tool icon, THE SYSTEM SHALL activate that tool and update the UI to show the active state
- **FR-7.5:** WHEN the user taps Cancel in edit mode, THE SYSTEM SHALL discard unsaved changes and return to view mode
- **FR-7.6:** WHEN the user has unsaved changes and taps back, THE SYSTEM SHALL display a confirmation dialog

### Non-Functional Requirements (EARS Notation)

#### NFR-1: Performance
- **NFR-1.1:** WHEN the user applies an annotation, THE SYSTEM SHALL update the display within 100ms
- **NFR-1.2:** WHEN the user saves an edited PDF, THE SYSTEM SHALL complete the save operation within 5 seconds for documents under 10MB

#### NFR-2: Usability
- **NFR-2.1:** WHEN the user enters edit mode, THE SYSTEM SHALL provide clear visual feedback for the active tool
- **NFR-2.2:** WHEN the user makes an error, THE SYSTEM SHALL display error messages in plain language

#### NFR-3: Reliability
- **NFR-3.1:** WHEN a save operation fails, THE SYSTEM SHALL preserve the original file without corruption
- **NFR-3.2:** WHEN the app crashes during editing, THE SYSTEM SHALL not corrupt the original PDF file

#### NFR-4: Architectural Compliance
- **NFR-4.1:** The implementation SHALL follow Feature-First Clean Architecture with Presentation/Domain/Data layers
- **NFR-4.2:** The implementation SHALL use Riverpod for all state management and dependency injection
- **NFR-4.3:** The implementation SHALL use dartz Either for error handling in Domain and Data layers
- **NFR-4.4:** The implementation SHALL use freezed for all entity and model classes

### Assumptions & Constraints

**Assumptions:**
1. Simple e-signatures (drawn images) are sufficient; PKI-based digital signatures are out of scope
2. Watermarks are text-only; image watermarks are out of scope for MVP
3. Page numbers apply to all pages; selective page ranges are out of scope
4. Edited PDFs are saved as new files; in-place editing is not supported
5. Android is the primary target platform
6. Users have storage permissions (handled by existing permissions feature)

**Constraints:**
1. Must use Syncfusion libraries (already in use for viewing)
2. Must not break existing document viewing functionality
3. Must integrate with existing file_discovery feature for recent documents
4. Must follow existing navigation patterns (GoRouter)
5. Budget-conscious: use existing Syncfusion license, avoid additional commercial SDKs

### Success Criteria

1. All EARS requirements have corresponding failing tests (Red phase)
2. All tests pass after implementation (Green phase)
3. Code is refactored for clarity while maintaining test coverage (Refactor phase)
4. Feature integrates seamlessly with existing document_viewer and file_discovery features
5. No regression in existing functionality
6. Architecture adheres to established patterns in `docs/agents/dev.md`

---

## Phase 2: Technical Specification

### Architecture Overview

Create a new `pdf_editor` feature module following Feature-First Clean Architecture:

```
lib/features/pdf_editor/
├── data/
│   ├── datasources/
│   │   └── pdf_editor_datasource.dart          # Syncfusion PDF library wrapper
│   ├── models/
│   │   ├── annotation_model.dart               # Annotation data model
│   │   ├── signature_model.dart                # Saved signature model
│   │   └── editor_settings_model.dart          # Watermark/page number settings
│   └── repositories/
│       └── pdf_editor_repository_impl.dart     # Repository implementation
├── domain/
│   ├── entities/
│   │   ├── annotation.dart                     # Annotation entity
│   │   ├── signature.dart                      # Signature entity
│   │   ├── watermark_settings.dart             # Watermark configuration
│   │   ├── page_number_settings.dart           # Page number configuration
│   │   └── edited_document.dart                # Result of edit operation
│   ├── repositories/
│   │   └── pdf_editor_repository.dart          # Repository interface
│   └── usecases/
│       ├── add_annotation.dart                 # Add highlight/underline/strikethrough
│       ├── remove_annotation.dart              # Delete annotation
│       ├── update_annotation.dart              # Change annotation properties
│       ├── create_signature.dart               # Create and save signature
│       ├── get_saved_signatures.dart           # Retrieve saved signatures
│       ├── add_signature_to_pdf.dart           # Place signature on PDF
│       ├── add_watermark.dart                  # Apply watermark to all pages
│       ├── add_page_numbers.dart               # Add page numbers to all pages
│       ├── detect_form_fields.dart             # Find fillable form fields
│       ├── fill_form_field.dart                # Update form field value
│       └── save_edited_pdf.dart                # Persist edited PDF to storage
└── presentation/
    ├── notifiers/
    │   ├── pdf_editor_notifier.dart            # Main editor state management
    │   ├── annotation_notifier.dart            # Annotation tool state
    │   ├── signature_notifier.dart             # Signature tool state
    │   └── form_filling_notifier.dart          # Form field state
    ├── providers/
    │   └── pdf_editor_providers.dart           # Riverpod provider definitions
    ├── screens/
    │   ├── pdf_editor_screen.dart              # Main editing screen
    │   └── signature_creation_screen.dart      # Signature drawing canvas
    └── widgets/
        ├── editor_toolbar.dart                 # Bottom/top toolbar with tool icons
        ├── annotation_color_picker.dart        # Color selection widget
        ├── watermark_dialog.dart               # Watermark input dialog
        ├── page_number_dialog.dart             # Page number settings dialog
        └── form_field_overlay.dart             # Highlight form fields
```

### Component Design

#### Domain Layer (Business Logic)

**Entities:**
- `Annotation`: Represents a text markup (highlight, underline, strikethrough) with type, color, page number, and bounds
- `Signature`: Represents a saved signature with id, image bytes, and creation date
- `WatermarkSettings`: Configuration for watermark text, opacity, font size, and color
- `PageNumberSettings`: Configuration for position (bottom-left/center/right) and format ("1", "Page 1", "1 of N")
- `EditedDocument`: Result containing file path, name, and success/failure status

**Repository Interface:**
- `PdfEditorRepository`: Abstract class defining all editing operations, returning `Future<Either<Failure, T>>`

**Use Cases:**
Each use case is a single-responsibility class with a `call` method that delegates to the repository. Examples:
- `AddAnnotation(repository)`: Takes annotation entity, returns `Either<Failure, void>`
- `SaveEditedPdf(repository)`: Takes original path and edits, returns `Either<Failure, EditedDocument>`

#### Data Layer (Implementation)

**Data Source:**
- `PdfEditorDataSource`: Wraps Syncfusion's `syncfusion_flutter_pdf` library
  - Uses `PdfDocument.fromFile()` to load PDFs
  - Uses `PdfTextMarkupAnnotation` for highlights/underlines/strikethroughs
  - Uses `PdfPage.graphics.drawString()` for watermarks and page numbers
  - Uses `PdfPage.graphics.drawImage()` for signatures
  - Uses `PdfForm` and `PdfFormField` for form filling
  - Uses `PdfDocument.save()` to write edited bytes to file
  - Uses `path_provider.getExternalStorageDirectory()` for Downloads folder
  - Stores saved signatures in app documents directory as JSON + PNG files

**Repository Implementation:**
- `PdfEditorRepositoryImpl`: Implements `PdfEditorRepository`, delegates to data source, converts exceptions to `Failure` objects using dartz `Either`

**Models:**
- Freezed classes with `fromJson`/`toJson` for persistence (signatures, settings)
- Conversion methods to/from domain entities

#### Presentation Layer (UI & State)

**Notifiers:**
- `PdfEditorNotifier`: Manages overall editor state (edit mode on/off, active tool, unsaved changes flag, current document path)
- `AnnotationNotifier`: Manages annotation list, selected annotation, color picker state
- `SignatureNotifier`: Manages saved signatures, signature creation canvas state
- `FormFillingNotifier`: Manages detected form fields and their values

All notifiers use `AsyncNotifier` or `Notifier` with `@riverpod` annotation, depend on use case providers, and map `Either` results to `AsyncValue` states.

**Screens:**
- `PdfEditorScreen`: Extends existing `PDFViewerScreen` with edit mode toggle, displays `EditorToolbar` when in edit mode, handles save/cancel actions, shows confirmation dialog on back with unsaved changes
- `SignatureCreationScreen`: Full-screen canvas using `CustomPaint` and `GestureDetector` for drawing, save/clear buttons, preview of drawn signature

**Widgets:**
- `EditorToolbar`: Horizontal row of icon buttons (highlight, underline, strikethrough, signature, watermark, page numbers, forms), highlights active tool, positioned at bottom of screen
- `AnnotationColorPicker`: Grid of color circles, appears when annotation is selected
- `WatermarkDialog`: `AlertDialog` with `TextField` for text input, opacity slider, preview
- `PageNumberDialog`: `AlertDialog` with radio buttons for position, dropdown for format
- `FormFieldOverlay`: Positioned widgets that highlight form field locations, tap to focus

### Data Flow Sequence

**Example: Adding a Highlight Annotation**

1. User taps highlight tool icon in `EditorToolbar`
2. `EditorToolbar` calls `ref.read(pdfEditorNotifierProvider.notifier).setActiveTool(ToolType.highlight)`
3. `PdfEditorNotifier` updates state with `activeTool = ToolType.highlight`
4. UI rebuilds, `SfPdfViewer` enables text selection mode
5. User selects text in PDF
6. `SfPdfViewer.onTextSelectionChanged` callback fires with selected text bounds
7. Callback invokes `ref.read(annotationNotifierProvider.notifier).addAnnotation(annotation)`
8. `AnnotationNotifier` calls `AddAnnotation` use case
9. Use case calls `repository.addAnnotation(annotation)`
10. Repository calls `dataSource.addAnnotation(annotation)`
11. Data source uses Syncfusion PDF library to create `PdfTextMarkupAnnotation` and add to document
12. Data source returns success
13. Repository wraps in `Right(void)`
14. Use case returns `Right(void)`
15. Notifier maps to `AsyncValue.data(void)` and updates annotation list
16. UI rebuilds, annotation appears on PDF

**Example: Saving Edited PDF**

1. User taps Save button in app bar
2. Button calls `ref.read(pdfEditorNotifierProvider.notifier).saveDocument()`
3. `PdfEditorNotifier` sets state to `AsyncValue.loading()`
4. Notifier calls `SaveEditedPdf` use case with original path and all edits
5. Use case calls `repository.saveEditedPdf(path, edits)`
6. Repository calls `dataSource.saveEditedPdf(path, edits)`
7. Data source:
   - Loads original PDF with `PdfDocument.fromFile(path)`
   - Applies all edits (annotations, signatures, watermarks, page numbers, form values)
   - Generates new filename: `originalName_edited.pdf`
   - Gets Downloads directory path
   - Saves bytes to `Downloads/originalName_edited.pdf`
   - Returns `EditedDocument` with new path
8. Repository wraps in `Right(EditedDocument)`
9. Use case returns `Right(EditedDocument)`
10. Notifier maps to `AsyncValue.data(EditedDocument)` and resets unsaved changes flag
11. Notifier calls `AddToRecent` use case from file_discovery feature to add edited document to recent list
12. UI shows success SnackBar with "Open" and "Share" actions

### Integration Points

**With document_viewer feature:**
- Reuse `DocumentContent` entity and `ReadDocumentContent` use case to load PDF bytes
- Extend `PDFViewerScreen` or create new `PdfEditorScreen` that wraps `SfPdfViewer` with editing capabilities
- Share `PdfViewerController` between viewing and editing modes

**With file_discovery feature:**
- Call `AddToRecent` use case after successful save to add edited document to recent list
- Reuse `Document` entity for edited documents
- Potentially refresh document list after save (via `ref.invalidate(documentListProvider)`)

**With permissions feature:**
- Assume storage permissions are already granted (handled by existing flow)
- If save fails due to permissions, return `PermissionFailure` and let UI handle

**Navigation:**
- Add new route: `/editor/pdf/:path?name={name}` in `app_router.dart`
- From `PDFViewerScreen`, add "Edit" button that navigates to `/editor/pdf/...`
- From `HomeScreen`, could add "Edit" option in document list item long-press menu (future enhancement)

### Testing Strategy

**Unit Tests (Domain Layer):**
- Test each use case with mocked repository
- Verify `Either` returns `Right` on success, `Left(Failure)` on error
- Test entity validation logic (if any)

**Unit Tests (Data Layer):**
- Test repository implementation with mocked data source
- Test data source with mocked Syncfusion PDF library (or use test PDFs)
- Test model serialization/deserialization
- Test file writing and path generation

**Widget Tests (Presentation Layer):**
- Test each widget in isolation with mocked providers
- Test toolbar button interactions
- Test dialog inputs and validations
- Test screen state transitions (view mode ↔ edit mode)

**Integration Tests:**
- Test complete annotation workflow (select tool → add annotation → save)
- Test signature workflow (create → save → place → save PDF)
- Test form filling workflow (detect fields → fill → save)
- Test save workflow (edit → save → verify file exists → verify in recent list)

**Test Doubles:**
- Use `mocktail` for mocking repositories, data sources, and use cases
- Create fake implementations for Riverpod providers in tests
- Use test PDF files with known content for integration tests

### Risk Assessment & Mitigation

**Risk 1: Syncfusion PDF library limitations**
- *Mitigation:* Research confirms Syncfusion supports all required features; prototype critical features early

**Risk 2: File save failures (permissions, storage full)**
- *Mitigation:* Comprehensive error handling with specific `Failure` types; preserve original file; show clear error messages

**Risk 3: Large PDF performance**
- *Mitigation:* Test with large files (50+ pages); implement loading indicators; consider lazy loading for annotations

**Risk 4: Signature image quality**
- *Mitigation:* Use high-resolution canvas; save as PNG with transparency; allow user to redraw if unsatisfied

**Risk 5: Form field detection accuracy**
- *Mitigation:* Rely on Syncfusion's built-in form detection; handle edge cases gracefully; provide manual field creation in future iteration

**Risk 6: State management complexity**
- *Mitigation:* Keep notifiers focused on single responsibility; use separate notifiers for each tool; leverage Riverpod's dependency injection

---

## Phase 3: Implementation Plan (TDD Task Hierarchy)

### Task Structure

Each feature requirement follows the Red-Green-Refactor cycle:
1. **[RED]** Write failing test
2. **[GREEN]** Implement minimal code to pass test
3. **[REFACTOR]** Improve code quality while keeping tests green

### Task Hierarchy

**Root Task: Implement PRD-004 - PDF Editor & Annotation Toolkit**

#### 1. Setup & Architectural Scaffolding

**1.1. Add Dependencies**
- Add `syncfusion_flutter_pdf` to `pubspec.yaml`
- Run `flutter pub get`
- Verify no dependency conflicts

**1.2. Create Feature Module Structure**
- Create `lib/features/pdf_editor/` directory
- Create subdirectories: `data/`, `domain/`, `presentation/`
- Create nested subdirectories following architecture pattern

**1.3. Define Core Domain Entities**
- Create `domain/entities/annotation.dart` with freezed
- Create `domain/entities/signature.dart` with freezed
- Create `domain/entities/watermark_settings.dart` with freezed
- Create `domain/entities/page_number_settings.dart` with freezed
- Create `domain/entities/edited_document.dart` with freezed
- Run `build_runner` to generate freezed code

**1.4. Define Repository Interface**
- Create `domain/repositories/pdf_editor_repository.dart` abstract class
- Define method signatures returning `Future<Either<Failure, T>>`

**1.5. Create Failure Types**
- Add `PdfEditFailure`, `FileWriteFailure`, `InvalidPdfFailure` to `core/error/failures.dart`

#### 2. Implement Annotation Tools (FR-1)

**2.1. Implement FR-1.1: Add Highlight Annotation**

**2.1.1. [RED] Write failing test for AddAnnotation use case**
- Create `test/features/pdf_editor/domain/usecases/add_annotation_test.dart`
- Test: given valid annotation, when call is invoked, then returns Right(void)
- Test: given repository error, when call is invoked, then returns Left(Failure)

**2.1.2. [GREEN] Implement AddAnnotation use case**
- Create `domain/usecases/add_annotation.dart`
- Implement `call` method that delegates to repository
- Ensure tests pass

**2.1.3. [REFACTOR] Improve use case code quality**
- Add documentation comments
- Ensure consistent naming
- Verify error handling

**2.1.4. [RED] Write failing test for repository implementation**
- Create `test/features/pdf_editor/data/repositories/pdf_editor_repository_impl_test.dart`
- Test: given valid annotation, when addAnnotation is called, then delegates to data source and returns Right(void)
- Test: given data source throws exception, when addAnnotation is called, then returns Left(PdfEditFailure)

**2.1.5. [GREEN] Implement PdfEditorRepositoryImpl.addAnnotation**
- Create `data/repositories/pdf_editor_repository_impl.dart`
- Implement `addAnnotation` method with try-catch and Either wrapping
- Ensure tests pass

**2.1.6. [REFACTOR] Improve repository code quality**
- Extract error mapping logic if repeated
- Add documentation

**2.1.7. [RED] Write failing test for data source**
- Create `test/features/pdf_editor/data/datasources/pdf_editor_datasource_test.dart`
- Test: given valid annotation, when addAnnotation is called, then creates PdfTextMarkupAnnotation with correct properties
- Test: given invalid PDF, when addAnnotation is called, then throws exception

**2.1.8. [GREEN] Implement PdfEditorDataSource.addAnnotation**
- Create `data/datasources/pdf_editor_datasource.dart`
- Implement `addAnnotation` using Syncfusion PDF library
- Load PDF document, get page, create annotation, add to page
- Ensure tests pass

**2.1.9. [REFACTOR] Improve data source code quality**
- Extract PDF loading logic to helper method
- Add error handling for invalid pages

**2.1.10. [RED] Write failing test for AnnotationNotifier**
- Create `test/features/pdf_editor/presentation/notifiers/annotation_notifier_test.dart`
- Test: given valid annotation, when addAnnotation is called, then state becomes AsyncData and annotation is added to list
- Test: given use case returns failure, when addAnnotation is called, then state becomes AsyncError

**2.1.11. [GREEN] Implement AnnotationNotifier**
- Create `presentation/notifiers/annotation_notifier.dart` with `@riverpod`
- Implement `addAnnotation` method that calls use case and maps Either to AsyncValue
- Maintain list of annotations in state
- Ensure tests pass

**2.1.12. [REFACTOR] Improve notifier code quality**
- Simplify state updates
- Add documentation

**2.1.13. [RED] Write failing widget test for annotation UI**
- Create `test/features/pdf_editor/presentation/widgets/editor_toolbar_test.dart`
- Test: given highlight tool is tapped, then notifier's setActiveTool is called with ToolType.highlight

**2.1.14. [GREEN] Implement EditorToolbar widget**
- Create `presentation/widgets/editor_toolbar.dart`
- Add icon button for highlight tool
- Wire up to notifier
- Ensure tests pass

**2.1.15. [REFACTOR] Improve toolbar widget**
- Extract icon button to reusable component
- Improve layout and styling

**2.2. Implement FR-1.2: Add Underline Annotation**
- Follow same Red-Green-Refactor cycle as FR-1.1
- Reuse existing use case, repository, data source with annotation type parameter
- Add underline button to toolbar
- Write tests for underline-specific behavior

**2.3. Implement FR-1.3: Add Strikethrough Annotation**
- Follow same Red-Green-Refactor cycle as FR-1.1
- Reuse existing use case, repository, data source with annotation type parameter
- Add strikethrough button to toolbar
- Write tests for strikethrough-specific behavior

**2.4. Implement FR-1.4: Display Annotation Options**

**2.4.1. [RED] Write failing test for annotation selection**
- Test: given annotation is tapped, when onAnnotationTapped is called, then selected annotation is set in state

**2.4.2. [GREEN] Implement annotation selection in notifier**
- Add `selectedAnnotation` field to state
- Implement `selectAnnotation` method
- Ensure tests pass

**2.4.3. [REFACTOR] Improve selection logic**

**2.4.4. [RED] Write failing widget test for color picker**
- Create `test/features/pdf_editor/presentation/widgets/annotation_color_picker_test.dart`
- Test: given color is tapped, when onColorSelected is called, then notifier's updateAnnotationColor is called

**2.4.5. [GREEN] Implement AnnotationColorPicker widget**
- Create `presentation/widgets/annotation_color_picker.dart`
- Display grid of color options
- Wire up to notifier
- Ensure tests pass

**2.4.6. [REFACTOR] Improve color picker UI**

**2.5. Implement FR-1.5: Change Annotation Color**

**2.5.1. [RED] Write failing test for UpdateAnnotation use case**
- Test: given valid annotation with new color, when call is invoked, then returns Right(void)

**2.5.2. [GREEN] Implement UpdateAnnotation use case**
- Create `domain/usecases/update_annotation.dart`
- Implement `call` method
- Ensure tests pass

**2.5.3. [REFACTOR] Improve use case**

**2.5.4. [RED] Write failing test for repository.updateAnnotation**
- Test: given valid annotation, when updateAnnotation is called, then delegates to data source

**2.5.5. [GREEN] Implement repository.updateAnnotation**
- Add method to `PdfEditorRepositoryImpl`
- Ensure tests pass

**2.5.6. [REFACTOR] Improve repository method**

**2.5.7. [RED] Write failing test for data source.updateAnnotation**
- Test: given annotation with new color, when updateAnnotation is called, then modifies existing annotation properties

**2.5.8. [GREEN] Implement data source.updateAnnotation**
- Add method to `PdfEditorDataSource`
- Use Syncfusion API to modify annotation
- Ensure tests pass

**2.5.9. [REFACTOR] Improve data source method**

**2.5.10. [RED] Write failing test for notifier.updateAnnotationColor**
- Test: given selected annotation and new color, when updateAnnotationColor is called, then annotation is updated in state

**2.5.11. [GREEN] Implement notifier.updateAnnotationColor**
- Add method to `AnnotationNotifier`
- Call use case and update state
- Ensure tests pass

**2.5.12. [REFACTOR] Improve notifier method**

**2.6. Implement FR-1.6: Delete Annotation**

**2.6.1. [RED] Write failing test for RemoveAnnotation use case**
- Test: given valid annotation id, when call is invoked, then returns Right(void)

**2.6.2. [GREEN] Implement RemoveAnnotation use case**
- Create `domain/usecases/remove_annotation.dart`
- Implement `call` method
- Ensure tests pass

**2.6.3. [REFACTOR] Improve use case**

**2.6.4. [RED] Write failing test for repository.removeAnnotation**
- Test: given annotation id, when removeAnnotation is called, then delegates to data source

**2.6.5. [GREEN] Implement repository.removeAnnotation**
- Add method to `PdfEditorRepositoryImpl`
- Ensure tests pass

**2.6.6. [REFACTOR] Improve repository method**

**2.6.7. [RED] Write failing test for data source.removeAnnotation**
- Test: given annotation id, when removeAnnotation is called, then removes annotation from PDF

**2.6.8. [GREEN] Implement data source.removeAnnotation**
- Add method to `PdfEditorDataSource`
- Use Syncfusion API to remove annotation
- Ensure tests pass

**2.6.9. [REFACTOR] Improve data source method**

**2.6.10. [RED] Write failing test for notifier.removeAnnotation**
- Test: given annotation id, when removeAnnotation is called, then annotation is removed from state

**2.6.11. [GREEN] Implement notifier.removeAnnotation**
- Add method to `AnnotationNotifier`
- Call use case and update state
- Ensure tests pass

**2.6.12. [REFACTOR] Improve notifier method**

**2.6.13. [RED] Write failing widget test for delete button**
- Test: given delete button is tapped, when onDelete is called, then notifier's removeAnnotation is called

**2.6.14. [GREEN] Add delete button to color picker or toolbar**
- Update `AnnotationColorPicker` or create delete button
- Wire up to notifier
- Ensure tests pass

**2.6.15. [REFACTOR] Improve delete UI**

#### 3. Implement Signature Tool (FR-2)

**3.1. Implement FR-2.1 & FR-2.2: Signature Creation Canvas**

**3.1.1. [RED] Write failing widget test for SignatureCreationScreen**
- Create `test/features/pdf_editor/presentation/screens/signature_creation_screen_test.dart`
- Test: given user draws on canvas, when drawing completes, then signature data is captured

**3.1.2. [GREEN] Implement SignatureCreationScreen**
- Create `presentation/screens/signature_creation_screen.dart`
- Use `CustomPaint` with `GestureDetector` for drawing
- Capture drawing points
- Ensure tests pass

**3.1.3. [REFACTOR] Improve signature canvas**
- Smooth drawing lines
- Add clear button
- Improve UI layout

**3.2. Implement FR-2.3: Save Signature**

**3.2.1. [RED] Write failing test for CreateSignature use case**
- Create `test/features/pdf_editor/domain/usecases/create_signature_test.dart`
- Test: given signature image bytes, when call is invoked, then returns Right(Signature)

**3.2.2. [GREEN] Implement CreateSignature use case**
- Create `domain/usecases/create_signature.dart`
- Implement `call` method
- Ensure tests pass

**3.2.3. [REFACTOR] Improve use case**

**3.2.4. [RED] Write failing test for repository.createSignature**
- Test: given signature bytes, when createSignature is called, then saves to storage and returns Signature entity

**3.2.5. [GREEN] Implement repository.createSignature**
- Add method to `PdfEditorRepositoryImpl`
- Ensure tests pass

**3.2.6. [REFACTOR] Improve repository method**

**3.2.7. [RED] Write failing test for data source.saveSignature**
- Test: given signature bytes, when saveSignature is called, then writes PNG file and JSON metadata to app documents directory

**3.2.8. [GREEN] Implement data source.saveSignature**
- Add method to `PdfEditorDataSource`
- Use `path_provider.getApplicationDocumentsDirectory()`
- Save PNG and JSON files
- Ensure tests pass

**3.2.9. [REFACTOR] Improve data source method**

**3.2.10. [RED] Write failing test for SignatureNotifier.createSignature**
- Create `test/features/pdf_editor/presentation/notifiers/signature_notifier_test.dart`
- Test: given signature bytes, when createSignature is called, then signature is saved and added to list

**3.2.11. [GREEN] Implement SignatureNotifier**
- Create `presentation/notifiers/signature_notifier.dart` with `@riverpod`
- Implement `createSignature` method
- Ensure tests pass

**3.2.12. [REFACTOR] Improve notifier**

**3.3. Implement FR-2.4 & FR-2.5: Place Signature on PDF**

**3.3.1. [RED] Write failing test for GetSavedSignatures use case**
- Create `test/features/pdf_editor/domain/usecases/get_saved_signatures_test.dart`
- Test: when call is invoked, then returns Right(List<Signature>)

**3.3.2. [GREEN] Implement GetSavedSignatures use case**
- Create `domain/usecases/get_saved_signatures.dart`
- Implement `call` method
- Ensure tests pass

**3.3.3. [REFACTOR] Improve use case**

**3.3.4. [RED] Write failing test for repository.getSavedSignatures**
- Test: when getSavedSignatures is called, then returns list of signatures from storage

**3.3.5. [GREEN] Implement repository.getSavedSignatures**
- Add method to `PdfEditorRepositoryImpl`
- Ensure tests pass

**3.3.6. [REFACTOR] Improve repository method**

**3.3.7. [RED] Write failing test for data source.loadSignatures**
- Test: when loadSignatures is called, then reads JSON files from app documents directory and returns signature list

**3.3.8. [GREEN] Implement data source.loadSignatures**
- Add method to `PdfEditorDataSource`
- Read JSON files and PNG images
- Ensure tests pass

**3.3.9. [REFACTOR] Improve data source method**

**3.3.10. [RED] Write failing test for AddSignatureToPdf use case**
- Create `test/features/pdf_editor/domain/usecases/add_signature_to_pdf_test.dart`
- Test: given signature and position, when call is invoked, then returns Right(void)

**3.3.11. [GREEN] Implement AddSignatureToPdf use case**
- Create `domain/usecases/add_signature_to_pdf.dart`
- Implement `call` method
- Ensure tests pass

**3.3.12. [REFACTOR] Improve use case**

**3.3.13. [RED] Write failing test for repository.addSignatureToPdf**
- Test: given signature and position, when addSignatureToPdf is called, then delegates to data source

**3.3.14. [GREEN] Implement repository.addSignatureToPdf**
- Add method to `PdfEditorRepositoryImpl`
- Ensure tests pass

**3.3.15. [REFACTOR] Improve repository method**

**3.3.16. [RED] Write failing test for data source.addSignatureToPdf**
- Test: given signature image and position, when addSignatureToPdf is called, then draws image on PDF page at specified position

**3.3.17. [GREEN] Implement data source.addSignatureToPdf**
- Add method to `PdfEditorDataSource`
- Use `PdfPage.graphics.drawImage()` from Syncfusion
- Ensure tests pass

**3.3.18. [REFACTOR] Improve data source method**

**3.3.19. [RED] Write failing test for notifier.addSignatureToPdf**
- Test: given signature and position, when addSignatureToPdf is called, then signature is added to PDF

**3.3.20. [GREEN] Implement notifier.addSignatureToPdf**
- Add method to `SignatureNotifier`
- Call use case and update state
- Ensure tests pass

**3.3.21. [REFACTOR] Improve notifier method**

**3.3.22. [RED] Write failing widget test for signature placement UI**
- Test: given signature is selected, when user taps PDF, then signature is placed at tap position

**3.3.23. [GREEN] Implement signature placement UI**
- Add signature button to toolbar
- Show signature picker dialog
- Handle tap on PDF to place signature
- Ensure tests pass

**3.3.24. [REFACTOR] Improve signature placement UX**
- Add drag and resize handles
- Show preview before confirming

#### 4. Implement Watermark Tool (FR-3)

**4.1. Implement FR-3.1 & FR-3.2: Watermark Input Dialog**

**4.1.1. [RED] Write failing widget test for WatermarkDialog**
- Create `test/features/pdf_editor/presentation/widgets/watermark_dialog_test.dart`
- Test: given text is entered and confirmed, when onConfirm is called, then notifier's addWatermark is called with text

**4.1.2. [GREEN] Implement WatermarkDialog widget**
- Create `presentation/widgets/watermark_dialog.dart`
- Add `TextField` for text input
- Add confirm/cancel buttons
- Ensure tests pass

**4.1.3. [REFACTOR] Improve dialog UI**
- Add opacity slider
- Add preview

**4.2. Implement FR-3.3 & FR-3.4: Apply Watermark**

**4.2.1. [RED] Write failing test for AddWatermark use case**
- Create `test/features/pdf_editor/domain/usecases/add_watermark_test.dart`
- Test: given watermark settings, when call is invoked, then returns Right(void)

**4.2.2. [GREEN] Implement AddWatermark use case**
- Create `domain/usecases/add_watermark.dart`
- Implement `call` method
- Ensure tests pass

**4.2.3. [REFACTOR] Improve use case**

**4.2.4. [RED] Write failing test for repository.addWatermark**
- Test: given watermark settings, when addWatermark is called, then delegates to data source

**4.2.5. [GREEN] Implement repository.addWatermark**
- Add method to `PdfEditorRepositoryImpl`
- Ensure tests pass

**4.2.6. [REFACTOR] Improve repository method**

**4.2.7. [RED] Write failing test for data source.addWatermark**
- Test: given watermark text, when addWatermark is called, then draws text diagonally on all pages with semi-transparent gray color

**4.2.8. [GREEN] Implement data source.addWatermark**
- Add method to `PdfEditorDataSource`
- Loop through all pages
- Use `PdfPage.graphics.drawString()` with rotation and transparency
- Ensure tests pass

**4.2.9. [REFACTOR] Improve data source method**
- Extract text drawing logic to helper
- Add configurable opacity and color

**4.2.10. [RED] Write failing test for PdfEditorNotifier.addWatermark**
- Create `test/features/pdf_editor/presentation/notifiers/pdf_editor_notifier_test.dart`
- Test: given watermark settings, when addWatermark is called, then watermark is applied to PDF

**4.2.11. [GREEN] Implement PdfEditorNotifier**
- Create `presentation/notifiers/pdf_editor_notifier.dart` with `@riverpod`
- Implement `addWatermark` method
- Ensure tests pass

**4.2.12. [REFACTOR] Improve notifier**

**4.2.13. [RED] Write failing widget test for watermark button**
- Test: given watermark button is tapped, when onTap is called, then watermark dialog is shown

**4.2.14. [GREEN] Add watermark button to toolbar**
- Update `EditorToolbar` with watermark icon
- Show dialog on tap
- Ensure tests pass

**4.2.15. [REFACTOR] Improve watermark UI**

#### 5. Implement Page Numbers Tool (FR-4)

**5.1. Implement FR-4.1: Page Number Settings Dialog**

**5.1.1. [RED] Write failing widget test for PageNumberDialog**
- Create `test/features/pdf_editor/presentation/widgets/page_number_dialog_test.dart`
- Test: given settings are selected and confirmed, when onConfirm is called, then notifier's addPageNumbers is called with settings

**5.1.2. [GREEN] Implement PageNumberDialog widget**
- Create `presentation/widgets/page_number_dialog.dart`
- Add radio buttons for position
- Add dropdown for format
- Ensure tests pass

**5.1.3. [REFACTOR] Improve dialog UI**

**5.2. Implement FR-4.2, FR-4.3, FR-4.4: Apply Page Numbers**

**5.2.1. [RED] Write failing test for AddPageNumbers use case**
- Create `test/features/pdf_editor/domain/usecases/add_page_numbers_test.dart`
- Test: given page number settings, when call is invoked, then returns Right(void)

**5.2.2. [GREEN] Implement AddPageNumbers use case**
- Create `domain/usecases/add_page_numbers.dart`
- Implement `call` method
- Ensure tests pass

**5.2.3. [REFACTOR] Improve use case**

**5.2.4. [RED] Write failing test for repository.addPageNumbers**
- Test: given page number settings, when addPageNumbers is called, then delegates to data source

**5.2.5. [GREEN] Implement repository.addPageNumbers**
- Add method to `PdfEditorRepositoryImpl`
- Ensure tests pass

**5.2.6. [REFACTOR] Improve repository method**

**5.2.7. [RED] Write failing test for data source.addPageNumbers**
- Test: given settings with bottom-center position and "Page N" format, when addPageNumbers is called, then draws page numbers on all pages at bottom-center
- Test: given settings with bottom-right position and "N of M" format, when addPageNumbers is called, then draws correctly formatted numbers at bottom-right

**5.2.8. [GREEN] Implement data source.addPageNumbers**
- Add method to `PdfEditorDataSource`
- Loop through all pages
- Calculate position based on settings
- Format text based on settings
- Use `PdfPage.graphics.drawString()`
- Ensure tests pass

**5.2.9. [REFACTOR] Improve data source method**
- Extract position calculation logic
- Extract format logic

**5.2.10. [RED] Write failing test for notifier.addPageNumbers**
- Test: given page number settings, when addPageNumbers is called, then page numbers are applied to PDF

**5.2.11. [GREEN] Implement notifier.addPageNumbers**
- Add method to `PdfEditorNotifier`
- Call use case and update state
- Ensure tests pass

**5.2.12. [REFACTOR] Improve notifier method**

**5.2.13. [RED] Write failing widget test for page numbers button**
- Test: given page numbers button is tapped, when onTap is called, then page number dialog is shown

**5.2.14. [GREEN] Add page numbers button to toolbar**
- Update `EditorToolbar` with page numbers icon
- Show dialog on tap
- Ensure tests pass

**5.2.15. [REFACTOR] Improve page numbers UI**

#### 6. Implement Form Filling Tool (FR-5)

**6.1. Implement FR-5.1: Detect Form Fields**

**6.1.1. [RED] Write failing test for DetectFormFields use case**
- Create `test/features/pdf_editor/domain/usecases/detect_form_fields_test.dart`
- Test: given PDF path, when call is invoked, then returns Right(List<FormField>)

**6.1.2. [GREEN] Implement DetectFormFields use case**
- Create `domain/usecases/detect_form_fields.dart`
- Create `domain/entities/form_field.dart` with freezed
- Implement `call` method
- Ensure tests pass

**6.1.3. [REFACTOR] Improve use case**

**6.1.4. [RED] Write failing test for repository.detectFormFields**
- Test: given PDF path, when detectFormFields is called, then returns list of form fields from data source

**6.1.5. [GREEN] Implement repository.detectFormFields**
- Add method to `PdfEditorRepositoryImpl`
- Ensure tests pass

**6.1.6. [REFACTOR] Improve repository method**

**6.1.7. [RED] Write failing test for data source.detectFormFields**
- Test: given PDF with form fields, when detectFormFields is called, then returns list of fields with types, names, and positions

**6.1.8. [GREEN] Implement data source.detectFormFields**
- Add method to `PdfEditorDataSource`
- Use Syncfusion's `PdfForm` and `PdfFormFieldCollection`
- Extract field properties
- Ensure tests pass

**6.1.9. [REFACTOR] Improve data source method**

**6.1.10. [RED] Write failing test for FormFillingNotifier.loadFormFields**
- Create `test/features/pdf_editor/presentation/notifiers/form_filling_notifier_test.dart`
- Test: given PDF path, when loadFormFields is called, then state contains list of form fields

**6.1.11. [GREEN] Implement FormFillingNotifier**
- Create `presentation/notifiers/form_filling_notifier.dart` with `@riverpod`
- Implement `loadFormFields` method
- Ensure tests pass

**6.1.12. [REFACTOR] Improve notifier**

**6.2. Implement FR-5.2, FR-5.3, FR-5.4: Fill Form Fields**

**6.2.1. [RED] Write failing test for FillFormField use case**
- Create `test/features/pdf_editor/domain/usecases/fill_form_field_test.dart`
- Test: given field name and value, when call is invoked, then returns Right(void)

**6.2.2. [GREEN] Implement FillFormField use case**
- Create `domain/usecases/fill_form_field.dart`
- Implement `call` method
- Ensure tests pass

**6.2.3. [REFACTOR] Improve use case**

**6.2.4. [RED] Write failing test for repository.fillFormField**
- Test: given field name and value, when fillFormField is called, then delegates to data source

**6.2.5. [GREEN] Implement repository.fillFormField**
- Add method to `PdfEditorRepositoryImpl`
- Ensure tests pass

**6.2.6. [REFACTOR] Improve repository method**

**6.2.7. [RED] Write failing test for data source.fillFormField**
- Test: given text field name and value, when fillFormField is called, then sets field value in PDF
- Test: given checkbox field name and boolean value, when fillFormField is called, then toggles checkbox
- Test: given dropdown field name and selected option, when fillFormField is called, then sets selected value

**6.2.8. [GREEN] Implement data source.fillFormField**
- Add method to `PdfEditorDataSource`
- Use Syncfusion's `PdfFormField` API to set values
- Handle different field types (text, checkbox, dropdown)
- Ensure tests pass

**6.2.9. [REFACTOR] Improve data source method**
- Extract field type handling logic

**6.2.10. [RED] Write failing test for notifier.fillFormField**
- Test: given field name and value, when fillFormField is called, then field value is updated in state

**6.2.11. [GREEN] Implement notifier.fillFormField**
- Add method to `FormFillingNotifier`
- Call use case and update state
- Ensure tests pass

**6.2.12. [REFACTOR] Improve notifier method**

**6.3. Implement FR-5.5: Preserve Form Data on Save**
- This is handled automatically by Syncfusion when saving the PDF document
- Add integration test to verify form data persists after save

**6.4. Implement Form Field UI**

**6.4.1. [RED] Write failing widget test for FormFieldOverlay**
- Create `test/features/pdf_editor/presentation/widgets/form_field_overlay_test.dart`
- Test: given form fields, when overlay is displayed, then highlights are shown at field positions
- Test: given field is tapped, when onTap is called, then appropriate input UI is shown

**6.4.2. [GREEN] Implement FormFieldOverlay widget**
- Create `presentation/widgets/form_field_overlay.dart`
- Position highlights over form fields
- Handle tap events
- Show keyboard for text fields, dialog for dropdowns
- Ensure tests pass

**6.4.3. [REFACTOR] Improve form field overlay UI**

**6.4.4. [RED] Write failing widget test for forms button**
- Test: given forms button is tapped, when onTap is called, then form fields are detected and overlay is shown

**6.4.5. [GREEN] Add forms button to toolbar**
- Update `EditorToolbar` with forms icon
- Trigger form detection on tap
- Ensure tests pass

**6.4.6. [REFACTOR] Improve forms UI**

#### 7. Implement Save & Persistence (FR-6)

**7.1. Implement FR-6.1: Enable Save Button**

**7.1.1. [RED] Write failing test for unsaved changes tracking**
- Test: given annotation is added, when state is checked, then hasUnsavedChanges is true
- Test: given document is saved, when state is checked, then hasUnsavedChanges is false

**7.1.2. [GREEN] Implement unsaved changes tracking in PdfEditorNotifier**
- Add `hasUnsavedChanges` field to state
- Set to true when any edit is made
- Set to false after successful save
- Ensure tests pass

**7.1.3. [REFACTOR] Improve change tracking logic**

**7.2. Implement FR-6.2: Save Edited PDF**

**7.2.1. [RED] Write failing test for SaveEditedPdf use case**
- Create `test/features/pdf_editor/domain/usecases/save_edited_pdf_test.dart`
- Test: given original path and edits, when call is invoked, then returns Right(EditedDocument) with new path

**7.2.2. [GREEN] Implement SaveEditedPdf use case**
- Create `domain/usecases/save_edited_pdf.dart`
- Implement `call` method
- Ensure tests pass

**7.2.3. [REFACTOR] Improve use case**

**7.2.4. [RED] Write failing test for repository.saveEditedPdf**
- Test: given original path, when saveEditedPdf is called, then returns EditedDocument with path in Downloads folder
- Test: given save fails, when saveEditedPdf is called, then returns Left(FileWriteFailure)

**7.2.5. [GREEN] Implement repository.saveEditedPdf**
- Add method to `PdfEditorRepositoryImpl`
- Ensure tests pass

**7.2.6. [REFACTOR] Improve repository method**

**7.2.7. [RED] Write failing test for data source.saveEditedPdf**
- Test: given PDF document with edits, when saveEditedPdf is called, then writes file to Downloads folder with "_edited" suffix
- Test: given invalid path, when saveEditedPdf is called, then throws exception

**7.2.8. [GREEN] Implement data source.saveEditedPdf**
- Add method to `PdfEditorDataSource`
- Load original PDF
- Apply all pending edits (annotations, signatures, watermarks, page numbers, form values)
- Generate new filename with "_edited" suffix
- Get Downloads directory path using `path_provider`
- Save bytes to file
- Return new file path
- Ensure tests pass

**7.2.9. [REFACTOR] Improve data source method**
- Extract filename generation logic
- Extract directory path logic
- Add error handling for storage full, permissions denied

**7.2.10. [RED] Write failing test for notifier.saveDocument**
- Test: given unsaved changes, when saveDocument is called, then state becomes AsyncData(EditedDocument) and hasUnsavedChanges is false
- Test: given save fails, when saveDocument is called, then state becomes AsyncError and hasUnsavedChanges remains true

**7.2.11. [GREEN] Implement notifier.saveDocument**
- Add method to `PdfEditorNotifier`
- Call use case and update state
- Reset unsaved changes flag on success
- Ensure tests pass

**7.2.12. [REFACTOR] Improve notifier method**

**7.3. Implement FR-6.3: Add to Recent Documents**

**7.3.1. [RED] Write failing test for adding edited document to recent list**
- Test: given successful save, when saveDocument completes, then AddToRecent use case is called with edited document

**7.3.2. [GREEN] Implement integration with file_discovery**
- Import `AddToRecent` use case from file_discovery feature
- Call it after successful save in `PdfEditorNotifier.saveDocument`
- Convert `EditedDocument` to `Document` entity
- Ensure tests pass

**7.3.3. [REFACTOR] Improve integration logic**

**7.4. Implement FR-6.4: Display Success Message**

**7.4.1. [RED] Write failing widget test for save success UI**
- Test: given save succeeds, when state updates, then SnackBar is shown with "Open" and "Share" actions

**7.4.2. [GREEN] Implement save success UI**
- Add `ref.listen` in `PdfEditorScreen` to watch save state
- Show SnackBar on success with action buttons
- Ensure tests pass

**7.4.3. [REFACTOR] Improve success UI**

**7.5. Implement FR-6.5: Display Error Message**

**7.5.1. [RED] Write failing widget test for save error UI**
- Test: given save fails, when state updates, then error message is displayed

**7.5.2. [GREEN] Implement save error UI**
- Add error handling in `ref.listen`
- Show error SnackBar or dialog
- Ensure tests pass

**7.5.3. [REFACTOR] Improve error UI**

#### 8. Implement Editor UI & Navigation (FR-7)

**8.1. Implement FR-7.1 & FR-7.2: Edit Mode Toggle**

**8.1.1. [RED] Write failing test for edit mode state**
- Test: given view mode, when enterEditMode is called, then isEditMode is true
- Test: given edit mode, when exitEditMode is called, then isEditMode is false

**8.1.2. [GREEN] Implement edit mode state in PdfEditorNotifier**
- Add `isEditMode` field to state
- Implement `enterEditMode` and `exitEditMode` methods
- Ensure tests pass

**8.1.3. [REFACTOR] Improve mode toggle logic**

**8.2. Implement FR-7.3 & FR-7.4: Editing Toolbar**

**8.2.1. [RED] Write failing widget test for EditorToolbar visibility**
- Test: given edit mode is false, when screen is built, then toolbar is not visible
- Test: given edit mode is true, when screen is built, then toolbar is visible

**8.2.2. [GREEN] Implement toolbar visibility in PdfEditorScreen**
- Create `presentation/screens/pdf_editor_screen.dart`
- Conditionally show `EditorToolbar` based on `isEditMode`
- Ensure tests pass

**8.2.3. [REFACTOR] Improve screen layout**

**8.2.4. [RED] Write failing widget test for active tool highlighting**
- Test: given highlight tool is active, when toolbar is built, then highlight button is highlighted

**8.2.5. [GREEN] Implement active tool highlighting in EditorToolbar**
- Update `EditorToolbar` to highlight active tool button
- Use different color or border for active state
- Ensure tests pass

**8.2.6. [REFACTOR] Improve toolbar styling**

**8.3. Implement FR-7.5: Cancel with Discard Changes**

**8.3.1. [RED] Write failing test for cancel action**
- Test: given unsaved changes, when cancel is called, then changes are discarded and edit mode is exited

**8.3.2. [GREEN] Implement cancel action in notifier**
- Add `cancelEditing` method to `PdfEditorNotifier`
- Reset all edits
- Exit edit mode
- Ensure tests pass

**8.3.3. [REFACTOR] Improve cancel logic**

**8.3.4. [RED] Write failing widget test for cancel button**
- Test: given cancel button is tapped, when onTap is called, then notifier's cancelEditing is called

**8.3.5. [GREEN] Add cancel button to app bar**
- Update `PdfEditorScreen` app bar with cancel button
- Wire up to notifier
- Ensure tests pass

**8.3.6. [REFACTOR] Improve cancel UI**

**8.4. Implement FR-7.6: Confirmation Dialog on Back**

**8.4.1. [RED] Write failing widget test for back button handling**
- Test: given unsaved changes, when back button is pressed, then confirmation dialog is shown
- Test: given no unsaved changes, when back button is pressed, then navigation proceeds without dialog

**8.4.2. [GREEN] Implement back button handling**
- Use `WillPopScope` or `PopScope` in `PdfEditorScreen`
- Check `hasUnsavedChanges` before allowing pop
- Show confirmation dialog if changes exist
- Ensure tests pass

**8.4.3. [REFACTOR] Improve back button UX**

**8.5. Implement Navigation Integration**

**8.5.1. [RED] Write failing test for editor route**
- Create `test/core/navigation/app_router_test.dart` (if not exists)
- Test: given editor route with path parameter, when navigated, then PdfEditorScreen is displayed

**8.5.2. [GREEN] Add editor route to app_router.dart**
- Add `/editor/pdf/:path` route to `app_router.dart`
- Pass path and name parameters to `PdfEditorScreen`
- Ensure tests pass

**8.5.3. [REFACTOR] Improve route configuration**

**8.5.4. [RED] Write failing widget test for Edit button in PDFViewerScreen**
- Test: given Edit button is tapped, when onTap is called, then navigation to editor route occurs

**8.5.5. [GREEN] Add Edit button to PDFViewerScreen**
- Update `pdf_viewer_screen.dart` app bar with Edit button
- Navigate to `/editor/pdf/...` on tap
- Ensure tests pass

**8.5.6. [REFACTOR] Improve Edit button placement**

#### 9. Integration & End-to-End Testing

**9.1. Create Integration Test for Complete Annotation Workflow**
- Create `integration_test/pdf_editor_annotation_test.dart`
- Test: user opens PDF → enters edit mode → adds highlight → changes color → saves → verifies file exists → verifies in recent list

**9.2. Create Integration Test for Signature Workflow**
- Create `integration_test/pdf_editor_signature_test.dart`
- Test: user creates signature → saves signature → opens PDF → enters edit mode → places signature → saves → verifies signature in saved PDF

**9.3. Create Integration Test for Watermark Workflow**
- Create `integration_test/pdf_editor_watermark_test.dart`
- Test: user opens PDF → enters edit mode → adds watermark → saves → verifies watermark on all pages

**9.4. Create Integration Test for Page Numbers Workflow**
- Create `integration_test/pdf_editor_page_numbers_test.dart`
- Test: user opens PDF → enters edit mode → adds page numbers → saves → verifies page numbers on all pages

**9.5. Create Integration Test for Form Filling Workflow**
- Create `integration_test/pdf_editor_form_filling_test.dart`
- Test: user opens PDF with forms → enters edit mode → fills text field → toggles checkbox → saves → verifies form data persists

**9.6. Create Integration Test for Unsaved Changes Workflow**
- Create `integration_test/pdf_editor_unsaved_changes_test.dart`
- Test: user makes edits → taps back → sees confirmation dialog → cancels → edits preserved → taps back → confirms → edits discarded

**9.7. Create Integration Test for Error Handling**
- Create `integration_test/pdf_editor_error_handling_test.dart`
- Test: simulate save failure → verify error message → verify original file unchanged → verify unsaved changes preserved

#### 10. Documentation & Finalization

**10.1. Update Architecture Documentation**
- Update `CLAUDE.md` with pdf_editor feature description
- Document new routes in navigation section
- Document new dependencies

**10.2. Create Feature README**
- Create `lib/features/pdf_editor/README.md`
- Document feature architecture
- Document use cases and their purposes
- Document testing approach

**10.3. Add Code Comments**
- Add documentation comments to all public classes and methods
- Add inline comments for complex logic (e.g., PDF manipulation, coordinate calculations)

**10.4. Update pubspec.yaml**
- Ensure `syncfusion_flutter_pdf` is added with correct version
- Update description if needed

**10.5. Run Full Test Suite**
- Run `flutter test` to verify all unit and widget tests pass
- Run `flutter test integration_test` to verify all integration tests pass
- Verify test coverage meets project standards

**10.6. Run Code Quality Checks**
- Run `flutter analyze` and fix any issues
- Run `dart format .` to ensure consistent formatting
- Run `dart run build_runner build --delete-conflicting-outputs` to regenerate code

**10.7. Manual Testing**
- Test on physical Android device
- Test with various PDF files (small, large, with forms, without forms)
- Test all editing tools
- Test save functionality
- Test error scenarios (storage full, invalid PDF, etc.)
- Test navigation flows
- Test back button behavior

**10.8. Create Demo Video/Screenshots**
- Record demo of key features
- Take screenshots for documentation
- Update README with visual examples

**10.9. Final Code Review**
- Review all code for adherence to architecture
- Review all code for adherence to KISS and YAGNI principles
- Review all code for security concerns
- Review all code for performance concerns

**10.10. Mark PRD-004 as Complete**
- Update project tracking
- Document any deferred features or known limitations
- Create tickets for future enhancements

---

## Summary

This implementation plan follows the 3-phase specification-driven TDD workflow:

1. **Phase 1 (Requirements Analysis):** Converted PRD-004 into 43 testable EARS-formatted requirements covering functional and non-functional aspects, identified architectural constraints, and validated testability.

2. **Phase 2 (Specification Creation):** Designed a complete `pdf_editor` feature module following Feature-First Clean Architecture with 11 use cases, 5 entities, repository interface/implementation, data source, 4 notifiers, 2 screens, and 5 widgets. Specified data flow sequences and integration points.

3. **Phase 3 (Implementation Planning):** Created a hierarchical task list with 10 major sections, 60+ subsections, and 200+ individual tasks. Each requirement follows the Red-Green-Refactor TDD cycle with explicit test-first development.

**Key Architectural Decisions:**
- Reuse existing Syncfusion viewer, extend with Syncfusion PDF library for editing
- Save edited PDFs as new files (non-destructive editing)
- Integrate with existing file_discovery for recent documents
- Use Riverpod for all state management and dependency injection
- Use dartz Either for error handling in Domain/Data layers
- Use freezed for all entities and models

**Simplicity Principles Applied:**
- No premature optimization (e.g., no caching, no background processing unless needed)
- Minimal dependencies (only add syncfusion_flutter_pdf)
- Reuse existing patterns and components
- Simple file naming convention ("_edited" suffix)
- Straightforward UI (toolbar with icons, simple dialogs)
- No complex state machines (just edit mode on/off)

**Testing Coverage:**
- Unit tests for all use cases, repositories, data sources
- Widget tests for all screens and widgets
- Integration tests for complete workflows
- Manual testing on physical devices

The plan is ready for execution following the TDD discipline: write failing test → implement minimal code → refactor → repeat.