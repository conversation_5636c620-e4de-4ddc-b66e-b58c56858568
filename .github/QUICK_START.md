# Quick Start Guide for AI Agents

## 🎯 Goal
Complete the Document Reader MVP implementation - 35% remaining (15 tasks)

## 📖 Read These Files First (3 minutes)

1. **README.md** - Environment setup and project overview
2. **CLAUDE.md** - Architecture, commands, and patterns
3. **docs/impl_plan/IMPLEMENTATION_PROGRESS_REPORT.md** - Current status (65% complete)

## 🎯 What's Left to Do

### Priority 1: File Discovery UI (3 tasks - ~5 hours)
- Complete HomeScreen with tabs and document list
- Create document list item widget
- Create file type tabs widget
- Create permission request widget

**Start here:**
```bash
# Read these files to understand the feature
cat lib/features/file_discovery/presentation/screens/home_screen.dart
cat lib/features/file_discovery/presentation/notifiers/document_list_notifier.dart
cat lib/features/file_discovery/presentation/providers/file_discovery_providers.dart

# Then implement the widgets in:
# lib/features/file_discovery/presentation/widgets/
```

### Priority 2: Testing (3 tasks - ~18 hours)
- Unit tests for use cases and repositories
- Widget tests for all screens
- Integration tests for user flows

**Start here:**
```bash
# Look at existing test structure
cat test/widget_test.dart

# Then create tests in:
# test/features/*/domain/usecases/*_test.dart
# test/features/*/data/repositories/*_test.dart
```

### Priority 3: Deployment (3 tasks - ~3 hours)
- ProGuard rules
- Generate release APK/AAB
- Device testing

## 🚀 Quick Commands

```bash
# Install dependencies
flutter pub get

# Generate code (after any changes to @freezed or @riverpod files)
dart run build_runner build --delete-conflicting-outputs

# Check code quality
flutter analyze

# Run tests
flutter test

# Build debug
flutter build apk --debug

# Build release
flutter build apk --release
```

## 📋 Quality Checklist Before Committing

```bash
✅ flutter analyze (0 issues)
✅ flutter test (all pass)
✅ flutter build apk --debug (success)
✅ Update docs/impl_plan/IMPLEMENTATION_PROGRESS_REPORT.md
✅ Git commit with good message
```

## 🎯 Current Sprint: File Discovery UI

**Goal:** Get the home screen fully functional

**Tasks:**
1. Add TabBar with 5 tabs (ALL, PDF, WORD, EXCEL, PPT) to HomeScreen
2. Add ListView.builder that uses DocumentListNotifier
3. Create DocumentListItem widget (shows icon, name, size, date)
4. Create FileTypeTabs widget (handles filtering)
5. Create PermissionRequestWidget (shows when permission denied)
6. Add navigation to document viewers on tap
7. Test the flow manually

**Acceptance:**
- Tapping tabs filters documents
- Tapping document opens correct viewer
- Permission flow works (grant and deny paths)

## 📚 Full Details

See `.github/ISSUE_TEMPLATE/continue-implementation.md` for:
- Complete task breakdown with acceptance criteria
- Detailed implementation guidelines
- Testing patterns and examples
- Common issues and solutions
- Definition of done

## 🆘 Common Issues

**Build runner fails?**
→ Check Riverpod v3 and Freezed v3 are installed (see IMPLEMENTATION_PROGRESS_REPORT.md)

**Freezed errors?**
→ Always use `abstract class` for Freezed classes (see README.md Troubleshooting)

**Tests fail with provider errors?**
→ Wrap in ProviderScope (see test/widget_test.dart example)

---

**Just read the 3 files above, then start with Priority 1 tasks!** 🚀
