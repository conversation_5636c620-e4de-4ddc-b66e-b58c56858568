# Continue MVP Implementation - Remaining Tasks

## 🎯 Objective

Complete the remaining 35% of the Document Reader MVP implementation, focusing on:
1. File Discovery feature completion
2. Comprehensive testing (unit, widget, integration)
3. Deployment preparation

## 📋 Current Status

**Overall Progress:** 65% Complete (30/45 tasks)
**Last Updated:** 2025-10-02
**Blockers:** None - all quality gates passing

### ✅ Completed Phases
- Setup & Configuration (100%)
- Core Layer (100%)
- Permissions Feature (100%)
- Document Viewer Screens (100%)
- Dependency Injection (100%)
- App Integration (100%)

### 🟡 In Progress
- File Discovery Feature (67%)
- Testing (25%)

### ⏳ Pending
- Deployment Preparation (0%)

## 📚 Required Reading (In Order)

### 1. **Start Here - Project Context**
Read these files to understand the project:

1. **README.md** - Environment setup, project overview, architecture summary
2. **CLAUDE.md** - Development guide, common commands, architecture patterns

### 2. **Implementation Plan - Master Reference**
Read these to understand what needs to be done:

1. **docs/impl_plan/251002.claude-plan-mvp-01-1.md** - Complete implementation plan
   - Section 3.2: Detailed task breakdown with acceptance criteria
   - Section 3.3: Testing strategy
   - Section 3.4: Quality gates
   - Look for tasks marked with ⏳ (pending)

2. **docs/impl_plan/IMPLEMENTATION_PROGRESS_REPORT.md** - Current status
   - Shows completed tasks (✅)
   - Shows pending tasks (⏳)
   - Next steps in priority order

3. **docs/impl_plan/IMPLEMENTATION_SESSION_SUMMARY.md** - Development history
   - Session 3 Update section shows latest work
   - Technical decisions and rationale

### 3. **Architecture Understanding**
Read these to understand how to implement:

1. **lib/core/error/failures.dart** - Error handling pattern
2. **lib/core/navigation/app_router.dart** - Navigation structure
3. **lib/features/permissions/** - Example of complete feature implementation
4. **lib/features/document_viewer/presentation/providers/document_viewer_providers.dart** - Provider pattern
5. **lib/features/document_viewer/presentation/notifiers/document_viewer_notifier.dart** - Notifier pattern

### 4. **Testing Examples**
1. **test/widget_test.dart** - Basic widget test with ProviderScope

## 🎯 Remaining Tasks (Priority Order)

### Phase 4: File Discovery Feature (3 tasks remaining)

**Priority: HIGH** - Core feature needed for app functionality

#### Task 4.3.2: Complete HomeScreen UI ⏳
- **File:** `lib/features/file_discovery/presentation/screens/home_screen.dart`
- **Status:** Partially implemented, needs completion
- **Effort:** 2 hours
- **Requirements:**
  - AppBar with "Documents" title ✅ (already implemented)
  - TabBar with 5 tabs (ALL, PDF, WORD, EXCEL, PPT)
  - ListView.builder for documents
  - RefreshIndicator for pull-to-refresh
  - FloatingActionButton for manual file picker
  - State handling with AsyncValue.when()
  - Show PermissionRequestWidget if permission denied
  - Show "No documents found" if empty
- **Acceptance Criteria:**
  - Material Design 3 compliant
  - 60 FPS scrolling performance
  - Proper navigation to viewer screens
  - Widget tests pass
- **Reference Files:**
  - Read: `lib/features/file_discovery/presentation/notifiers/document_list_notifier.dart`
  - Read: `lib/features/file_discovery/presentation/providers/file_discovery_providers.dart`
  - Read: `lib/core/theme/app_theme.dart`

#### Task 4.3.3: Create Home Screen Widgets ⏳
- **Files to create:**
  - `lib/features/file_discovery/presentation/widgets/document_list_item.dart`
  - `lib/features/file_discovery/presentation/widgets/file_type_tabs.dart`
  - `lib/features/file_discovery/presentation/widgets/permission_request_widget.dart`
- **Status:** Not started
- **Effort:** 2 hours
- **Requirements:**
  - **DocumentListItem:** Display icon, name, type, size, date (formatted)
  - **FileTypeTabs:** Handle tab selection and filtering
  - **PermissionRequestWidget:** Match design in plan section 2.6.2
- **Acceptance Criteria:**
  - All widgets have widget tests
  - Consistent styling with app theme
  - Proper tap handling
- **Reference Files:**
  - Read: `lib/core/utils/file_size_formatter.dart`
  - Read: `lib/core/utils/date_formatter.dart`
  - Read: `lib/features/document_viewer/presentation/screens/pdf_viewer_screen.dart` (for UI patterns)

#### Task 4.3.1: Test Document List Integration ⏳
- **Status:** Not started
- **Effort:** 1 hour
- **Requirements:**
  - Manual testing of document scanning
  - Manual testing of file picker
  - Manual testing of document filtering
  - Verify navigation to viewers works
- **Acceptance Criteria:**
  - Permission grant flow works
  - Permission deny + manual picker works
  - All document types detected correctly
  - Filtering by type works
  - Navigation to correct viewer for each type

### Phase 8: Testing (3 tasks remaining)

**Priority: HIGH** - Required before MVP release

#### Task 8.1: Unit Tests ⏳
- **Scope:** Use cases, repositories, utilities
- **Status:** Not started
- **Effort:** 8 hours
- **Requirements:**
  - Test all use cases in `features/*/domain/usecases/`
  - Test all repositories in `features/*/data/repositories/`
  - Test all utilities in `core/utils/`
  - Use mocktail for mocking
  - Coverage target: >80% for business logic
- **Files to Test (minimum):**
  - `lib/features/file_discovery/domain/usecases/scan_documents.dart`
  - `lib/features/file_discovery/domain/usecases/pick_document.dart`
  - `lib/features/file_discovery/data/repositories/document_repository_impl.dart`
  - `lib/features/permissions/domain/usecases/check_storage_permission.dart`
  - `lib/features/permissions/domain/usecases/request_storage_permission.dart`
  - `lib/features/document_viewer/domain/usecases/read_document_content.dart`
  - `lib/core/utils/file_size_formatter.dart`
  - `lib/core/utils/date_formatter.dart`
- **Test File Pattern:**
  ```
  test/features/file_discovery/domain/usecases/scan_documents_test.dart
  test/features/file_discovery/data/repositories/document_repository_impl_test.dart
  test/core/utils/file_size_formatter_test.dart
  ```
- **Acceptance Criteria:**
  - All use cases have tests for success and failure paths
  - All repositories have tests with mocked data sources
  - Edge cases covered (null, empty, errors)
  - All tests pass consistently
  - `flutter test` runs without errors
- **Reference Files:**
  - Read: `test/widget_test.dart` - Example test structure
  - Read: `lib/features/permissions/presentation/notifiers/permission_notifier.dart` - Example to test

#### Task 8.2: Widget Tests ⏳
- **Scope:** All screens and custom widgets
- **Status:** 1 test created (widget_test.dart)
- **Effort:** 6 hours
- **Requirements:**
  - Test all screens in `features/*/presentation/screens/`
  - Test all custom widgets in `features/*/presentation/widgets/`
  - Test state transitions (loading → data → error)
  - Test user interactions (taps, swipes)
- **Files to Test (minimum):**
  - `lib/features/file_discovery/presentation/screens/home_screen.dart`
  - `lib/features/file_discovery/presentation/widgets/document_list_item.dart`
  - `lib/features/file_discovery/presentation/widgets/file_type_tabs.dart`
  - `lib/features/file_discovery/presentation/widgets/permission_request_widget.dart`
  - `lib/features/document_viewer/presentation/screens/pdf_viewer_screen.dart`
  - `lib/features/document_viewer/presentation/screens/docx_viewer_screen.dart`
  - `lib/features/document_viewer/presentation/screens/xlsx_viewer_screen.dart`
  - `lib/features/document_viewer/presentation/screens/pptx_viewer_screen.dart`
- **Test Scenarios:**
  - Loading state renders CircularProgressIndicator
  - Error state displays error message
  - Data state renders content
  - Tap interactions trigger correct callbacks
- **Acceptance Criteria:**
  - All screens have widget tests
  - All custom widgets have tests
  - Tests use ProviderScope with fake providers
  - All tests pass
- **Reference Files:**
  - Read: `test/widget_test.dart` - Example with ProviderScope and fake providers

#### Task 8.3: Integration Tests ⏳
- **Scope:** End-to-end user flows
- **Status:** Not started
- **Effort:** 4 hours
- **File to Create:** `integration_test/app_test.dart`
- **Requirements:**
  - Test permission granted flow
  - Test permission denied + manual selection flow
  - Test document filtering flow
  - Test open and view document flow
- **Test Flows:**
  1. App launch → Grant permission → See document list → Tap document → View opens
  2. App launch → Deny permission → Tap "Select File" → Pick file → View opens
  3. Home screen → Tap PDF tab → See only PDFs → Tap WORD tab → See only DOCX
  4. Home screen → Tap document → PDF viewer opens → Back button → Return to home
- **Acceptance Criteria:**
  - Tests run on emulator/device
  - All critical paths covered
  - Tests are reliable (no flakiness)
  - All tests pass
- **Reference Files:**
  - Read Flutter docs: https://docs.flutter.dev/testing/integration-tests

### Phase 9: Deployment Preparation (3 tasks remaining)

**Priority: MEDIUM** - Can be done after core features work

#### Task 9.2: ProGuard Rules ⏳
- **File to Create:** `android/app/proguard-rules.pro`
- **File to Modify:** `android/app/build.gradle`
- **Status:** Not started
- **Effort:** 30 minutes
- **Requirements:**
  - Keep Syncfusion classes
  - Keep Riverpod generated code
  - Keep Freezed/JSON Serializable classes
  - Keep microsoft_viewer classes
  - Enable minification in build.gradle
- **Rules Template:**
  ```proguard
  # Syncfusion
  -keep class com.syncfusion.** { *; }

  # Riverpod
  -keep class **.*Provider { *; }
  -keep class **.*Notifier { *; }

  # Freezed
  -keep class **.freezed.** { *; }

  # JSON Serializable
  -keepclassmembers class ** {
    @com.google.gson.annotations.SerializedName <fields>;
  }

  # Microsoft Viewer
  -keep class com.microsoft.** { *; }
  ```
- **Acceptance Criteria:**
  - Release build doesn't crash
  - All features work in release mode
  - APK size reasonable (<50MB)
- **Reference Files:**
  - Read: `android/app/build.gradle` - Current build configuration

#### Task 9.3: Generate Release APK/AAB ⏳
- **Status:** Not started
- **Effort:** 15 minutes
- **Commands:**
  ```bash
  flutter build apk --release
  flutter build appbundle --release
  ```
- **Acceptance Criteria:**
  - APK generated successfully
  - AAB generated successfully
  - Files located in `build/app/outputs/`
  - Install APK on device and verify all features work

#### Task 9.4: Device Testing ⏳
- **Status:** Not started
- **Effort:** 2 hours
- **Requirements:**
  - Test on Android 6.0 (API 23) - minimum SDK
  - Test on Android 11+ (API 30+) - scoped storage
  - Test on various screen sizes
- **Test Cases:**
  1. Install APK from unknown sources
  2. Grant/deny permissions
  3. Open various document types
  4. Test performance and stability
  5. Test back navigation
  6. Test app restart (state persistence)
- **Acceptance Criteria:**
  - App works on all tested devices
  - No crashes or ANRs
  - UI scales correctly
  - Performance acceptable

## 🛠️ Implementation Guidelines

### Code Generation

After creating or modifying any file with:
- `@freezed` annotation
- `@riverpod` annotation
- `@JsonSerializable` annotation

Run:
```bash
dart run build_runner build --delete-conflicting-outputs
```

### Error Handling Pattern

Always use Either from dartz:
```dart
// In repository
Future<Either<Failure, SuccessType>> methodName() async {
  try {
    final result = await dataSource.method();
    return Right(result);
  } catch (e) {
    return Left(SomeFailure(e.toString()));
  }
}

// In notifier
final result = await useCase();
result.fold(
  (failure) => throw failure,  // Riverpod converts to AsyncError
  (success) => success,
);
```

### Testing Pattern

Always wrap widgets in ProviderScope for tests:
```dart
testWidgets('Widget renders correctly', (tester) async {
  await tester.pumpWidget(
    ProviderScope(
      overrides: [
        // Override providers with fakes
        someProvider.overrideWith((ref) => FakeImplementation()),
      ],
      child: MaterialApp(home: YourWidget()),
    ),
  );

  expect(find.text('Expected Text'), findsOneWidget);
});
```

### Quality Checks (Run Before Committing)

```bash
# 1. Analyze code
flutter analyze

# 2. Run tests
flutter test

# 3. Build debug
flutter build apk --debug

# 4. Verify all pass before committing
```

## 📊 Progress Tracking

After completing tasks, update these files:

1. **docs/impl_plan/IMPLEMENTATION_PROGRESS_REPORT.md**
   - Change task status from ⏳ to ✅
   - Update completion percentages
   - Update "Last Updated" date

2. **docs/impl_plan/251002.claude-plan-mvp-01-1.md**
   - Mark completed tasks in "Implementation Status" section
   - Update overall progress percentage

3. **Git Commit Messages**
   - Use conventional commits format:
     - `feat:` for new features
     - `test:` for adding tests
     - `fix:` for bug fixes
     - `docs:` for documentation
     - `refactor:` for code refactoring

## 🚨 Common Issues & Solutions

### Issue: Build runner fails with analyzer conflicts
**Solution:** Check versions in pubspec.yaml match those in IMPLEMENTATION_PROGRESS_REPORT.md. We're using Riverpod v3 and Freezed v3.

### Issue: Freezed compilation errors
**Solution:** See README.md Troubleshooting section. Always declare `abstract class` for Freezed classes.

### Issue: Tests fail with provider not found
**Solution:** Wrap test widgets in ProviderScope. See test/widget_test.dart for example.

### Issue: Permission handler not working
**Solution:** Verify AndroidManifest.xml has correct permissions. See docs/impl_plan/251002.claude-plan-mvp-01-1.md section 1.4.

## 📞 Support Resources

- **Implementation Plan:** docs/impl_plan/251002.claude-plan-mvp-01-1.md
- **Progress Report:** docs/impl_plan/IMPLEMENTATION_PROGRESS_REPORT.md
- **Architecture Guide:** CLAUDE.md
- **Dependency Conflicts:** docs/impl_plan/DEPENDENCY_CONFLICT_RESOLUTION.md

## ✅ Definition of Done

A task is complete when:
1. ✅ Code implemented per acceptance criteria
2. ✅ Tests written and passing
3. ✅ `flutter analyze` shows 0 issues
4. ✅ Debug build successful
5. ✅ Documentation updated (progress reports)
6. ✅ Git commit with descriptive message
7. ✅ Code reviewed (if applicable)

## 🎯 Success Criteria for MVP

The MVP is ready for release when:
1. ✅ All 45 tasks completed (100%)
2. ✅ All tests passing (unit + widget + integration)
3. ✅ Flutter analyze: 0 issues
4. ✅ Release APK/AAB generated successfully
5. ✅ Manual testing on devices successful
6. ✅ No known critical bugs
7. ✅ All documentation updated

**Estimated time to completion:** ~42 hours of focused development

---

**Good luck! Follow the reading order, implement tasks in priority order, and update progress as you go.** 🚀
