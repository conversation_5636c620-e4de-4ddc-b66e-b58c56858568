---
name: PRD-004 PDF Editor & Annotation Toolkit
about: Implement PRD-004 per implementation plan with TDD and Feature-First Clean Architecture
title: "PRD-004: PDF Editor & Annotation Toolkit — Implement per docs/impl_plan/251003.4.prd-004.md"
labels: feature, TDD
assignees: ''
---

## Summary
Implement PRD-004 (PDF Editor & Annotation Toolkit) following the implementation plan and TDD, strictly adhering to the project’s Feature-First Clean Architecture with Riverpod DI.

## Scope
- Annotation tools: highlight, underline, strikethrough
- Signature tool: create, save, place signature; flatten on save
- Add watermark: text watermark across pages
- Add page numbers: bottom positions, simple formats
- Form filling: text fields, checkboxes, dropdowns
- Save edited copy with `_edited` suffix; integrate with recents

## Plans & Specs
- Implementation Plan: `docs/impl_plan/251003.4.prd-004.md`
- Dependency Conflicts: `docs/impl_plan/DEPENDENCY_CONFLICT_RESOLUTION.md`

## Architecture & Guidelines
- Project context and architecture: `CLAUDE.md`
- Developer guidelines (Feature-First Clean Architecture, Riverpod): `docs/agents/dev.md`
- Troubleshooting and repo overview: `README.md`

## Environment (Terminal-Only)
If your environment does not have Flutter/Dart/Android SDK yet, follow the setup steps in `CLAUDE.md` (Environment Setup section). Agents work via terminal/shell; Android Studio or VS Code are NOT required.

## Process & Reporting
- Maintain progress documentation in:
  - `docs/impl_plan/prd-004/IMPLEMENTATION_PROGRESS_REPORT.md` with the same layout in `docs/impl_plan/IMPLEMENTATION_PROGRESS_REPORT.md`
  - `docs/impl_plan/prd-004/IMPLEMENTATION_SESSION_SUMMARY.md` with the same layout in `docs/impl_plan/IMPLEMENTATION_SESSION_SUMMARY.md`
- Record and maintain all dependency issues/resolutions in:
  - `docs/impl_plan/DEPENDENCY_CONFLICT_RESOLUTION.md`
- Follow TDD (Red → Green → Refactor) aligned with the Implementation Plan.

## Acceptance Criteria
- Meets all EARS requirements defined in `docs/impl_plan/251003.4.prd-004.md`
- All unit/widget tests added and passing; CI green
- Edit mode accessible from viewer; editing toolbar visible and functional
- Annotations (highlight/underline/strikethrough) applied and editable; signature creation/placement works
- Watermark and page numbers applied across pages with basic formatting
- Form fields detected and values persisted on save
- Save creates new file with `_edited` suffix in Downloads; recents list updated; user-friendly errors

## Tasks (High-Level)
- Scaffold feature module `pdf_editor/` with domain/data/presentation
- Implement use cases, repository, and datasource (Syncfusion-backed)
- Implement notifiers and UI: editing toolbar, signature pad, dialogs
- Wire DI with Riverpod providers; integrate entry from viewer (Edit button) and routes
- Add tests (Red → Green → Refactor per requirement)
- Update progress and session summary docs after each session/milestone
- Update dependency conflict doc if conflicts are discovered/resolved

## Notes
- Open decisions to align with reviewer:
  - Syncfusion PDF API usage details and licensing confirmation
  - Exact save location and permissions (Downloads vs app directory)
  - Toolbar layout and UX for active tool state

## Links
- PRD: `docs/mvp/251003.pdf-manipulating.md#prd-004-pdf-editor--annotation-toolkit`

