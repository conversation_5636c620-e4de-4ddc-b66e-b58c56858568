---
name: PRD-001 Core App Enhancements
about: Implement PRD-001 per implementation plan with TDD and Feature-First Clean Architecture
title: "PRD-001: Core App Enhancements — Implement per docs/impl_plan/251003.1.prd-001.md"
labels: feature, TDD
assignees: ''
---

## Summary
Implement PRD-001 (Core App Enhancements) following the implementation plan and the TDD plan, strictly adhering to the project’s Feature-First Clean Architecture with Riverpod DI.

## Scope
- Discover tab and tools list
- PDF password protection (set/remove; validate; encrypted state)
- File Info screen with metadata
- Enhanced sharing (platform share sheet)

## Plans & Specs
- Implementation Plan: `docs/impl_plan/251003.1.prd-001.md`
- Dependency Conflicts: `docs/impl_plan/DEPENDENCY_CONFLICT_RESOLUTION.md`

## Architecture & Guidelines
- Project context and architecture: `CLAUDE.md`
- Developer guidelines (Feature-First Clean Architecture, Riverpod): `docs/agents/dev.md`
- Troubleshooting and repo overview: `README.md`

## Environment (Terminal-Only)
If your environment does not have Flutter/Dart/Android SDK yet, follow the setup steps in `CLAUDE.md` (Environment Setup section). Agents work via terminal/shell; Android Studio or VS Code are NOT required.

## Process & Reporting
- Maintain progress documentation in:
  - `docs/impl_plan/prd-001/IMPLEMENTATION_PROGRESS_REPORT.md` with the same layout in `docs/impl_plan/IMPLEMENTATION_PROGRESS_REPORT.md`
  - `docs/impl_plan/prd-001/IMPLEMENTATION_SESSION_SUMMARY.md` with the same layout in `docs/impl_plan/IMPLEMENTATION_SESSION_SUMMARY.md`
- Record and maintain all dependency issues/resolutions in:
  - `docs/impl_plan/DEPENDENCY_CONFLICT_RESOLUTION.md`
- Follow TDD (Red → Green → Refactor) per `docs/plans/prd-001-tdd-plan.md`.

## Acceptance Criteria
- **R1 Discover Navigation & UI**
  - A1.0 Bottom navigation renders with Home selected by default and Discover available.
  - A1.1 Tapping `Discover` switches to the Discover route backed by GoRouter.
  - A1.2 Discover screen lists at least four tool cards (Merge, Split, Convert, AI) with icons.
  - A1.3 Empty state copy appears when the tool list is empty.
  - A1.4 Tapping a tool triggers navigation to the feature route or a "Coming soon" sheet.
- **R2 PDF Password Security**
  - A2.1 "Add Password" flow prompts for password + confirmation on unprotected PDFs.
  - A2.2 Successful submission encrypts the PDF (per D2 decision: in-place vs `-protected.pdf`).
  - A2.3 Protected files surface a protected indicator and expose remove/change actions.
  - A2.4 Incorrect passwords reject with a user-friendly error and no file changes.
- **R3 File Info**
  - A3.1 File Info screen renders name, path, size, createdAt, modifiedAt, MIME type.
  - A3.2 Author metadata only renders when available.
  - A3.3 Password-protected files show an encrypted indicator.
  - A3.4 Invalid/missing paths surface an error state without crashes.
- **R4 Enhanced Sharing**
  - A4.1 Share sheet launches with the document attachment and accurate MIME.
  - A4.2 Large files share by file reference without loading whole file into memory.
  - A4.3 Canceling share returns to the app cleanly.
- **Quality Gates**
  - All unit/widget/integration tests added and green; CI passing.
  - Routes `/discover` and `/file-info` registered and navigable from existing entry points.
  - Encryption operations never log passwords; UX meets MD3 guidelines.

## Tasks (High-Level)
- **T0 Scaffold & Wiring**
  - T0.1 Create feature modules `discover/`, `pdf_security/`, `file_info/`, `sharing/` with `data/domain/presentation` folders.
  - T0.2 Extend `core/navigation/app_router.dart` with `/discover` and `/file-info` routes and tab scaffold.
  - T0.3 Declare Riverpod providers for repositories/use cases with dependency graph per `docs/agents/dev.md`.
  - T0.4 Ensure shell scaffold supports bottom-nav tab persistence and deep links.
- **R1 Discover (T1.x)**
  - T1.0 RED → GREEN → REFACTOR: widget test for 4 default tools, implement `ToolEntry`, notifier, and `DiscoverScreen`.
  - T1.1 Wire tool-card taps to navigation or "Coming soon" sheet; extract `ToolCard` component.
  - T1.2 Integration test for bottom-nav switching while maintaining Home state.
- **R2 PDF Security (T2.x)**
  - T2.0 Unit tests for `SetPdfPassword` success/failure; implement use case.
  - T2.1 Repository tests with mocked datasource; implement `PdfSecurityRepositoryImpl` + datasource.
  - T2.2 Widget test for `PasswordDialog` validation and notifier happy/error paths.
  - T2.3 UI wiring from document list & viewer menus, ensuring post-action refresh.
- **R3 File Info (T3.x)**
  - T3.0 Use case tests for `GetFileMetadata`; implement use case + entity.
  - T3.1 Repository/datasource tests for file system stat + MIME detection.
  - T3.2 Widget test for loading/data/error states of `FileInfoScreen` and notifier logic.
  - T3.3 Navigation wiring from menus to `/file-info?path=...` preserving back stack.
- **R4 Sharing (T4.x)**
  - T4.0 Use case tests for `ShareFile` verifying MIME mapping; implement use case + util.
  - T4.1 Repository tests for platform datasource happy/cancel flows; implement datasource.
  - T4.2 UI tests hooking share actions, verifying progress feedback and recovery.
- **Validation (TV.x)**
  - TV.1 Run `flutter analyze`, unit/widget/integration tests, and regenerate code with build_runner.
  - TV.2 Manual QA: password set/remove, File Info display, share flow, Discover nav.
  - TV.3 Update progress/session summary docs & dependency log after each milestone.

## Testing Strategy
- **Unit Tests:** Cover all use cases (`SetPdfPassword`, `RemovePdfPassword`, `GetFileMetadata`, `ShareFile`, etc.) and repositories/datasources with mocked collaborators.
- **Widget Tests:** Validate Discover grid rendering, Password dialog validation, File Info loading/data/error states, error surfaces on failures.
- **Integration Tests:** Bottom-nav flow, password-protect happy path using fixture, File Info navigation, share invocation with cancellation.
- **Automation:** Execute `dart run build_runner build --delete-conflicting-outputs` whenever Riverpod/Freezed annotations change.

## Notes
- Open decisions to align with reviewer:
  - PDF encryption library choice (AES-256 support, license)
  - In-place vs new protected file (`-protected.pdf`)

## Links
- PRD: `docs/mvp/251003.pdf-manipulating.md#prd-001-core-app-enhancements`
