# Copilot Instructions

## Overview
- Document Reader MVP built with Flutter 3.9+ targeting Android; offline viewer for PDF, DOCX, XLSX, PPTX with Material 3 styling.

## Architecture & Data Flow
- Clean architecture + feature-first layout: see `lib/features/<feature>/{data,domain,presentation}` with shared utilities under `lib/core`.
- UI widgets delegate to Riverpod notifiers, which call domain use cases, then repositories and platform data sources; errors travel as `Failure` objects via `dartz Either`.
- Navigation wires through `lib/core/navigation/app_router.dart` (GoRouter) with routes like `/viewer/pdf/:path?name=...`; keep route params URL-encoded and decoded at the screen level.

## Riverpod & Codegen
- Providers and notifiers use `@riverpod`; models use `@freezed`. After editing any annotated file, run `dart run build_runner build --delete-conflicting-outputs` (or `watch`) to refresh the generated `.g.dart` / `.freezed.dart` artifacts.
- Notifiers throw `Failure` instances so `AsyncValue` surfaces rich errors for `ErrorDisplay`; prefer this pattern over returning `Either` from UI layers.
- Override generated providers in tests with `ProviderScope(overrides: [...])` (see `test/widget_test.dart`) when stubbing platform plugins.

## Document Handling
- Document metadata is defined in `features/file_discovery/domain/entities/document.dart`; keep `DocumentType`, icons (`DocumentListItem`), and filter chips (`FileTypeTabs`) in sync when extending formats.
- Supported extensions live in `core/constants/file_types.dart`; also update `_getDocumentTypeFromPath` and `FileSystemDataSourceImpl.scanDocuments` to recognize new types.
- `DocumentListNotifier` caches the most recent scan and adds picked files to `_recent_documents.json` via `FileSystemDataSourceImpl`; operations should stay fast and guard against missing directories.

## Permission Flow
- Storage permission logic resides in `features/permissions/...`; `PermissionDataSourceImpl` chooses between `Permission.manageExternalStorage` and `Permission.storage` based on SDK via `DeviceInfoPlus`.
- The home screen consults `permissionProvider`; a denied state renders `PermissionRequestWidget` that can either re-request permission or open the manual picker.

## Viewers
- PDFs load through `documentViewerProvider(path)` -> `DocumentViewerNotifier` -> `ReadDocumentContent`, finally rendered by `SfPdfViewer.memory`; retries call `loadDocument` on the notifier.
- DOCX/XLSX/PPTX screens stream raw bytes into `MicrosoftViewer`; `_readFileBytes` throws if the file is missing, so be ready to surface `ErrorDisplay`-style fallbacks.
- `DocumentReaderDataSourceImpl` guards file existence, extension, and caps size at 50 MB—mirror these checks if you add new reader logic.

## UI Conventions
- Theme configuration is centralized in `core/theme/app_theme.dart`; reuse helper widgets from `lib/core/widgets/` (`LoadingIndicator`, `ErrorDisplay`) for consistent look and feel.
- Use `Color.withValues` (already adopted) to match current styling, falling back to `withOpacity` if you hit SDK compatibility issues.

## Workflows
- Fetch deps with `flutter pub get`; regenerate code with `dart run build_runner build --delete-conflicting-outputs`.
- Static analysis & tests: `flutter analyze`, `flutter test`; integration suite requires a device/emulator (`flutter test integration_test/app_test.dart -d emulator-5554`).
- Default run target is the Android emulator (`flutter run -d emulator-5554`), which matches the project’s existing workflow.

## Testing Tips
- When widget testing features that touch platform channels, override providers (e.g., replace `permissionDataSourceProvider` with a fake) to avoid plugin initialization.
- Integration tests expect variability in permission/file state; follow existing assertions that allow optional widgets (`findsWidgets`) to keep runs stable across environments.
