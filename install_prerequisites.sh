#!/bin/bash

# Script to install all prerequisites for Flutter project
# This script installs Git, Java JDK (17 recommended), Android SDK, and Flutter

set -e  # Exit on any error

echo "Installing prerequisites for fs-fast-pdf-reader project..."

# Install basic system dependencies
echo "Installing system dependencies..."
sudo apt-get update
sudo apt-get install -y git unzip curl zip tar libglu1-mesa default-jdk clang cmake ninja-build pkg-config libgtk-3-dev

# Verify Java installation
if command -v java &> /dev/null; then
    JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
    if [[ "$JAVA_VERSION" == *"17."* ]] || [[ "$JAVA_VERSION" == *"21."* ]]; then
        echo "✅ Java JDK is installed (version $JAVA_VERSION) - Compatible"
    else
        echo "⚠️  Java is installed but version $JAVA_VERSION is not optimal for Android development"
        echo "Installing OpenJDK 17 (recommended for Android build tools)..."
        sudo apt-get install -y openjdk-17-jdk
    fi
else
    echo "Installing OpenJDK 17 (recommended for Android development)..."
    sudo apt-get install -y openjdk-17-jdk
fi

# Install Google Chrome for web development
if ! command -v google-chrome &> /dev/null; then
    echo "Installing Google Chrome..."
    wget https://dl.google.com/linux/direct/google-chrome-stable_current_amd64.deb
    sudo dpkg -i google-chrome-stable_current_amd64.deb || sudo apt-get -f install -y
    rm google-chrome-stable_current_amd64.deb
else
    echo "✅ Google Chrome is already installed."
fi


# Set up Android SDK
echo "Setting up Android SDK..."

# Create SDK directory structure
ANDROID_DIR="$HOME/Android"
SDK_DIR="$ANDROID_DIR/Sdk"
mkdir -p "$SDK_DIR/cmdline-tools/latest"

# Download and extract Android command-line tools
if [ ! -f "$SDK_DIR/cmdline-tools/latest/bin/sdkmanager" ]; then
    echo "Downloading Android command-line tools..."
    cd "$SDK_DIR/cmdline-tools"
    wget https://dl.google.com/android/repository/commandlinetools-linux-11076708_latest.zip -O tools.zip
    unzip -o tools.zip -d latest
    rm tools.zip
else
    echo "✅ Android command-line tools are already installed."
fi


# Set environment variables
export ANDROID_SDK_ROOT="$SDK_DIR"
export ANDROID_HOME="$SDK_DIR"
export PATH="$ANDROID_SDK_ROOT/platform-tools:$ANDROID_SDK_ROOT/emulator:$ANDROID_SDK_ROOT/cmdline-tools/latest/bin:$PATH"

# Add environment variables to shell profile for persistence
if ! grep -q "# Android SDK" ~/.bashrc; then
    echo "" >> ~/.bashrc
    echo "# Android SDK" >> ~/.bashrc
    echo "export ANDROID_SDK_ROOT=$ANDROID_DIR/Sdk" >> ~/.bashrc
    echo "export ANDROID_HOME=\\$ANDROID_SDK_ROOT" >> ~/.bashrc
    echo 'export PATH="$ANDROID_SDK_ROOT/platform-tools:$ANDROID_SDK_ROOT/emulator:$ANDROID_SDK_ROOT/cmdline-tools/latest/bin:$PATH"' >> ~/.bashrc
fi


# Install required Android packages
echo "Installing required Android packages..."
yes | "$ANDROID_SDK_ROOT/cmdline-tools/latest/bin/sdkmanager" --licenses
"$ANDROID_SDK_ROOT/cmdline-tools/latest/bin/sdkmanager" \
  "platform-tools" \
  "platforms;android-34" \
  "build-tools;34.0.0"

# Install Flutter SDK
echo "Installing Flutter SDK..."
cd "$HOME"

if [ ! -d "flutter" ]; then
    # Download Flutter
    FLUTTER_VERSION="3.24.0"  # Using a recent stable version
    wget "https://storage.googleapis.com/flutter_infra_release/releases/stable/linux/flutter_linux_$FLUTTER_VERSION-stable.tar.xz" -O flutter.tar.xz
    tar xf flutter.tar.xz
    rm flutter.tar.xz
else
    echo "✅ Flutter SDK is already installed."
fi


# Add Flutter to PATH
export PATH="$HOME/flutter/bin:$PATH"

# Add Flutter to shell profile for persistence
if ! grep -q "# Flutter" ~/.bashrc; then
    echo "" >> ~/.bashrc
    echo "# Flutter" >> ~/.bashrc
    echo 'export PATH="$HOME/flutter/bin:$PATH"' >> ~/.bashrc
fi


# Accept Android licenses
echo "Accepting Android licenses..."
$HOME/flutter/bin/flutter doctor --android-licenses || true

echo "Flutter installation completed!"
echo "Please run: source ~/.bashrc"
echo "Then run: flutter doctor to verify the installation"