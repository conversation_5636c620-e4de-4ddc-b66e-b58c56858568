// features/permissions/data/datasources/permission_datasource.dart

import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:permission_handler/permission_handler.dart';

abstract class PermissionDataSource {
  /// Checks the current storage permission status
  Future<PermissionStatus> checkStoragePermission();

  /// Requests storage permission from the user
  Future<PermissionStatus> requestStoragePermission();
}

class PermissionDataSourceImpl implements PermissionDataSource {
  PermissionDataSourceImpl({DeviceInfoPlugin? deviceInfo})
      : _deviceInfo = deviceInfo ?? DeviceInfoPlugin();

  final DeviceInfoPlugin _deviceInfo;

  Future<int?> _getAndroidSdkInt() async {
    if (!Platform.isAndroid) return null;
    try {
      final androidInfo = await _deviceInfo.androidInfo;
      return androidInfo.version.sdkInt;
    } catch (_) {
      return null;
    }
  }

  @override
  Future<PermissionStatus> checkStoragePermission() async {
    if (!Platform.isAndroid) {
      // Non-Android platforms do not expose MANAGE_EXTERNAL_STORAGE; treat storage as available.
      return PermissionStatus.granted;
    }

    final sdkInt = await _getAndroidSdkInt();

    if (sdkInt != null && sdkInt >= 30) {
      final manageStatus = await Permission.manageExternalStorage.status;
      if (manageStatus.isGranted) {
        return PermissionStatus.granted;
      }

      final storageStatus = await Permission.storage.status;
      if (storageStatus.isGranted) {
        return PermissionStatus.granted;
      }

      return manageStatus;
    }

    try {
      final storageStatus = await Permission.storage.status;
      if (storageStatus.isGranted) {
        return PermissionStatus.granted;
      }
      return storageStatus;
    } catch (_) {
      return PermissionStatus.denied;
    }
  }

  @override
  Future<PermissionStatus> requestStoragePermission() async {
    if (!Platform.isAndroid) {
      // No runtime storage permission needed on non-Android platforms.
      return PermissionStatus.granted;
    }

    final sdkInt = await _getAndroidSdkInt();

    if (sdkInt != null && sdkInt >= 30) {
      final manageResult = await Permission.manageExternalStorage.request();
      if (manageResult.isGranted) {
        return PermissionStatus.granted;
      }
      return manageResult;
    }

    try {
      final storageResult = await Permission.storage.request();
      if (storageResult.isGranted) {
        return PermissionStatus.granted;
      }
      return storageResult;
    } catch (_) {
      return PermissionStatus.denied;
    }
  }
}
