// features/permissions/data/repositories/permission_repository_impl.dart

import 'package:dartz/dartz.dart';
import 'package:permission_handler/permission_handler.dart';
import '../datasources/permission_datasource.dart';
import '../../domain/repositories/permission_repository.dart';
import '../../../../core/error/failures.dart';

class PermissionRepositoryImpl implements PermissionRepository {
  final PermissionDataSource dataSource;

  PermissionRepositoryImpl(this.dataSource);

  @override
  Future<Either<Failure, PermissionStatus>> checkStoragePermission() async {
    try {
      final result = await dataSource.checkStoragePermission();
      return Right(result);
    } catch (e) {
      return Left(StoragePermissionFailure('Failed to check storage permission: $e'));
    }
  }

  @override
  Future<Either<Failure, PermissionStatus>> requestStoragePermission() async {
    try {
      final result = await dataSource.requestStoragePermission();
      return Right(result);
    } catch (e) {
      return Left(StoragePermissionFailure('Failed to request storage permission: $e'));
    }
  }
}