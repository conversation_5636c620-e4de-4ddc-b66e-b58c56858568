// features/permissions/presentation/notifiers/permission_notifier.dart

import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../providers/permission_providers.dart';

part 'permission_notifier.g.dart';

@riverpod
class PermissionNotifier extends _$PermissionNotifier {
  static bool? lastKnownPermission;

  @override
  Future<bool> build() async {
    final checkStoragePermission = ref.read(checkStoragePermissionProvider);
    final result = await checkStoragePermission();
    
    return result.fold(
      (failure) => throw failure,
      (hasPermission) {
        lastKnownPermission = hasPermission;
        return hasPermission;
      },
    );
  }

  Future<bool> checkPermission() async {
    final checkStoragePermission = ref.read(checkStoragePermissionProvider);
    final result = await checkStoragePermission();
    
    state = await AsyncValue.guard(() async {
      return result.fold(
        (failure) => throw failure,
        (hasPermission) => hasPermission,
      );
    });
    
    lastKnownPermission = state.value;
    return state.value ?? false;
  }

  Future<bool> requestPermission() async {
    final requestStoragePermission = ref.read(requestStoragePermissionProvider);
    final result = await requestStoragePermission();
    
    state = await AsyncValue.guard(() async {
      return result.fold(
        (failure) => throw failure,
        (hasPermission) => hasPermission,
      );
    });
    
    lastKnownPermission = state.value;
    return state.value ?? false;
  }

  bool get hasPermission => state.maybeWhen(
        data: (permission) => permission,
        orElse: () => false,
      );

  bool get isPermissionDenied => state.maybeWhen(
        data: (permission) => !permission,
        orElse: () => false,
      );
}