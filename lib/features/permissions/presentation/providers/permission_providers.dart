// features/permissions/presentation/providers/permission_providers.dart

import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../data/datasources/permission_datasource.dart';
import '../../data/repositories/permission_repository_impl.dart';
import '../../domain/repositories/permission_repository.dart';
import '../../domain/usecases/check_storage_permission.dart';
import '../../domain/usecases/request_storage_permission.dart';

part 'permission_providers.g.dart';

@riverpod
PermissionDataSource permissionDataSource(Ref ref) {
  return PermissionDataSourceImpl();
}

@riverpod
PermissionRepository permissionRepository(Ref ref) {
  final dataSource = ref.watch(permissionDataSourceProvider);
  return PermissionRepositoryImpl(dataSource);
}

@riverpod
CheckStoragePermission checkStoragePermission(Ref ref) {
  final repository = ref.watch(permissionRepositoryProvider);
  return CheckStoragePermission(repository);
}

@riverpod
RequestStoragePermission requestStoragePermission(Ref ref) {
  final repository = ref.watch(permissionRepositoryProvider);
  return RequestStoragePermission(repository);
}