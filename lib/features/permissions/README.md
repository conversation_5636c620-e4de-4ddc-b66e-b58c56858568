# Permissions

Overview
- Handle storage permission required for scanning the device for documents. The app still works via manual picker when permission is denied.

Structure
- presentation/notifiers: `PermissionNotifier` tracks status and requests
- presentation/providers: Riverpod providers for permission logic
- domain/usecases: check_storage_permission, request_storage_permission
- domain/repositories: `PermissionRepository` interface
- data/datasources: platform permission calls
- data/repositories: `PermissionRepositoryImpl`

Key Files
- presentation/notifiers/permission_notifier.dart: State + triggers
- presentation/providers/permission_providers.dart: DI for permission services
- domain/usecases/*.dart: Check/request actions
- data/datasources/permission_datasource.dart: Platform layer
- data/repositories/permission_repository_impl.dart: Repo implementation

App Flow
- On discovery, check permission; if denied, show request UI from discovery feature.
- If granted, proceed to file scanning; otherwise offer manual picker path.

