// features/permissions/domain/usecases/request_storage_permission.dart

import 'package:dartz/dartz.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../../../core/error/failures.dart';
import '../repositories/permission_repository.dart';

class RequestStoragePermission {
  final PermissionRepository repository;

  RequestStoragePermission(this.repository);

  Future<Either<Failure, bool>> call() async {
    final result = await repository.requestStoragePermission();
    return result.fold(
      (failure) => Left(failure),
      (status) => Right(status.isGranted),
    );
  }
}