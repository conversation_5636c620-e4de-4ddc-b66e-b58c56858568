// features/permissions/domain/repositories/permission_repository.dart

import 'package:dartz/dartz.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../../../core/error/failures.dart';

abstract class PermissionRepository {
  /// Checks the current storage permission status
  Future<Either<Failure, PermissionStatus>> checkStoragePermission();

  /// Requests storage permission from the user
  Future<Either<Failure, PermissionStatus>> requestStoragePermission();
}