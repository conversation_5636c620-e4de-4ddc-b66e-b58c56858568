// features/document_viewer/presentation/notifiers/document_viewer_notifier.dart

import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:path/path.dart' as p;
import '../../domain/entities/document_content.dart';
import '../providers/document_viewer_providers.dart';

part 'document_viewer_notifier.g.dart';

@riverpod
class DocumentViewerNotifier extends _$DocumentViewerNotifier {
  @override
  Future<DocumentContent> build(String documentPath) async {
    // For PDFs we render via file path; avoid loading full bytes into memory.
    final ext = p.extension(documentPath).toLowerCase();
    if (ext == '.pdf') {
      return Future.value(
        DocumentContent(documentId: documentPath, bytes: const <int>[]),
      );
    }

    final readDocumentContent = ref.read(readDocumentContentProvider);
    final result = await readDocumentContent(documentPath);

    return result.fold(
      (failure) => throw failure,
      (content) => content,
    );
  }

  Future<void> loadDocument(String path) async {
    state = const AsyncValue.loading();
    final ext = p.extension(path).toLowerCase();
    if (ext == '.pdf') {
      state = AsyncValue.data(
        DocumentContent(documentId: path, bytes: const <int>[]),
      );
      return;
    }

    final readDocumentContent = ref.read(readDocumentContentProvider);
    final result = await readDocumentContent(path);

    state = await AsyncValue.guard(() async {
      return result.fold(
        (failure) => throw failure,
        (content) => content,
      );
    });
  }

  DocumentContent? get documentContent => state.maybeWhen(
    data: (content) => content,
    orElse: () => null,
  );

  bool get isLoading => state.isLoading;
  bool get hasError => state.hasError;
  Object? get error => state.error;
}
