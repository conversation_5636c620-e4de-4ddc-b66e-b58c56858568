// features/document_viewer/presentation/screens/pdf_viewer_screen.dart

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import '../../../file_actions/presentation/widgets/document_actions_button.dart';
import '../../../file_discovery/domain/entities/document.dart';
import '../notifiers/document_viewer_notifier.dart';
import '../widgets/document_app_bar.dart';
import '../../../../core/widgets/error_display.dart';
import '../../../../core/widgets/loading_indicator.dart';
import '../../../../core/error/failures.dart';

class PDFViewerScreen extends ConsumerStatefulWidget {
  final String documentPath;
  final String documentName;

  const PDFViewerScreen({
    super.key,
    required this.documentPath,
    required this.documentName,
  });

  @override
  ConsumerState<PDFViewerScreen> createState() => _PDFViewerScreenState();
}

class _PDFViewerScreenState extends ConsumerState<PDFViewerScreen> {
  late PdfViewerController _pdfViewerController;

  @override
  void initState() {
    super.initState();
    _pdfViewerController = PdfViewerController();
  }

  @override
  Widget build(BuildContext context) {
    final documentViewerState = ref.watch(
      documentViewerProvider(widget.documentPath),
    );

    return Scaffold(
      appBar: DocumentAppBar(
        title: widget.documentName,
        onBackPressed: () => Navigator.of(context).pop(),
        actions: [
          DocumentActionsButton(
            filePath: widget.documentPath,
            displayName: widget.documentName,
            documentType: DocumentType.pdf,
            iconColor: Theme.of(context).colorScheme.onPrimary,
          ),
        ],
      ),
      body: documentViewerState.when(
        loading: () => const LoadingIndicator(message: 'Loading PDF...'),
        error: (error, stack) => ErrorDisplay(
          failure: error as Failure,
          onRetry: () {
            ref
                .read(documentViewerProvider(widget.documentPath).notifier)
                .loadDocument(widget.documentPath);
          },
        ),
        data: (content) {
          // Prefer file-based rendering to reduce memory pressure.
          return SfPdfViewer.file(
            File(widget.documentPath),
            key: const Key('pdf_viewer_file'),
            controller: _pdfViewerController,
            canShowScrollHead: true,
            canShowScrollStatus: true,
            enableDoubleTapZooming: true,
            enableTextSelection: false,
            onDocumentLoaded: (details) {},
            onDocumentLoadFailed: (details) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Failed to load PDF: ${details.error}'),
                  backgroundColor: Colors.red,
                ),
              );
            },
          );
        },
      ),
      floatingActionButton: documentViewerState.maybeWhen(
        data: (_) => Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            FloatingActionButton(
              heroTag: "zoom_in",
              mini: true,
              onPressed: () {
                _pdfViewerController.zoomLevel += 0.25;
              },
              child: const Icon(Icons.zoom_in),
            ),
            const SizedBox(height: 8),
            FloatingActionButton(
              heroTag: "zoom_out",
              mini: true,
              onPressed: () {
                if (_pdfViewerController.zoomLevel > 1.0) {
                  _pdfViewerController.zoomLevel -= 0.25;
                }
              },
              child: const Icon(Icons.zoom_out),
            ),
          ],
        ),
        orElse: () => null,
      ),
    );
  }
}
