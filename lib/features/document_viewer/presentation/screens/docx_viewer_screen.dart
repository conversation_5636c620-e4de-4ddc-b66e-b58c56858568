// features/document_viewer/presentation/screens/docx_viewer_screen.dart

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:microsoft_viewer/microsoft_viewer.dart';
import '../../../file_actions/presentation/widgets/document_actions_button.dart';
import '../../../file_discovery/domain/entities/document.dart';

class DOCXViewerScreen extends StatelessWidget {
  final String documentPath;
  final String documentName;

  const DOCXViewerScreen({
    super.key,
    required this.documentPath,
    required this.documentName,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(documentName, overflow: TextOverflow.ellipsis),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          DocumentActionsButton(
            filePath: documentPath,
            displayName: documentName,
            documentType: DocumentType.docx,
            iconColor: Theme.of(context).colorScheme.onPrimary,
          ),
        ],
      ),
      body: FutureBuilder<List<int>>(
        future: _readFileBytes(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          if (snapshot.hasError) {
            return Center(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Theme.of(context).colorScheme.error,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Error loading document',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    SelectableText(
                      snapshot.error.toString(),
                      style: Theme.of(context).textTheme.bodyMedium,
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            );
          }

          if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return const Center(child: Text('No document data'));
          }

          // MicrosoftViewer(fileBytes, fixedHeight)
          // fixedHeight=false allows dynamic height for scrolling
          return MicrosoftViewer(snapshot.data!, false);
        },
      ),
    );
  }

  Future<List<int>> _readFileBytes() async {
    final file = File(documentPath);
    if (!await file.exists()) {
      throw Exception('File not found: $documentPath');
    }
    return await file.readAsBytes();
  }
}
