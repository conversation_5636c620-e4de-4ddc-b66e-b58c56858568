// features/document_viewer/presentation/providers/document_viewer_providers.dart

import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../data/datasources/document_reader_datasource.dart';
import '../../data/repositories/document_reader_repository_impl.dart';
import '../../domain/repositories/document_reader_repository.dart';
import '../../domain/usecases/read_document_content.dart';

part 'document_viewer_providers.g.dart';

// Data source provider
@riverpod
DocumentReaderDataSource documentReaderDataSource(Ref ref) {
  return DocumentReaderDataSourceImpl();
}

// Repository provider
@riverpod
DocumentReaderRepository documentReaderRepository(Ref ref) {
  final dataSource = ref.watch(documentReaderDataSourceProvider);
  return DocumentReaderRepositoryImpl(dataSource);
}

// Use case provider
@riverpod
ReadDocumentContent readDocumentContent(Ref ref) {
  final repository = ref.watch(documentReaderRepositoryProvider);
  return ReadDocumentContent(repository);
}

// Notifier provider is generated in document_viewer_notifier.g.dart as documentViewerProvider