// features/document_viewer/data/repositories/document_reader_repository_impl.dart

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../domain/entities/document_content.dart';
import '../../domain/repositories/document_reader_repository.dart';
import '../datasources/document_reader_datasource.dart';

class DocumentReaderRepositoryImpl implements DocumentReaderRepository {
  final DocumentReaderDataSource dataSource;

  DocumentReaderRepositoryImpl(this.dataSource);

  @override
  Future<Either<Failure, DocumentContent>> readDocument(String path) async {
    try {
      final bytes = await dataSource.readDocument(path);
      final content = DocumentContent(
        documentId: path,
        bytes: bytes,
      );
      return Right(content);
    } catch (e) {
      if (e.toString().contains('does not exist')) {
        return Left(FileNotFoundFailure(path));
      } else if (e.toString().contains('too large')) {
        return Left(FileReadFailure(e.toString()));
      } else if (e.toString().contains('Unsupported file type')) {
        return Left(UnsupportedFileFormatFailure(e.toString()));
      } else {
        return Left(FileReadFailure('Failed to read document: $e'));
      }
    }
  }
}