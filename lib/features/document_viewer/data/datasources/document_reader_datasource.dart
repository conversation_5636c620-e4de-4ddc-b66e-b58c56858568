// features/document_viewer/data/datasources/document_reader_datasource.dart

import 'dart:io';
import 'package:path/path.dart' as path;

abstract class DocumentReaderDataSource {
  /// Reads the content of a document file
  Future<List<int>> readDocument(String documentPath);
}

class DocumentReaderDataSourceImpl implements DocumentReaderDataSource {
  @override
  Future<List<int>> readDocument(String documentPath) async {
    final file = File(documentPath);
    
    // Validate file exists
    if (!await file.exists()) {
      throw Exception('File does not exist: $documentPath');
    }
    
    // Check file size (MVP: limit to 50MB)
    final stat = await file.stat();
    if (stat.size > 50 * 1024 * 1024) { // 50MB
      throw Exception('File too large (>50MB): ${stat.size} bytes');
    }
    
    // Validate file extension
    final extension = path.extension(documentPath).toLowerCase();
    if (!['.pdf', '.docx', '.xlsx', '.pptx'].contains(extension)) {
      throw Exception('Unsupported file type: $extension');
    }
    
    // Read the file content
    return await file.readAsBytes();
  }
}