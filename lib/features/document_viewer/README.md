# Document Viewer

Overview
- Rendering and viewing of supported document types: PDF, DOCX, XLSX, PPTX.
- Provides dedicated screens per format and repository abstraction for reading content.

Structure
- presentation/screens: `*_viewer_screen.dart` per format
- presentation/widgets: shared viewer UI (app bar, etc.)
- presentation/notifiers: `DocumentViewerNotifier` state for loading/display
- presentation/providers: Riverpod providers for dependencies
- domain/entities: `DocumentContent` abstraction for content
- domain/repositories: `DocumentReaderRepository` interface
- domain/usecases: `ReadDocumentContent` orchestrates reading file
- data/datasources: platform-specific reading implementation
- data/repositories: `DocumentReaderRepositoryImpl` binds data source

Key Files
- presentation/screens/pdf_viewer_screen.dart: PDF rendering
- presentation/screens/docx_viewer_screen.dart: DOCX rendering
- presentation/screens/xlsx_viewer_screen.dart: XLSX rendering
- presentation/screens/pptx_viewer_screen.dart: PPTX rendering
- presentation/widgets/document_app_bar.dart: Common app bar/actions
- presentation/notifiers/document_viewer_notifier.dart: Loading/error/content state
- domain/usecases/read_document_content.dart: Reads and prepares content
- data/datasources/document_reader_datasource.dart: Low-level file reading
- data/repositories/document_reader_repository_impl.dart: Repo implementation

App Flow
- Navigation is configured in `lib/core/navigation/app_router.dart` with routes like `/viewer/pdf/:path` and query `name`.
- Viewer screen decodes path/name, loads content via `ReadDocumentContent`, and renders.
- PDF uses Syncfusion PDF viewer; Office formats use the configured Office viewer package.

