// features/document_viewer/domain/usecases/read_document_content.dart

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/document_content.dart';
import '../repositories/document_reader_repository.dart';

class ReadDocumentContent {
  final DocumentReaderRepository repository;

  ReadDocumentContent(this.repository);

  Future<Either<Failure, DocumentContent>> call(String path) async {
    return await repository.readDocument(path);
  }
}