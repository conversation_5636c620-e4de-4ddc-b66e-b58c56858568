# File Actions

Overview
- UI components (button + bottom sheet) to present available actions for a selected document (e.g., open info, share, security, page tools).

Structure
- presentation/widgets: `DocumentActionsButton`, `DocumentActionsSheet`

Key Files
- presentation/widgets/document_actions_button.dart: Triggers the action sheet
- presentation/widgets/document_actions_sheet.dart: Shows available actions and routes

App Flow
- List items or viewer screens include `DocumentActionsButton`.
- Tapping opens `DocumentActionsSheet` with context-specific actions.
- Actions navigate to other feature flows (file info, sharing, page management, security).

