// features/file_actions/presentation/widgets/document_actions_button.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../file_discovery/domain/entities/document.dart';
import 'document_actions_sheet.dart';

class DocumentActionsButton extends ConsumerWidget {
  final String filePath;
  final String displayName;
  final DocumentType documentType;
  final Color? iconColor;

  const DocumentActionsButton({
    super.key,
    required this.filePath,
    required this.displayName,
    required this.documentType,
    this.iconColor,
  });

  factory DocumentActionsButton.fromDocument(
    Document document, {
    Key? key,
    Color? iconColor,
  }) {
    return DocumentActionsButton(
      key: key,
      filePath: document.path,
      displayName: document.name,
      documentType: document.type,
      iconColor: iconColor,
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return IconButton(
      icon: Icon(Icons.more_vert, color: iconColor),
      tooltip: 'More actions',
      onPressed: () {
        showModalBottomSheet<void>(
          context: context,
          isScrollControlled: true,
          builder: (bottomSheetContext) => DocumentActionsSheet(
            filePath: filePath,
            displayName: displayName,
            documentType: documentType,
          ),
        );
      },
    );
  }
}
