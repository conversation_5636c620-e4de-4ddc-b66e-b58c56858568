// features/file_actions/presentation/widgets/document_actions_sheet.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../file_discovery/domain/entities/document.dart';
import '../../../file_discovery/presentation/notifiers/document_list_notifier.dart';
import '../../../pdf_security/presentation/notifiers/pdf_security_notifier.dart';
import '../../../pdf_security/presentation/widgets/password_dialog.dart';
import '../../../sharing/presentation/notifiers/sharing_notifier.dart';
import '../../../../core/error/failures.dart';

class DocumentActionsSheet extends ConsumerStatefulWidget {
  final String filePath;
  final String displayName;
  final DocumentType documentType;

  const DocumentActionsSheet({
    super.key,
    required this.filePath,
    required this.displayName,
    required this.documentType,
  });

  @override
  ConsumerState<DocumentActionsSheet> createState() =>
      _DocumentActionsSheetState();
}

class _DocumentActionsSheetState extends ConsumerState<DocumentActionsSheet> {
  bool _isEncrypted = false;
  bool _isLoadingEncryption = false;
  Failure? _encryptionFailure;

  @override
  void initState() {
    super.initState();
    _loadEncryptionStatus();
  }

  @override
  Widget build(BuildContext context) {
    final isPdf = widget.documentType == DocumentType.pdf;

    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.displayName,
              style: Theme.of(context).textTheme.titleMedium,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            Text(
              widget.filePath,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const Divider(height: 24),
            if (isPdf)
              if (_isLoadingEncryption)
                const ListTile(
                  leading: SizedBox(
                    height: 24,
                    width: 24,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                  title: Text('Checking security status...'),
                )
              else if (_encryptionFailure != null)
                ListTile(
                  leading: const Icon(Icons.lock_outline),
                  title: Text(
                    'Unable to determine security status',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.error,
                    ),
                  ),
                  subtitle: Text(_encryptionFailure!.message),
                  trailing: TextButton(
                    onPressed: _loadEncryptionStatus,
                    child: const Text('Retry'),
                  ),
                )
              else if (_isEncrypted)
                ListTile(
                  leading: const Icon(Icons.lock_open),
                  title: const Text('Remove Password'),
                  subtitle: const Text('Create an unprotected copy'),
                  onTap: _handleRemovePassword,
                )
              else
                ListTile(
                  leading: const Icon(Icons.lock),
                  title: const Text('Add Password'),
                  subtitle: const Text('Protect PDF with a password'),
                  onTap: _handleAddPassword,
                ),
            if (isPdf) const Divider(height: 16),
            ListTile(
              leading: const Icon(Icons.info_outline),
              title: const Text('File Info'),
              onTap: _handleFileInfo,
            ),
            ListTile(
              leading: const Icon(Icons.ios_share),
              title: const Text('Share'),
              onTap: _handleShare,
            ),
            const SizedBox(height: 8),
          ],
        ),
      ),
    );
  }

  Future<void> _handleAddPassword() async {
    final result = await showDialog<bool>(
      context: context,
      builder: (dialogContext) => PasswordDialog(
        filePath: widget.filePath,
        fileName: widget.displayName,
        mode: PasswordDialogMode.setPassword,
      ),
    );

    if (result == true && mounted) {
      ref.invalidate(documentListProvider);
      await _loadEncryptionStatus();
      if (mounted) {
        Navigator.of(context).pop();
      }
    }
  }

  Future<void> _handleRemovePassword() async {
    final result = await showDialog<bool>(
      context: context,
      builder: (dialogContext) => PasswordDialog(
        filePath: widget.filePath,
        fileName: widget.displayName,
        mode: PasswordDialogMode.removePassword,
      ),
    );

    if (result == true && mounted) {
      ref.invalidate(documentListProvider);
      await _loadEncryptionStatus();
      if (mounted) {
        Navigator.of(context).pop();
      }
    }
  }

  Future<void> _handleShare() async {
    try {
      await ref
          .read(sharingProvider.notifier)
          .share(
            path: widget.filePath,
            mime: _resolveMime(widget.documentType),
          );
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (error) {
      if (!mounted) return;
      final failure = _mapShareError(error);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(failure.message),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    }
  }

  void _handleFileInfo() {
    final router = GoRouter.of(context);
    Navigator.of(context).pop();
    router.push('/file-info?path=${Uri.encodeComponent(widget.filePath)}');
  }

  Future<void> _loadEncryptionStatus() async {
    if (widget.documentType != DocumentType.pdf) {
      setState(() {
        _isEncrypted = false;
        _encryptionFailure = null;
        _isLoadingEncryption = false;
      });
      return;
    }

    setState(() {
      _isLoadingEncryption = true;
      _encryptionFailure = null;
    });

    try {
      final encrypted = await ref
          .read(pdfSecurityProvider.notifier)
          .isEncrypted(widget.filePath);
      if (!mounted) return;
      setState(() {
        _isEncrypted = encrypted;
        _isLoadingEncryption = false;
      });
    } catch (error) {
      if (!mounted) return;
      setState(() {
        _encryptionFailure = _mapSecurityError(error);
        _isLoadingEncryption = false;
      });
    }
  }

  Failure _mapSecurityError(Object error) {
    if (error is Failure) {
      return error;
    }
    return PdfEncryptionFailure(error.toString());
  }

  Failure _mapShareError(Object error) {
    if (error is Failure) {
      return error;
    }
    return SharingFailure(error.toString());
  }

  String _resolveMime(DocumentType type) {
    switch (type) {
      case DocumentType.pdf:
        return 'application/pdf';
      case DocumentType.docx:
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      case DocumentType.xlsx:
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      case DocumentType.pptx:
        return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
      case DocumentType.unknown:
        return 'application/octet-stream';
    }
  }
}
