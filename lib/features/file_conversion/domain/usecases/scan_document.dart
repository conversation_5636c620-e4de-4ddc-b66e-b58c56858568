import 'package:dartz/dartz.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/file_conversion/domain/entities/conversion_request.dart';
import 'package:myapp/features/file_conversion/domain/entities/conversion_result.dart';
import 'package:myapp/features/file_conversion/domain/repositories/file_conversion_repository.dart';

class ScanDocumentUseCase {
  final FileConversionRepository repository;
  
  ScanDocumentUseCase(this.repository);

  Future<Either<Failure, List<String>>> call() => repository.captureImages();
  
  // Additional method to coordinate the complete scan flow
  Future<Either<Failure, ConversionResult>> scanAndConvertToPdf({
    String outputFileName = 'scanned.pdf',
    PageOrientation? orientation,
  }) async {
    final capturedImagesResult = await repository.captureImages();
    
    return capturedImagesResult.fold(
      (failure) => Left(failure),
      (imagePaths) async {
        if (imagePaths.isEmpty) {
          // Handle case where no images were captured
          return const Left(ConversionFailure('No images were captured during scanning'));
        }
        
        return await repository.convertImagesToPdf(
          imagePaths,
          outputFileName,
          orientation,
        );
      },
    );
  }
}
