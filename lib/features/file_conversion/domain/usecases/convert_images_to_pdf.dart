import 'package:dartz/dartz.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/file_conversion/domain/entities/conversion_request.dart';
import 'package:myapp/features/file_conversion/domain/entities/conversion_result.dart';
import 'package:myapp/features/file_conversion/domain/repositories/file_conversion_repository.dart';

class ConvertImagesToPdfUseCase {
  final FileConversionRepository repository;
  
  ConvertImagesToPdfUseCase(this.repository);

  Future<Either<Failure, ConversionResult>> call(
    List<String> imagePaths,
    String outputFileName,
    PageOrientation? orientation,
  ) =>
      repository.convertImagesToPdf(imagePaths, outputFileName, orientation);
}
