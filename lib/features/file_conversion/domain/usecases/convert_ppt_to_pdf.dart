import 'package:dartz/dartz.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/file_conversion/domain/entities/conversion_result.dart';
import 'package:myapp/features/file_conversion/domain/repositories/file_conversion_repository.dart';

class ConvertPptToPdfUseCase {
  final FileConversionRepository repository;
  
  ConvertPptToPdfUseCase(this.repository);

  Future<Either<Failure, ConversionResult>> call(
    String pptPath,
    String outputFileName,
  ) =>
      repository.convertPptToPdf(pptPath, outputFileName);
}
