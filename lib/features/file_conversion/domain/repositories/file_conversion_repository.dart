import 'package:dartz/dartz.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/file_conversion/domain/entities/conversion_request.dart';
import 'package:myapp/features/file_conversion/domain/entities/conversion_result.dart';
import 'package:myapp/features/file_conversion/domain/entities/scanned_page.dart';
import 'dart:ui';

abstract class FileConversionRepository {
  // Scanning operations
  Future<Either<Failure, List<String>>> captureImages();
  
  Future<Either<Failure, String>> applyFiltersToImage(
    String imagePath,
    PageFilter filter,
    double rotation,
    Rect? cropRect,
  );

  // Conversion operations
  Future<Either<Failure, ConversionResult>> convertImagesToPdf(
    List<String> imagePaths,
    String outputPath,
    PageOrientation? orientation,
  );

  Future<Either<Failure, ConversionResult>> convertDocxToPdf(
    String docxPath,
    String outputFileName,
  );

  Future<Either<Failure, ConversionResult>> convertPptToPdf(
    String pptPath,
    String outputFileName,
  );

  Future<Either<Failure, ConversionResult>> convertXlsxToPdf(
    String xlsxPath,
    String outputFileName,
  );

  // File operations
  Future<Either<Failure, List<String>>> pickImages();
  
  Future<Either<Failure, String>> pickDocument(List<String> extensions);
}
