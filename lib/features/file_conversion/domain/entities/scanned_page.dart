// ignore_for_file: invalid_annotation_target
import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:ui';

part 'scanned_page.freezed.dart';

enum PageFilter { color, grayscale, blackAndWhite }

@freezed
abstract class ScannedPage with _$ScannedPage {
  const factory ScannedPage({
    required String imagePath,
    required PageFilter filter,
    required double rotationAngle,
    Rect? cropRect,
  }) = _ScannedPage;
}
