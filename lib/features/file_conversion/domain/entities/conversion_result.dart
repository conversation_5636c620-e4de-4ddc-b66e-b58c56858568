// ignore_for_file: invalid_annotation_target
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:myapp/core/utils/file_size_formatter.dart';

part 'conversion_result.freezed.dart';

@freezed
abstract class ConversionResult with _$ConversionResult {
  const factory ConversionResult({
    required String outputPath,
    required String fileName,
    required int fileSize,
    required DateTime createdAt,
  }) = _ConversionResult;
}

extension ConversionResultX on ConversionResult {
  String get formattedSize => FileSizeFormatter.formatFileSize(fileSize);
}
