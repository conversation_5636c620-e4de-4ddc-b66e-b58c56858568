// ignore_for_file: invalid_annotation_target
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:myapp/features/file_conversion/domain/entities/conversion_type.dart';
import 'package:myapp/features/file_conversion/domain/entities/scanned_page.dart';

part 'conversion_request.freezed.dart';

enum PageOrientation { portrait, landscape, auto }

@freezed
abstract class ConversionRequest with _$ConversionRequest {
  const factory ConversionRequest({
    required ConversionType type,
    required List<String> sourcePaths,
    required String outputFileName,
    PageOrientation? orientation,
    List<ScannedPage>? scannedPages,
  }) = _ConversionRequest;
}
