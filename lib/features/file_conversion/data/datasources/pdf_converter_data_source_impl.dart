import 'dart:io';
import 'dart:ui' show Rect;
import 'package:dartz/dartz.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:image/image.dart' as img;
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/file_conversion/data/datasources/pdf_converter_data_source.dart';

class PDFConverterDataSourceImpl implements PDFConverterDataSource {
  @override
  Future<Either<Failure, String>> convertImagesToPdf(
    List<String> imagePaths,
    String outputPath,
    PdfPageFormat? pageFormat,
  ) async {
    try {
      // Validate input
      if (imagePaths.isEmpty) {
        return Left(ConversionFailure('No images provided for PDF conversion'));
      }

      final pdf = pw.Document();

      for (final imagePath in imagePaths) {
        final imageFile = File(imagePath);
        if (!await imageFile.exists()) {
          return Left(ConversionFailure('Image file does not exist: $imagePath'));
        }

        final imageBytes = await imageFile.readAsBytes();
        final decodedImage = img.decodeImage(imageBytes);

        if (decodedImage == null) {
          return Left(ConversionFailure('Could not decode image: $imagePath'));
        }

        final pdfImage = pw.MemoryImage(imageBytes);

        // Calculate page format based on image aspect ratio if auto
        final imageAspectRatio = decodedImage.width / decodedImage.height;
        final pageFormatToUse = pageFormat ??
          (imageAspectRatio > 1 ? PdfPageFormat.a4.landscape : PdfPageFormat.a4);

        pdf.addPage(
          pw.Page(
            pageFormat: pageFormatToUse,
            build: (context) {
              return pw.Center(
                child: pw.Image(
                  pdfImage,
                  fit: pw.BoxFit.contain,
                ),
              );
            },
          ),
        );
      }

      try {
        await Directory(path.dirname(outputPath)).create(recursive: true);
      } catch (e) {
        return Left(ConversionFailure('Failed to create output directory: $e'));
      }

      final outputBytes = await pdf.save();
      await File(outputPath).writeAsBytes(outputBytes);

      // Verify file was created
      final outputFile = File(outputPath);
      if (!await outputFile.exists()) {
        return Left(ConversionFailure('PDF file was not created at: $outputPath'));
      }

      return Right(outputPath);
    } catch (e, stackTrace) {
      return Left(ConversionFailure('Failed to convert images to PDF: $e\n$stackTrace'));
    }
  }

  @override
  Future<Either<Failure, String>> convertDocxToPdf(String docxPath, String outputFileName) async {
    // For now, return a failure since we don't have a proper DOCX to PDF converter
    // In a real implementation, this would use a platform channel, external service, or a Dart package
    return Left(ConversionFailure('DOCX to PDF conversion not implemented yet'));
  }

  @override
  Future<Either<Failure, String>> convertPptToPdf(String pptPath, String outputFileName) async {
    // For now, return a failure since we don't have a proper PPT to PDF converter
    // In a real implementation, this would use a platform channel, external service, or a Dart package
    return Left(ConversionFailure('PPT to PDF conversion not implemented yet'));
  }

  @override
  Future<Either<Failure, String>> convertXlsxToPdf(String xlsxPath, String outputFileName) async {
    // For now, return a failure since we don't have a proper XLSX to PDF converter
    // In a real implementation, this would use a platform channel, external service, or a Dart package
    return Left(ConversionFailure('XLSX to PDF conversion not implemented yet'));
  }

  @override
  Future<Either<Failure, String>> applyImageFilters(
    String imagePath,
    String filterType,
    double rotationAngle,
    Rect? cropRect,
  ) async {
    try {
      // Validate input
      if (imagePath.isEmpty) {
        return Left(ConversionFailure('Image path is empty'));
      }

      final imageFile = File(imagePath);
      if (!await imageFile.exists()) {
        return Left(ConversionFailure('Image file does not exist: $imagePath'));
      }

      final imageBytes = await imageFile.readAsBytes();
      var decodedImage = img.decodeImage(imageBytes);

      if (decodedImage == null) {
        return Left(ConversionFailure('Could not decode image: $imagePath'));
      }

      // Apply rotation (normalize angle and handle 360 degrees)
      final normalizedAngle = rotationAngle % 360;
      if (normalizedAngle != 0.0) {
        try {
          decodedImage = img.copyRotate(decodedImage, angle: normalizedAngle);
        } catch (e) {
          return Left(ConversionFailure('Failed to rotate image: $e'));
        }
      }

      // Apply cropping with validation
      if (cropRect != null) {
        final left = cropRect.left.toInt();
        final top = cropRect.top.toInt();
        final width = cropRect.width.toInt();
        final height = cropRect.height.toInt();

        // Validate crop bounds
        if (left < 0 || top < 0 || width <= 0 || height <= 0) {
          return Left(ConversionFailure('Invalid crop rectangle: negative or zero dimensions'));
        }

        if (left + width > decodedImage.width || top + height > decodedImage.height) {
          return Left(ConversionFailure(
              'Crop rectangle out of bounds: image=${decodedImage.width}x${decodedImage.height}, crop=${left},${top},${width},${height}'));
        }

        try {
          decodedImage = img.copyCrop(
            decodedImage,
            x: left,
            y: top,
            width: width,
            height: height,
          );
        } catch (e) {
          return Left(ConversionFailure('Failed to crop image: $e'));
        }
      }

      // Apply filters
      try {
        switch (filterType) {
          case 'grayscale':
            decodedImage = img.grayscale(decodedImage);
            break;
          case 'blackAndWhite':
            // Apply grayscale first
            decodedImage = img.grayscale(decodedImage);
            // Apply threshold to get clean black and white output using fixed threshold at 128
            decodedImage = _applyThreshold(decodedImage);
            break;
          case 'color':
            // No filter needed, just return the original (possibly rotated/cropped)
            break;
          default:
            // Default to color filter
            break;
        }
      } catch (e) {
        return Left(ConversionFailure('Failed to apply filter ($filterType): $e'));
      }

      // Save the processed image to a temporary location
      try {
        final directory = await getTemporaryDirectory();
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        final outputFileName = '${path.basenameWithoutExtension(imagePath)}_processed_$timestamp.jpg';
        final outputPath = path.join(directory.path, outputFileName);

        // Always save as JPG for consistency
        final encoded = img.encodeJpg(decodedImage, quality: 90);
        await File(outputPath).writeAsBytes(encoded);

        // Verify file was created
        if (!await File(outputPath).exists()) {
          return Left(ConversionFailure('Failed to save processed image to: $outputPath'));
        }

        return Right(outputPath);
      } catch (e) {
        return Left(ConversionFailure('Failed to save processed image: $e'));
      }
    } catch (e, stackTrace) {
      return Left(ConversionFailure('Failed to apply image filters: $e\n$stackTrace'));
    }
  }

  /// Applies threshold to convert grayscale image to clean black and white
  /// Uses a fixed threshold of 128 for simplicity, as Otsu's method implementation
  /// might be too complex for the image package
  img.Image _applyThreshold(img.Image image) {
    final result = img.Image(width: image.width, height: image.height);
    for (int y = 0; y < image.height; y++) {
      for (int x = 0; x < image.width; x++) {
        final pixel = image.getPixel(x, y);
        final r = pixel.r;
        final g = pixel.g;
        final b = pixel.b;
        final grayValue = (r + g + b) ~/ 3;
        
        // Apply fixed threshold of 128 (middle value)
        final newPixel = grayValue > 128
            ? img.ColorRgb8(255, 255, 255) // white
            : img.ColorRgb8(0, 0, 0); // black
        result.setPixel(x, y, newPixel);
      }
    }
    
    return result;
  }
}
