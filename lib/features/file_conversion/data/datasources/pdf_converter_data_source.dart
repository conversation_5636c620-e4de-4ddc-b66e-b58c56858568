import 'dart:ui' show Rect;
import 'package:dartz/dartz.dart';
import 'package:pdf/pdf.dart';
import 'package:myapp/core/error/failures.dart';

abstract class PDFConverterDataSource {
  Future<Either<Failure, String>> convertImagesToPdf(
    List<String> imagePaths,
    String outputPath,
    PdfPageFormat? pageFormat,
  );
  
  Future<Either<Failure, String>> convertDocxToPdf(
    String docxPath,
    String outputFileName,
  );
  
  Future<Either<Failure, String>> convertPptToPdf(
    String pptPath,
    String outputFileName,
  );
  
  Future<Either<Failure, String>> convertXlsxToPdf(
    String xlsxPath,
    String outputFileName,
  );

  Future<Either<Failure, String>> applyImageFilters(
    String imagePath,
    String filterType,
    double rotationAngle,
    Rect? cropRect,
  );
}
