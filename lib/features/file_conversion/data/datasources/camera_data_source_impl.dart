import 'package:dartz/dartz.dart';
import 'package:flutter_doc_scanner/flutter_doc_scanner.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/file_conversion/data/datasources/camera_data_source.dart';

class CameraDataSourceImpl implements CameraDataSource {
  CameraDataSourceImpl({FlutterDocScanner? scanner})
      : _scanner = scanner ?? FlutterDocScanner();

  final FlutterDocScanner _scanner;

  @override
  Future<Either<Failure, List<String>>> captureImages() async {
    try {
      final imagePaths = await _scanner.getScanDocuments();

      if (imagePaths == null || imagePaths.isEmpty) {
        return const Left(UserCancelledFailure());
      }

      return Right(imagePaths);
    } catch (error) {
      return Left(
        CameraFailure('Failed to capture images: ${error.toString()}'),
      );
    }
  }
}
