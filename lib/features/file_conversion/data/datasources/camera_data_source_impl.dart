import 'dart:developer' as developer;
import 'dart:io';
import 'package:dartz/dartz.dart';
import 'package:flutter_doc_scanner/flutter_doc_scanner.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/file_conversion/data/datasources/camera_data_source.dart';

class CameraDataSourceImpl implements CameraDataSource {
  CameraDataSourceImpl({FlutterDocScanner? scanner})
      : _scanner = scanner ?? FlutterDocScanner();

  final FlutterDocScanner _scanner;

  @override
  Future<Either<Failure, List<String>>> captureImages() async {
    try {
      developer.log('Starting document scan...', name: 'CameraDataSource');

      final scanResult = await _scanner.getScanDocuments();

      developer.log('Scan result type: ${scanResult.runtimeType}', name: 'CameraDataSource');
      developer.log('Scan result: $scanResult', name: 'CameraDataSource');

      if (scanResult == null) {
        developer.log('Scan cancelled by user', name: 'CameraDataSource');
        return const Left(UserCancelledFailure());
      }

      // Handle different return types from the plugin
      List<String> imagePaths = [];

      if (scanResult is List) {
        // Expected case: List of strings
        imagePaths = scanResult.cast<String>();
        developer.log('Received List with ${imagePaths.length} items', name: 'CameraDataSource');
      } else if (scanResult is Map) {
        // Handle Map return type (newer plugin versions or platform differences)
        developer.log('Received Map, attempting to extract paths', name: 'CameraDataSource');

        // Try common map keys that might contain the image paths
        final possibleKeys = ['paths', 'images', 'documents', 'files', 'data'];

        for (final key in possibleKeys) {
          if (scanResult.containsKey(key)) {
            final value = scanResult[key];
            if (value is List) {
              imagePaths = value.cast<String>();
              developer.log('Found paths in map key "$key": ${imagePaths.length} items', name: 'CameraDataSource');
              break;
            }
          }
        }

        // If no known keys found, try to extract all string values
        if (imagePaths.isEmpty) {
          imagePaths = scanResult.values
              .where((value) => value is String)
              .cast<String>()
              .toList();
          developer.log('Extracted ${imagePaths.length} string values from map', name: 'CameraDataSource');
        }
      } else if (scanResult is String) {
        // Single file path
        imagePaths = [scanResult];
        developer.log('Received single string path', name: 'CameraDataSource');
      } else {
        developer.log('Unexpected return type: ${scanResult.runtimeType}', name: 'CameraDataSource');
        return Left(CameraFailure('Unexpected scan result type: ${scanResult.runtimeType}'));
      }

      if (imagePaths.isEmpty) {
        developer.log('No image paths found in scan result', name: 'CameraDataSource');
        return const Left(UserCancelledFailure());
      }

      // Validate that all paths exist and are accessible
      final validPaths = <String>[];
      for (final path in imagePaths) {
        developer.log('Checking file existence: $path', name: 'CameraDataSource');

        if (path.isEmpty) {
          developer.log('Skipping empty path', name: 'CameraDataSource');
          continue;
        }

        try {
          final file = File(path);
          if (await file.exists()) {
            validPaths.add(path);
            developer.log('File exists: $path', name: 'CameraDataSource');
          } else {
            developer.log('File does not exist: $path', name: 'CameraDataSource');
          }
        } catch (e) {
          developer.log('Error checking file: $path - $e', name: 'CameraDataSource');
        }
      }

      if (validPaths.isEmpty) {
        developer.log('No valid image files found', name: 'CameraDataSource');
        return const Left(CameraFailure('No valid image files found after scanning'));
      }

      developer.log('Successfully captured ${validPaths.length} images', name: 'CameraDataSource');
      return Right(validPaths);

    } catch (error, stackTrace) {
      developer.log(
        'Failed to capture images: $error',
        name: 'CameraDataSource',
        error: error,
        stackTrace: stackTrace,
      );

      return Left(
        CameraFailure('Failed to capture images: ${error.toString()}'),
      );
    }
  }
}
