import 'dart:developer' as developer;
import 'dart:io';
import 'package:dartz/dartz.dart';
import 'package:flutter_doc_scanner/flutter_doc_scanner.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/file_conversion/data/datasources/camera_data_source.dart';

class CameraDataSourceImpl implements CameraDataSource {
  CameraDataSourceImpl({FlutterDocScanner? scanner})
      : _scanner = scanner ?? FlutterDocScanner();

  final FlutterDocScanner _scanner;

  @override
  Future<Either<Failure, List<String>>> captureImages() async {
    try {
      developer.log('Starting document scan...', name: 'CameraDataSource');

      final scanResult = await _scanner.getScanDocuments();

      developer.log('Scan result type: ${scanResult.runtimeType}', name: 'CameraDataSource');
      developer.log('Scan result: $scanResult', name: 'CameraDataSource');

      if (scanResult == null) {
        developer.log('Scan cancelled by user', name: 'CameraDataSource');
        return const Left(UserCancelledFailure());
      }

      // Handle different return types from the plugin
      List<String> imagePaths = [];
      developer.log('Processing scan result to extract image paths...', name: 'CameraDataSource');

      if (scanResult is List) {
        // Expected case: List of strings
        imagePaths = scanResult.cast<String>();
        developer.log('Received List with ${imagePaths.length} items', name: 'CameraDataSource');
      } else if (scanResult is Map) {
        // Handle Map return type (newer plugin versions or platform differences)
        developer.log('Received Map, attempting to extract paths', name: 'CameraDataSource');

        // Try common map keys that might contain the image paths
        final possibleKeys = ['paths', 'images', 'documents', 'files', 'data'];

        for (final key in possibleKeys) {
          if (scanResult.containsKey(key)) {
            final value = scanResult[key];
            if (value is List) {
              imagePaths = value.cast<String>();
              developer.log('Found paths in map key "$key": ${imagePaths.length} items', name: 'CameraDataSource');
              break;
            }
          }
        }

        // If no known keys found, try to extract all string values
        if (imagePaths.isEmpty) {
          imagePaths = scanResult.values
              .where((value) => value is String)
              .cast<String>()
              .toList();
          developer.log('Extracted ${imagePaths.length} string values from map', name: 'CameraDataSource');
        }
      } else if (scanResult is String) {
        // Single file path
        imagePaths = [scanResult];
        developer.log('Received single string path', name: 'CameraDataSource');
      } else {
        developer.log('Unexpected return type: ${scanResult.runtimeType}', name: 'CameraDataSource');
        return Left(CameraFailure('Unexpected scan result type: ${scanResult.runtimeType}'));
      }

      if (imagePaths.isEmpty) {
        developer.log('No image paths found in scan result', name: 'CameraDataSource');
        return const Left(UserCancelledFailure());
      }

      developer.log('Extracted ${imagePaths.length} image paths from scan result', name: 'CameraDataSource');
      for (int i = 0; i < imagePaths.length; i++) {
        developer.log('Path $i: ${imagePaths[i]}', name: 'CameraDataSource');
      }

      // Validate that paths exist and are accessible with retry logic
      final validPaths = <String>[];
      developer.log('Starting validation of ${imagePaths.length} image paths', name: 'CameraDataSource');

      for (final path in imagePaths) {
        developer.log('Checking file existence: $path', name: 'CameraDataSource');

        if (path.isEmpty) {
          developer.log('Skipping empty path', name: 'CameraDataSource');
          continue;
        }

        try {
          final file = File(path);
          bool fileExists = false;

          // Retry file existence check with delays (files might still be writing)
          for (int attempt = 1; attempt <= 3; attempt++) {
            if (await file.exists()) {
              fileExists = true;
              break;
            }

            if (attempt < 3) {
              developer.log('File not found on attempt $attempt, retrying in ${attempt * 100}ms: $path', name: 'CameraDataSource');
              await Future.delayed(Duration(milliseconds: attempt * 100));
            }
          }

          if (fileExists) {
            // Additional check: ensure file has content (not empty)
            try {
              final fileSize = await file.length();
              if (fileSize > 0) {
                validPaths.add(path);
                developer.log('File exists and has content ($fileSize bytes): $path', name: 'CameraDataSource');
              } else {
                developer.log('File exists but is empty: $path', name: 'CameraDataSource');
              }
            } catch (e) {
              // If we can't check file size, assume it's valid since it exists
              validPaths.add(path);
              developer.log('File exists (size check failed, assuming valid): $path', name: 'CameraDataSource');
            }
          } else {
            developer.log('File does not exist after retries: $path', name: 'CameraDataSource');
          }
        } catch (e) {
          developer.log('Error checking file: $path - $e', name: 'CameraDataSource');
        }
      }

      developer.log('Validation complete: ${validPaths.length}/${imagePaths.length} files are valid', name: 'CameraDataSource');

      if (validPaths.isEmpty) {
        developer.log('No valid image files found after validation', name: 'CameraDataSource');

        // Fallback: If scanner returned paths but validation failed,
        // it might be a timing issue. Return original paths with warning.
        if (imagePaths.isNotEmpty) {
          developer.log('FALLBACK: Returning original paths despite validation failure', name: 'CameraDataSource');
          developer.log('This may indicate files are still being written by the scanner', name: 'CameraDataSource');
          return Right(imagePaths);
        }

        // Provide detailed error message for true failures
        final errorMessage = imagePaths.isEmpty
            ? 'No image paths returned from scanner'
            : 'Scanner returned ${imagePaths.length} paths but none were accessible: ${imagePaths.take(3).join(", ")}${imagePaths.length > 3 ? "..." : ""}';
        return Left(CameraFailure('No valid image files found after scanning. $errorMessage'));
      }

      developer.log('Successfully captured ${validPaths.length} images', name: 'CameraDataSource');
      return Right(validPaths);

    } catch (error, stackTrace) {
      developer.log(
        'Failed to capture images: $error',
        name: 'CameraDataSource',
        error: error,
        stackTrace: stackTrace,
      );

      return Left(
        CameraFailure('Failed to capture images: ${error.toString()}'),
      );
    }
  }
}
