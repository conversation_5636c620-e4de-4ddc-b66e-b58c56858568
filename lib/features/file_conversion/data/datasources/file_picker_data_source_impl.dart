import 'package:dartz/dartz.dart';
import 'package:file_picker/file_picker.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/file_conversion/data/datasources/file_picker_data_source.dart';

class FilePickerDataSourceImpl implements FilePickerDataSource {
  @override
  Future<Either<Failure, List<String>>> pickImages({int? allowMultiple}) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: allowMultiple != null ? allowMultiple > 1 : true,
        withData: false,
        withReadStream: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final filePaths = result.files.map((file) => file.path!).toList();
        return Right(filePaths);
      } else {
        return const Left(FilePickerFailure('No images selected'));
      }
    } catch (e) {
      return Left(FilePickerFailure('Failed to pick images: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, String>> pickDocument(List<String> allowedExtensions) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: allowedExtensions,
        withData: false,
        withReadStream: false,
      );

      if (result != null && result.files.isNotEmpty && result.files.first.path != null) {
        return Right(result.files.first.path!);
      } else {
        return const Left(FilePickerFailure('No document selected'));
      }
    } catch (e) {
      return Left(FilePickerFailure('Failed to pick document: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<String>>> pickFiles(List<String> allowedExtensions) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: allowedExtensions,
        allowMultiple: true,
        withData: false,
        withReadStream: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final filePaths = result.files.where((file) => file.path != null).map((file) => file.path!).toList();
        return Right(filePaths);
      } else {
        return const Left(FilePickerFailure('No files selected'));
      }
    } catch (e) {
      return Left(FilePickerFailure('Failed to pick files: ${e.toString()}'));
    }
  }
}

class FilePickerFailure extends Failure {
  const FilePickerFailure(super.message);
}
