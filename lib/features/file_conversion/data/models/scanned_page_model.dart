// ignore_for_file: invalid_annotation_target
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:myapp/features/file_conversion/domain/entities/scanned_page.dart';
import 'dart:ui';
import 'dart:convert';

part 'scanned_page_model.freezed.dart';
part 'scanned_page_model.g.dart';

@freezed
abstract class ScannedPageModel with _$ScannedPageModel {
  const factory ScannedPageModel({
    required String imagePath,
    required String filter,  // String representation of PageFilter
    required double rotationAngle,
    String? cropRect,  // JSON string representation of Rect
  }) = _ScannedPageModel;

  factory ScannedPageModel.fromJson(Map<String, dynamic> json) =>
      _$ScannedPageModelFromJson(json);
      
  factory ScannedPageModel.fromEntity(ScannedPage entity) {
    String filterString;
    switch (entity.filter) {
      case PageFilter.color:
        filterString = 'color';
        break;
      case PageFilter.grayscale:
        filterString = 'grayscale';
        break;
      case PageFilter.blackAndWhite:
        filterString = 'blackAndWhite';
        break;
    }

    String? cropRectJson;
    if (entity.cropRect != null) {
      cropRectJson = jsonEncode({
        'left': entity.cropRect!.left,
        'top': entity.cropRect!.top,
        'right': entity.cropRect!.right,
        'bottom': entity.cropRect!.bottom,
        'width': entity.cropRect!.width,
        'height': entity.cropRect!.height,
      });
    }

    return ScannedPageModel(
      imagePath: entity.imagePath,
      filter: filterString,
      rotationAngle: entity.rotationAngle,
      cropRect: cropRectJson,
    );
  }
}

extension ScannedPageModelX on ScannedPageModel {
  ScannedPage toEntity() {
    PageFilter filterEnum;
    switch (filter) {
      case 'color':
        filterEnum = PageFilter.color;
        break;
      case 'grayscale':
        filterEnum = PageFilter.grayscale;
        break;
      case 'blackAndWhite':
        filterEnum = PageFilter.blackAndWhite;
        break;
      default:
        filterEnum = PageFilter.color; // default fallback
        break;
    }

    Rect? cropRectObj;
    if (cropRect != null) {
      final cropData = jsonDecode(cropRect!);
      cropRectObj = Rect.fromLTWH(
        cropData['left']?.toDouble() ?? 0.0,
        cropData['top']?.toDouble() ?? 0.0,
        cropData['width']?.toDouble() ?? 0.0,
        cropData['height']?.toDouble() ?? 0.0,
      );
    }

    return ScannedPage(
      imagePath: imagePath,
      filter: filterEnum,
      rotationAngle: rotationAngle,
      cropRect: cropRectObj,
    );
  }
}
