// ignore_for_file: invalid_annotation_target
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:myapp/features/file_conversion/domain/entities/conversion_result.dart';
import 'dart:convert';

part 'conversion_result_model.freezed.dart';
part 'conversion_result_model.g.dart';

@freezed
abstract class ConversionResultModel with _$ConversionResultModel {
  const factory ConversionResultModel({
    required String outputPath,
    required String fileName,
    required int fileSize,
    required String createdAt,  // String representation of DateTime in ISO8601 format
  }) = _ConversionResultModel;

  factory ConversionResultModel.fromJson(Map<String, dynamic> json) =>
      _$ConversionResultModelFromJson(json);
      
  factory ConversionResultModel.fromEntity(ConversionResult entity) {
    return ConversionResultModel(
      outputPath: entity.outputPath,
      fileName: entity.fileName,
      fileSize: entity.fileSize,
      createdAt: entity.createdAt.toIso8601String(), // Convert DateTime to ISO8601 string
    );
  }
}

extension ConversionResultModelX on ConversionResultModel {
  ConversionResult toEntity() {
    return ConversionResult(
      outputPath: outputPath,
      fileName: fileName,
      fileSize: fileSize,
      createdAt: DateTime.parse(createdAt), // Parse ISO8601 string back to DateTime
    );
  }
}
