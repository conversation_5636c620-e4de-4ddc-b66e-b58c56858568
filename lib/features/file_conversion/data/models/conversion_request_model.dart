// ignore_for_file: invalid_annotation_target
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:myapp/features/file_conversion/domain/entities/conversion_request.dart';
import 'package:myapp/features/file_conversion/domain/entities/conversion_type.dart';
import 'package:myapp/features/file_conversion/domain/entities/scanned_page.dart'; // Import ScannedPage entity
import 'package:myapp/features/file_conversion/data/models/scanned_page_model.dart';
import 'dart:convert';

part 'conversion_request_model.freezed.dart';
part 'conversion_request_model.g.dart';

@freezed
abstract class ConversionRequestModel with _$ConversionRequestModel {
  const factory ConversionRequestModel({
    required String type,  // String representation of ConversionType
    required List<String> sourcePaths,
    required String outputFileName,
    String? orientation,  // String representation of PageOrientation
    String? scannedPages,  // JSON string for List<ScannedPageModel>
  }) = _ConversionRequestModel;

  factory ConversionRequestModel.fromJson(Map<String, dynamic> json) =>
      _$ConversionRequestModelFromJson(json);
      
  factory ConversionRequestModel.fromEntity(ConversionRequest entity) {
    String typeString;
    switch (entity.type) {
      case ConversionType.imageToPdf:
        typeString = 'imageToPdf';
        break;
      case ConversionType.docxToPdf:
        typeString = 'docxToPdf';
        break;
      case ConversionType.pptToPdf:
        typeString = 'pptToPdf';
        break;
      case ConversionType.xlsxToPdf:
        typeString = 'xlsxToPdf';
        break;
      case ConversionType.scanToPdf:
        typeString = 'scanToPdf';
        break;
    }

    String? orientationString;
    if (entity.orientation != null) {
      switch (entity.orientation!) {
        case PageOrientation.portrait:
          orientationString = 'portrait';
          break;
        case PageOrientation.landscape:
          orientationString = 'landscape';
          break;
        case PageOrientation.auto:
          orientationString = 'auto';
          break;
      }
    }

    String? scannedPagesJson;
    if (entity.scannedPages != null) {
      final scannedPageModels = entity.scannedPages!.map((page) => ScannedPageModel.fromEntity(page)).toList();
      scannedPagesJson = jsonEncode(scannedPageModels.map((model) => model.toJson()).toList());
    }

    return ConversionRequestModel(
      type: typeString,
      sourcePaths: entity.sourcePaths,
      outputFileName: entity.outputFileName,
      orientation: orientationString,
      scannedPages: scannedPagesJson,
    );
  }
}

extension ConversionRequestModelX on ConversionRequestModel {
  ConversionRequest toEntity() {
    ConversionType typeEnum;
    switch (type) {
      case 'imageToPdf':
        typeEnum = ConversionType.imageToPdf;
        break;
      case 'docxToPdf':
        typeEnum = ConversionType.docxToPdf;
        break;
      case 'pptToPdf':
        typeEnum = ConversionType.pptToPdf;
        break;
      case 'xlsxToPdf':
        typeEnum = ConversionType.xlsxToPdf;
        break;
      case 'scanToPdf':
        typeEnum = ConversionType.scanToPdf;
        break;
      default:
        typeEnum = ConversionType.imageToPdf; // default fallback
        break;
    }

    PageOrientation? orientationEnum;
    if (orientation != null) {
      switch (orientation!) {
        case 'portrait':
          orientationEnum = PageOrientation.portrait;
          break;
        case 'landscape':
          orientationEnum = PageOrientation.landscape;
          break;
        case 'auto':
          orientationEnum = PageOrientation.auto;
          break;
        default:
          orientationEnum = PageOrientation.portrait; // default fallback
          break;
      }
    }

    List<ScannedPage>? scannedPagesList;
    if (scannedPages != null) {
      final scannedPagesJson = jsonDecode(scannedPages!);
      if (scannedPagesJson is List) {
        scannedPagesList = [];
        for (final pageJson in scannedPagesJson) {
          if (pageJson is Map<String, dynamic>) {
            final scannedPageModel = ScannedPageModel.fromJson(pageJson);
            scannedPagesList.add(scannedPageModel.toEntity());
          }
        }
      }
    }

    return ConversionRequest(
      type: typeEnum,
      sourcePaths: sourcePaths,
      outputFileName: outputFileName,
      orientation: orientationEnum,
      scannedPages: scannedPagesList,
    );
  }
}
