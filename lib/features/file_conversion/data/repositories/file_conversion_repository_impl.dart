import 'dart:io';
import 'dart:ui' show Rect;
import 'package:dartz/dartz.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/file_conversion/domain/entities/conversion_request.dart';
import 'package:myapp/features/file_conversion/domain/entities/conversion_result.dart';
import 'package:myapp/features/file_conversion/domain/entities/scanned_page.dart';
import 'package:myapp/features/file_conversion/domain/repositories/file_conversion_repository.dart';
import '../datasources/camera_data_source.dart';
import '../datasources/file_picker_data_source.dart';
import '../datasources/pdf_converter_data_source.dart';

// Import PdfPageFormat from the pdf package
import 'package:pdf/pdf.dart';
import 'package:path/path.dart' as path;

class FileConversionRepositoryImpl implements FileConversionRepository {
  final CameraDataSource cameraDataSource;
  final FilePickerDataSource filePickerDataSource;
  final PDFConverterDataSource pdfConverterDataSource;

  FileConversionRepositoryImpl({
    required this.cameraDataSource,
    required this.filePickerDataSource,
    required this.pdfConverterDataSource,
  });

  @override
  Future<Either<Failure, List<String>>> captureImages() async {
    return cameraDataSource.captureImages();
  }

  @override
  Future<Either<Failure, String>> applyFiltersToImage(
    String imagePath,
    PageFilter filter,
    double rotation,
    Rect? cropRect,
  ) async {
    // Validate input
    if (imagePath.isEmpty) {
      return Future.value(left(ConversionFailure('Image path cannot be empty')));
    }

    final result = await pdfConverterDataSource.applyImageFilters(
      imagePath,
      _mapPageFilterToString(filter),
      rotation,
      cropRect,
    );
    return result;
  }

  @override
  Future<Either<Failure, ConversionResult>> convertImagesToPdf(
    List<String> imagePaths,
    String outputPath,
    PageOrientation? orientation,
  ) async {
    // Validate input
    if (imagePaths.isEmpty) {
      return Future.value(left(ConversionFailure('No images provided for PDF conversion')));
    }

    if (outputPath.isEmpty) {
      return Future.value(left(ConversionFailure('Output path cannot be empty')));
    }

    final pageFormat = _mapPageOrientationToPdfPageFormat(orientation);
    final result = await pdfConverterDataSource.convertImagesToPdf(
      imagePaths,
      outputPath,
      pageFormat,
    );

    return result.fold(
      (failure) => Future.value(left(failure)),
      (filePath) async {
        final conversion = await _createConversionResultFromFile(filePath);
        return conversion.fold(
          (failure) => left(failure),
          (result) => right(result),
        );
      },
    );
  }

  @override
  Future<Either<Failure, ConversionResult>> convertDocxToPdf(
    String docxPath,
    String outputFileName,
  ) async {
    final result = await pdfConverterDataSource.convertDocxToPdf(
      docxPath,
      outputFileName,
    );
    
    return result.fold(
      (failure) => Future.value(left(failure)),
      (filePath) async {
        final conversion = await _createConversionResultFromFile(filePath);
        return conversion.fold(
          (failure) => left(failure),
          (result) => right(result),
        );
      },
    );
  }

  @override
  Future<Either<Failure, ConversionResult>> convertPptToPdf(
    String pptPath,
    String outputFileName,
  ) async {
    final result = await pdfConverterDataSource.convertPptToPdf(
      pptPath,
      outputFileName,
    );
    
    return result.fold(
      (failure) => Future.value(left(failure)),
      (filePath) async {
        final conversion = await _createConversionResultFromFile(filePath);
        return conversion.fold(
          (failure) => left(failure),
          (result) => right(result),
        );
      },
    );
  }

  @override
  Future<Either<Failure, ConversionResult>> convertXlsxToPdf(
    String xlsxPath,
    String outputFileName,
  ) async {
    final result = await pdfConverterDataSource.convertXlsxToPdf(
      xlsxPath,
      outputFileName,
    );
    
    return result.fold(
      (failure) => Future.value(left(failure)),
      (filePath) async {
        final conversion = await _createConversionResultFromFile(filePath);
        return conversion.fold(
          (failure) => left(failure),
          (result) => right(result),
        );
      },
    );
  }

  @override
  Future<Either<Failure, List<String>>> pickImages() async {
    final result = await filePickerDataSource.pickImages();
    return result;
  }

  @override
  Future<Either<Failure, String>> pickDocument(List<String> extensions) async {
    final result = await filePickerDataSource.pickDocument(extensions);
    return result;
  }

  String _mapPageFilterToString(PageFilter filter) {
    switch (filter) {
      case PageFilter.color:
        return 'color';
      case PageFilter.grayscale:
        return 'grayscale';
      case PageFilter.blackAndWhite:
        return 'blackAndWhite';
    }
  }

  PdfPageFormat? _mapPageOrientationToPdfPageFormat(PageOrientation? orientation) {
    switch (orientation) {
      case PageOrientation.portrait:
        return PdfPageFormat.a4;
      case PageOrientation.landscape:
        return PdfPageFormat.a4.landscape;
      case PageOrientation.auto:
      case null:
        return null; // Let the data source auto-detect based on image
    }
  }

  Future<Either<Failure, ConversionResult>> _createConversionResultFromFile(String filePath) async {
    try {
      final file = File(filePath);
      final exists = await file.exists();

      if (!exists) {
        return Left(ConversionFailure('Output file was not created: $filePath'));
      }

      final fileName = path.basename(filePath);
      final fileSize = await file.length();
      // Note: File creation time is not available cross-platform in Dart.
      // Using lastModified() as a best-effort value for when the file was created.
      final createdAt = await file.lastModified();

      return Right(ConversionResult(
        outputPath: filePath,
        fileName: fileName,
        fileSize: fileSize,
        createdAt: createdAt,
      ));
    } catch (e, stackTrace) {
      return Left(ConversionFailure('Error creating conversion result: $e\n$stackTrace'));
    }
  }
}