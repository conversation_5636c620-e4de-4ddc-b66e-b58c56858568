import 'package:flutter/material.dart';

class PageReorderWidget extends StatelessWidget {
  final List<String> imagePaths;
  final Function(int, int) onReorder;
  final double itemHeight;

  const PageReorderWidget({
    super.key,
    required this.imagePaths,
    required this.onReorder,
    this.itemHeight = 80,
  });

  @override
  Widget build(BuildContext context) {
    return ReorderableListView.builder(
      itemCount: imagePaths.length,
      onReorder: onReorder,
      header: const Padding(
        padding: EdgeInsets.all(16.0),
        child: Text(
          'Reorder Pages',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
      ),
      itemBuilder: (context, index) {
        return Card(
          key: ValueKey(index),
          elevation: 4,
          child: Container(
            height: itemHeight,
            padding: const EdgeInsets.all(8),
            child: Row(
              children: [
                // Drag handle
                const Icon(
                  Icons.drag_handle,
                  color: Colors.grey,
                ),
                const SizedBox(width: 8),
                // Image preview
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4),
                    color: Colors.grey[300],
                  ),
                  child: const Icon(
                    Icons.image,
                    color: Colors.grey,
                  ), // In a real app, this would be an Image widget
                ),
                const SizedBox(width: 8),
                // Page number
                Text(
                  'Page ${index + 1}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const Spacer(),
                // Index indicator
                Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${index + 1}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}