import 'package:flutter/material.dart';

class ScanResultDialog extends StatelessWidget {
  const ScanResultDialog({
    super.key,
    required this.isLoading,
    this.errorMessage,
    this.onRetry,
    this.onDismiss,
  });

  final bool isLoading;
  final String? errorMessage;
  final VoidCallback? onRetry;
  final VoidCallback? onDismiss;

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return AlertDialog(
        title: const Text('Creating PDF'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: const [
            SizedBox(height: 12),
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Please wait while we process your pages.'),
          ],
        ),
      );
    }

    if (errorMessage != null) {
      return AlertDialog(
        title: const Text('Conversion failed'),
        content: Text(errorMessage!),
        actions: [
          if (onRetry != null)
            FilledButton.icon(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
            ),
          TextButton(
            onPressed: onDismiss ?? () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      );
    }

    return AlertDialog(
      title: const Text('Success'),
      content: const Text('PDF created successfully.'),
      actions: [
        TextButton(
          onPressed: onDismiss ?? () => Navigator.of(context).pop(),
          child: const Text('OK'),
        ),
      ],
    );
  }
}
