import 'package:flutter/material.dart';
import 'package:myapp/features/file_conversion/domain/entities/scanned_page.dart';

class PageFilterSelector extends StatelessWidget {
  final PageFilter currentFilter;
  final Function(PageFilter) onFilterChanged;

  const PageFilterSelector({
    super.key,
    required this.currentFilter,
    required this.onFilterChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildFilterChip(
            context: context,
            label: 'Color',
            filter: PageFilter.color,
            isSelected: currentFilter == PageFilter.color,
          ),
          _buildFilterChip(
            context: context,
            label: 'Grayscale',
            filter: PageFilter.grayscale,
            isSelected: currentFilter == PageFilter.grayscale,
          ),
          _buildFilterChip(
            context: context,
            label: 'B&W',
            filter: PageFilter.blackAndWhite,
            isSelected: currentFilter == PageFilter.blackAndWhite,
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip({
    required BuildContext context,
    required String label,
    required PageFilter filter,
    required bool isSelected,
  }) {
    return ChoiceChip(
      label: Text(label),
      selected: isSelected,
      selectedColor: Theme.of(context).primaryColor,
      onSelected: (selected) {
        if (selected) {
          onFilterChanged(filter);
        }
      },
      labelStyle: TextStyle(
        color: isSelected ? Colors.white : Colors.black,
      ),
    );
  }
}