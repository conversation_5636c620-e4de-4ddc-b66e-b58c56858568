import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class CreateConvertBottomSheet extends StatelessWidget {
  const CreateConvertBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return SafeArea(
      top: false,
      child: Padding(
        padding: const EdgeInsets.fromLTRB(16, 12, 16, 24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.shade400,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 16),
            Align(
              alignment: Alignment.centerLeft,
              child: Text(
                'Create PDF',
                style: textTheme.titleMedium,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _BottomSheetCardButton(
                    backgroundColor: Colors.amber.shade100,
                    iconColor: Colors.amber.shade700,
                    icon: Icons.image,
                    label: 'Image to PDF',
                    onTap: () {
                      final navigator = Navigator.of(context);
                      final router = GoRouter.of(context);
                      navigator.pop();
                      router.push('/convert-images');
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _BottomSheetCardButton(
                    backgroundColor: Colors.blue.shade100,
                    iconColor: Colors.blue.shade700,
                    icon: Icons.camera_alt,
                    label: 'Scan Document',
                    onTap: () {
                      final navigator = Navigator.of(context);
                      final router = GoRouter.of(context);
                      navigator.pop();
                      router.push('/scan-document');
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Align(
              alignment: Alignment.centerLeft,
              child: Text(
                'PDF Converter',
                style: textTheme.titleMedium,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _BottomSheetCircularButton(
                  backgroundColor: Colors.blue.shade100,
                  iconColor: Colors.blue.shade700,
                  icon: Icons.description,
                  label: 'Docx to PDF',
                  route: '/convert-document?type=docxToPdf',
                ),
                _BottomSheetCircularButton(
                  backgroundColor: Colors.red.shade100,
                  iconColor: Colors.red.shade700,
                  icon: Icons.slideshow,
                  label: 'PPT to PDF',
                  route: '/convert-document?type=pptToPdf',
                ),
                _BottomSheetCircularButton(
                  backgroundColor: Colors.green.shade100,
                  iconColor: Colors.green.shade700,
                  icon: Icons.table_chart,
                  label: 'XLSX to PDF',
                  route: '/convert-document?type=xlsxToPdf',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _BottomSheetCardButton extends StatelessWidget {
  const _BottomSheetCardButton({
    required this.backgroundColor,
    required this.iconColor,
    required this.icon,
    required this.label,
    required this.onTap,
  });

  final Color backgroundColor;
  final Color iconColor;
  final IconData icon;
  final String label;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    final textStyle = Theme.of(context).textTheme.bodyMedium;
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16),
      child: Ink(
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(16),
        ),
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 32, color: iconColor),
            const SizedBox(height: 12),
            Text(label, style: textStyle),
          ],
        ),
      ),
    );
  }
}

class _BottomSheetCircularButton extends StatelessWidget {
  const _BottomSheetCircularButton({
    required this.backgroundColor,
    required this.iconColor,
    required this.icon,
    required this.label,
    required this.route,
  });

  final Color backgroundColor;
  final Color iconColor;
  final IconData icon;
  final String label;
  final String route;

  @override
  Widget build(BuildContext context) {
    final textStyle = Theme.of(context).textTheme.bodyMedium;
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        InkWell(
          onTap: () {
            final navigator = Navigator.of(context);
            final router = GoRouter.of(context);
            navigator.pop();
            router.push(route);
          },
          customBorder: const CircleBorder(),
          child: Ink(
            height: 80,
            width: 80,
            decoration: BoxDecoration(
              color: backgroundColor,
              shape: BoxShape.circle,
            ),
            child: Icon(icon, size: 40, color: iconColor),
          ),
        ),
        const SizedBox(height: 8),
        Text(label, style: textStyle),
      ],
    );
  }
}
