import 'package:flutter/material.dart';

class ConversionProgressWidget extends StatelessWidget {
  final String status;
  final double? progress;
  final bool showProgress;

  const ConversionProgressWidget({
    super.key,
    required this.status,
    this.progress,
    this.showProgress = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (showProgress)
            LinearProgressIndicator(
              value: progress,
              backgroundColor: Colors.grey[300],
              valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
            ),
          const SizedBox(height: 8),
          Text(
            status,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          if (progress != null)
            Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Text(
                '${(progress! * 100).round()}%',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
            ),
        ],
      ),
    );
  }
}