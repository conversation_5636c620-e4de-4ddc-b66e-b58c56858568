import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:dartz/dartz.dart';
import 'package:myapp/features/file_conversion/domain/entities/conversion_result.dart';
import 'package:myapp/features/file_conversion/domain/entities/conversion_type.dart';
import 'package:myapp/features/file_conversion/presentation/providers/file_conversion_providers.dart';
import 'package:myapp/core/error/failures.dart';

part 'document_conversion_notifier.g.dart';

@riverpod
class DocumentConversionNotifier extends _$DocumentConversionNotifier {
  @override
  FutureOr<ConversionResult?> build() async {
    return null;
  }

  Future<void> convertDocument({
    required String filePath,
    required String outputFileName,
    required ConversionType type,
  }) async {
    final result = await _executeConversion(filePath, outputFileName, type);
    
    result.fold(
      (failure) => state = AsyncValue.error(failure, StackTrace.current),
      (success) {
        state = AsyncValue.data(success);
      },
    );
  }

  Future<Either<Failure, ConversionResult>> _executeConversion(
    String filePath,
    String outputFileName,
    ConversionType type,
  ) async {
    switch (type) {
      case ConversionType.docxToPdf:
        final useCase = ref.read(convertDocxToPdfProvider);
        return await useCase(filePath, outputFileName);
      case ConversionType.pptToPdf:
        final useCase = ref.read(convertPptToPdfProvider);
        return await useCase(filePath, outputFileName);
      case ConversionType.xlsxToPdf:
        final useCase = ref.read(convertXlsxToPdfProvider);
        return await useCase(filePath, outputFileName);
      case ConversionType.imageToPdf:
      case ConversionType.scanToPdf:
        // These shouldn't be handled by this notifier
        return const Left(ConversionFailure('Invalid conversion type for document conversion'));
    }
  }
}