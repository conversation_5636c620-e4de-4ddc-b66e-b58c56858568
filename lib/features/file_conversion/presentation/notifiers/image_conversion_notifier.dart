import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:myapp/features/file_conversion/domain/entities/conversion_result.dart';
import 'package:myapp/features/file_conversion/domain/entities/conversion_request.dart';
import 'package:myapp/features/file_conversion/presentation/providers/file_conversion_providers.dart';
import 'package:myapp/core/error/failures.dart';

part 'image_conversion_notifier.g.dart';

@riverpod
class ImageConversionNotifier extends _$ImageConversionNotifier {
  @override
  FutureOr<ConversionResult?> build() async {
    return null;
  }

  Future<void> convertImages({
    required List<String> imagePaths,
    required String outputFileName,
    PageOrientation? orientation,
  }) async {
    final useCase = ref.read(convertImagesToPdfProvider);
    
    state = const AsyncValue.loading();
    
    final result = await useCase(
      imagePaths,
      outputFileName,
      orientation,
    );
    
    result.fold(
      (failure) => state = AsyncValue.error(failure, StackTrace.current),
      (success) {
        state = AsyncValue.data(success);
      },
    );
  }
  
  Future<void> reorderImages(int oldIndex, int newIndex) async {
    // This would be handled in the UI, not in the notifier
    // The reorder functionality would happen in the UI layer before calling convertImages
  }
}