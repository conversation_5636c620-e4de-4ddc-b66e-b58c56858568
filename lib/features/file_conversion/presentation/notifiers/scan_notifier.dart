import 'dart:developer' as developer;
import 'dart:io';
import 'dart:ui';

import 'package:dartz/dartz.dart';
import 'package:intl/intl.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/file_conversion/domain/entities/conversion_request.dart';
import 'package:myapp/features/file_conversion/domain/entities/conversion_result.dart';
import 'package:myapp/features/file_conversion/domain/entities/scanned_page.dart';
import 'package:myapp/features/file_conversion/presentation/providers/file_conversion_providers.dart';

part 'scan_notifier.g.dart';

@riverpod
class ScanNotifier extends _$ScanNotifier {
  @override
  FutureOr<List<ScannedPage>> build() async {
    return [];
  }

  Future<void> captureImage() async {
    developer.log('Starting image capture...', name: 'ScanNotifier');

    final repository = ref.read(fileConversionRepositoryProvider);
    final existingPages = state.asData?.value ?? [];

    developer.log('Existing pages count: ${existingPages.length}', name: 'ScanNotifier');

    state = const AsyncValue.loading();
    final result = await repository.captureImages();

    await result.fold(
      (failure) async {
        developer.log('Capture failed: ${failure.runtimeType} - ${failure.message}', name: 'ScanNotifier');

        if (failure is UserCancelledFailure) {
          developer.log('User cancelled scan, restoring previous state', name: 'ScanNotifier');
          state = AsyncValue.data(existingPages);
        } else {
          developer.log('Setting error state', name: 'ScanNotifier');
          state = AsyncValue.error(failure, StackTrace.current);
        }
      },
      (imagePaths) async {
        developer.log('Capture successful: ${imagePaths.length} images', name: 'ScanNotifier');

        for (int i = 0; i < imagePaths.length; i++) {
          developer.log('Image $i: ${imagePaths[i]}', name: 'ScanNotifier');
        }

        final newPages = imagePaths
            .map(
              (imagePath) => ScannedPage(
                imagePath: imagePath,
                filter: PageFilter.color,
                rotationAngle: 0.0,
              ),
            )
            .toList();

        developer.log('Created ${newPages.length} ScannedPage objects', name: 'ScanNotifier');
        state = AsyncValue.data([...existingPages, ...newPages]);
        developer.log('Updated state with ${state.asData?.value.length ?? 0} total pages', name: 'ScanNotifier');
      },
    );
  }

  Future<void> applyFilter(int index, PageFilter filter) async {
    final pages = List<ScannedPage>.from(state.asData?.value ?? []);
    if (index < 0 || index >= pages.length) return;

    pages[index] = pages[index].copyWith(filter: filter);
    state = AsyncValue.data(pages);
  }

  Future<void> rotatePage(int index, double angle) async {
    final pages = List<ScannedPage>.from(state.asData?.value ?? []);
    if (index < 0 || index >= pages.length) return;

    final normalizedAngle = angle % 360;
    pages[index] = pages[index].copyWith(rotationAngle: normalizedAngle);
    state = AsyncValue.data(pages);
  }

  Future<void> cropPage(int index, Rect cropRect) async {
    final pages = List<ScannedPage>.from(state.asData?.value ?? []);
    if (index < 0 || index >= pages.length) return;

    pages[index] = pages[index].copyWith(cropRect: cropRect);
    state = AsyncValue.data(pages);
  }
  
  Future<void> reorderPages(int oldIndex, int newIndex) async {
    final pages = List<ScannedPage>.from(state.asData?.value ?? []);
    if (oldIndex < 0 || oldIndex >= pages.length) return;

    var adjustedNewIndex = newIndex;
    if (newIndex > oldIndex) {
      adjustedNewIndex -= 1;
    }
    if (adjustedNewIndex < 0 || adjustedNewIndex > pages.length - 1) {
      adjustedNewIndex = pages.length - 1;
    }

    final item = pages.removeAt(oldIndex);
    pages.insert(adjustedNewIndex, item);

    state = AsyncValue.data(pages);
  }

  Future<void> deletePage(int index) async {
    final pages = List<ScannedPage>.from(state.asData?.value ?? []);
    if (index < 0 || index >= pages.length) return;

    pages.removeAt(index);
    state = AsyncValue.data(pages);
  }

  Future<void> updateImagePath(int index, String newPath) async {
    final pages = List<ScannedPage>.from(state.asData?.value ?? []);
    if (index < 0 || index >= pages.length) return;

    pages[index] = pages[index].copyWith(
      imagePath: newPath,
      cropRect: null,
    );
    state = AsyncValue.data(pages);
  }

  Future<void> clearPages() async {
    state = const AsyncValue.data([]);
  }

  Future<Either<Failure, ConversionResult?>> finalizeScan() async {
    developer.log('Starting finalize scan...', name: 'ScanNotifier');

    final currentPages = List<ScannedPage>.from(state.asData?.value ?? []);
    developer.log('Current pages count: ${currentPages.length}', name: 'ScanNotifier');

    if (currentPages.isEmpty) {
      developer.log('No pages to finalize', name: 'ScanNotifier');
      return const Right(null);
    }

    // Validate that all image files exist
    developer.log('Validating image files...', name: 'ScanNotifier');
    for (int i = 0; i < currentPages.length; i++) {
      final page = currentPages[i];
      developer.log('Checking page $i: ${page.imagePath}', name: 'ScanNotifier');

      final file = File(page.imagePath);
      if (!await file.exists()) {
        final failure = ConversionFailure('Image file not found: ${page.imagePath}');
        developer.log('File validation failed: ${failure.message}', name: 'ScanNotifier');
        // Preserve current pages in error state
        state = AsyncValue.data(currentPages);
        return Left(failure);
      } else {
        developer.log('File exists: ${page.imagePath}', name: 'ScanNotifier');
      }
    }

    developer.log('All files validated successfully', name: 'ScanNotifier');

    final repository = ref.read(fileConversionRepositoryProvider);
    // Removed loading transition to preserve pages on error
    final processedPaths = <String>[];
    final updatedPages = <ScannedPage>[];

    // Process each page
    for (final page in currentPages) {
      final needsProcessing =
          page.filter != PageFilter.color ||
          page.rotationAngle % 360 != 0 ||
          page.cropRect != null;

      if (needsProcessing) {
        final processedResult = await repository.applyFiltersToImage(
          page.imagePath,
          page.filter,
          page.rotationAngle,
          page.cropRect,
        );

        final processedPath = processedResult.fold<String?>(
          (failure) {
            // Preserve current pages in error state
            state = AsyncValue.data(currentPages);
            return null;
          },
          (value) => value,
        );

        if (processedPath == null) {
          // Preserve current pages in error state
          state = AsyncValue.data(currentPages);
          if (processedResult.isLeft()) {
            return Left(processedResult.fold((l) => l, (r) => ConversionFailure('Processing failed without error')));
          }
          return const Left(ConversionFailure('Processing failed without error'));
        }

        processedPaths.add(processedPath);
        updatedPages.add(
          page.copyWith(
            imagePath: processedPath,
            cropRect: null,
            rotationAngle: 0.0, // Reset rotation angle to avoid double-rotation
            filter: PageFilter.color, // Reset filter to avoid double-applying effects
          ),
        );
      } else {
        processedPaths.add(page.imagePath);
        updatedPages.add(page.copyWith(
          rotationAngle: 0.0, // Reset rotation angle even for unprocessed pages
          filter: PageFilter.color, // Reset filter to avoid double-applying effects
        ));
      }
    }

    // Check if we have any paths to convert
    if (processedPaths.isEmpty) {
      // Preserve current pages in error state
      state = AsyncValue.data(currentPages);
      return const Left(ConversionFailure('No images available for PDF conversion'));
    }

    // Create output directory with error handling
    try {
      final documentsDir = await getApplicationDocumentsDirectory();
      final scannedDir = Directory(path.join(documentsDir.path, 'Scanned'));
      await scannedDir.create(recursive: true);

      final fileName = 'scanned_${DateFormat('yyyyMMdd_HHmmss').format(DateTime.now())}.pdf';
      final outputPath = path.join(scannedDir.path, fileName);

      final conversion = await repository.convertImagesToPdf(
        processedPaths,
        outputPath,
        PageOrientation.auto,
      );

      return conversion.fold(
        (failure) {
          // Preserve current pages in error state
          state = AsyncValue.data(currentPages);
          return Left(failure);
        },
        (result) {
          state = AsyncValue.data(updatedPages);
          return Right(result);
        },
      );
    } catch (e) {
      // Preserve current pages in error state
      state = AsyncValue.data(currentPages);
      return Left(ConversionFailure('Failed to create output directory: $e'));
    }
  }
}