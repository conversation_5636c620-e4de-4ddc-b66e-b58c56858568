import 'dart:io';
import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/file_conversion/domain/entities/conversion_result.dart';
import 'package:myapp/features/file_conversion/domain/entities/scanned_page.dart';
import 'package:myapp/features/file_conversion/presentation/notifiers/scan_notifier.dart';
import 'package:myapp/features/file_conversion/presentation/widgets/page_filter_selector.dart';
import 'package:myapp/features/file_conversion/presentation/widgets/scan_result_dialog.dart';

class ScanReviewScreen extends ConsumerStatefulWidget {
  const ScanReviewScreen({super.key});

  @override
  ConsumerState<ScanReviewScreen> createState() => _ScanReviewScreenState();
}

class _ScanReviewScreenState extends ConsumerState<ScanReviewScreen> {
  late final PageController _pageController;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final scanState = ref.watch(scanProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Review Scanned Pages'),
        actions: [
          TextButton(
            onPressed: () => _addMorePages(context),
            child: const Text(
              'Add More',
              style: TextStyle(color: Colors.white, fontSize: 16),
            ),
          ),
          IconButton(
            tooltip: 'Reorder',
            onPressed: scanState.asData?.value.isNotEmpty == true
                ? () => _showReorderSheet(context)
                : null,
            icon: const Icon(Icons.reorder),
          ),
          IconButton(
            tooltip: 'Delete page',
            onPressed: scanState.asData?.value.isNotEmpty == true
                ? () => _confirmDeleteCurrentPage(context)
                : null,
            icon: const Icon(Icons.delete_outline),
          ),
        ],
      ),
      body: scanState.when(
        data: (pages) => _buildContent(context, pages),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, _) => _buildError(context, error),
      ),
      bottomNavigationBar: SafeArea(
        minimum: const EdgeInsets.all(16),
        child: FilledButton.icon(
          onPressed: scanState.asData?.value.isNotEmpty == true
              ? () => _onFinalizePressed(context)
              : null,
          icon: const Icon(Icons.picture_as_pdf),
          label: const Text('Create PDF'),
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context, List<ScannedPage> pages) {
    if (pages.isEmpty) {
      return _buildEmptyState(context);
    }

    _currentIndex = _currentIndex.clamp(0, pages.length - 1);

    return Column(
      children: [
        Expanded(
          child: PageView.builder(
            controller: _pageController,
            itemCount: pages.length,
            onPageChanged: (index) => setState(() => _currentIndex = index),
            itemBuilder: (context, index) => _buildPagePreview(pages[index]),
          ),
        ),
        const SizedBox(height: 16),
        PageFilterSelector(
          currentFilter: pages[_currentIndex].filter,
          onFilterChanged: (filter) => ref
              .read(scanProvider.notifier)
              .applyFilter(_currentIndex, filter),
        ),
        const SizedBox(height: 8),
        _buildControlsRow(pages[_currentIndex]),
        const SizedBox(height: 8),
        _buildPageIndicator(context, pages.length),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildPagePreview(ScannedPage page) {
    final file = File(page.imagePath);
    final filter = _colorFilterFor(page.filter);
    Widget imageWidget = Image.file(file, fit: BoxFit.contain);

    if (filter != null) {
      imageWidget = ColorFiltered(colorFilter: filter, child: imageWidget);
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Material(
          color: Colors.black,
          child: InteractiveViewer(
            panEnabled: true,
            minScale: 0.8,
            maxScale: 4,
            child: Center(
              child: Transform.rotate(
                angle: page.rotationAngle * math.pi / 180,
                child: imageWidget,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildControlsRow(ScannedPage page) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          OutlinedButton.icon(
            onPressed: () => _rotateCurrentPage(page, -90),
            icon: const Icon(Icons.rotate_left),
            label: const Text('Rotate left'),
          ),
          OutlinedButton.icon(
            onPressed: () => _openCropper(page),
            icon: const Icon(Icons.crop),
            label: const Text('Crop'),
          ),
          OutlinedButton.icon(
            onPressed: () => _rotateCurrentPage(page, 90),
            icon: const Icon(Icons.rotate_right),
            label: const Text('Rotate right'),
          ),
        ],
      ),
    );
  }

  Widget _buildPageIndicator(BuildContext context, int totalPages) {
    final theme = Theme.of(context);
    return Text(
      'Page ${_currentIndex + 1} of $totalPages',
      style: theme.textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.library_add, size: 80),
          const SizedBox(height: 16),
          const Text('No pages scanned yet.'),
          const SizedBox(height: 16),
          FilledButton(
            onPressed: () => _addMorePages(context),
            child: const Text('Scan a document'),
          ),
        ],
      ),
    );
  }

  Widget _buildError(BuildContext context, Object error) {
    final message = error is Failure ? error.message : 'Unexpected error.';
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text(message, textAlign: TextAlign.center),
          const SizedBox(height: 16),
          FilledButton(
            onPressed: () => ref.invalidate(scanProvider),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  void _rotateCurrentPage(ScannedPage page, int deltaDegrees) {
    final newAngle = page.rotationAngle + deltaDegrees;
    ref.read(scanProvider.notifier).rotatePage(_currentIndex, newAngle);
    setState(() {});
  }

  Future<void> _openCropper(ScannedPage page) async {
    final cropped = await ImageCropper().cropImage(
      sourcePath: page.imagePath,
      compressQuality: 100,
      uiSettings: [
        AndroidUiSettings(
          toolbarTitle: 'Crop page',
          toolbarColor: Theme.of(context).colorScheme.surface,
          toolbarWidgetColor: Theme.of(context).colorScheme.onSurface,
          lockAspectRatio: false,
        ),
  IOSUiSettings(title: 'Crop page'),
      ],
    );

    if (cropped != null) {
      await ref
          .read(scanProvider.notifier)
          .updateImagePath(_currentIndex, cropped.path);
      setState(() {});
    }
  }

  ColorFilter? _colorFilterFor(PageFilter filter) {
    switch (filter) {
      case PageFilter.color:
        return null;
      case PageFilter.grayscale:
        return const ColorFilter.matrix(<double>[
          0.2126, 0.7152, 0.0722, 0, 0,
          0.2126, 0.7152, 0.0722, 0, 0,
          0.2126, 0.7152, 0.0722, 0, 0,
          0, 0, 0, 1, 0,
        ]);
      case PageFilter.blackAndWhite:
        return const ColorFilter.matrix(<double>[
          1.5, 1.5, 1.5, 0, -1,
          1.5, 1.5, 1.5, 0, -1,
          1.5, 1.5, 1.5, 0, -1,
          0, 0, 0, 1, 0,
        ]);
    }
  }

  void _addMorePages(BuildContext context) {
    context.go('/scan-document');
  }

  Future<void> _confirmDeleteCurrentPage(BuildContext context) async {
    final pages = ref.read(scanProvider).asData?.value ?? [];
    if (pages.isEmpty) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Remove page'),
        content: const Text('Do you want to remove this page from the scan?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(false),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () => Navigator.of(dialogContext).pop(true),
            child: const Text('Remove'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await ref.read(scanProvider.notifier).deletePage(_currentIndex);
      setState(() {
        final remaining = ref.read(scanProvider).asData?.value.length ?? 0;
        _currentIndex = remaining == 0 ? 0 : _currentIndex.clamp(0, remaining - 1);
      });
    }
  }

  Future<void> _showReorderSheet(BuildContext context) async {
    final pages = List<ScannedPage>.from(ref.read(scanProvider).asData?.value ?? []);
    if (pages.length < 2) return;

    await showModalBottomSheet<void>(
      context: context,
      showDragHandle: true,
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: ReorderableListView.builder(
            itemCount: pages.length,
            onReorder: (oldIndex, newIndex) {
              final adjustedNewIndex = newIndex > oldIndex ? newIndex - 1 : newIndex;
              final page = pages.removeAt(oldIndex);
              pages.insert(adjustedNewIndex, page);
              ref.read(scanProvider.notifier).reorderPages(oldIndex, newIndex);
              setState(() {
                if (_currentIndex == oldIndex) {
                  _currentIndex = adjustedNewIndex;
                }
              });
            },
            itemBuilder: (context, index) {
              final page = pages[index];
              return ListTile(
                key: ValueKey(page.imagePath),
                leading: CircleAvatar(child: Text('${index + 1}')),
                title: Text('Page ${index + 1}'),
                subtitle: Text(page.filter.name.toUpperCase()),
                trailing: const Icon(Icons.drag_handle),
                onTap: () {
                  Navigator.of(context).pop();
                  _pageController.jumpToPage(index);
                  setState(() => _currentIndex = index);
                },
              );
            },
          ),
        );
      },
    );
  }

  Future<void> _onFinalizePressed(BuildContext context) async {
    showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (context) => const ScanResultDialog(isLoading: true),
    );

    final result = await ref.read(scanProvider.notifier).finalizeScan();

    if (!context.mounted) {
      return;
    }

    // Safely dismiss loading dialog
    try {
      Navigator.of(context).pop();
    } catch (_) {
      // Dialog already dismissed or context invalid
    }

    result.fold(
      (failure) {
        // Handle failure with error message from the typed result
        final message = failure.message;

        if (!context.mounted) {
          return;
        }

        // Show error dialog with retry option
        showDialog<void>(
          context: context,
          builder: (context) => ScanResultDialog(
            isLoading: false,
            errorMessage: message,
            onDismiss: () {
              if (context.mounted) {
                Navigator.of(context).pop();
              }
            },
            onRetry: () {
              if (context.mounted) {
                Navigator.of(context).pop();
                // Wait a frame before retrying to ensure dialog is dismissed
                Future.microtask(() {
                  if (context.mounted) {
                    _onFinalizePressed(context);
                  }
                });
              }
            },
          ),
        );
      },
      (conversionResult) {
        if (conversionResult == null) {
          // Handle case where no conversion result was returned (no pages to process)
          if (!context.mounted) {
            return;
          }

          // Show error dialog with retry option
          showDialog<void>(
            context: context,
            builder: (context) => ScanResultDialog(
              isLoading: false,
              errorMessage: 'No pages available to convert to PDF.',
              onDismiss: () {
                if (context.mounted) {
                  Navigator.of(context).pop();
                }
              },
              onRetry: () {
                if (context.mounted) {
                  Navigator.of(context).pop();
                  // Wait a frame before retrying to ensure dialog is dismissed
                  Future.microtask(() {
                    if (context.mounted) {
                      _onFinalizePressed(context);
                    }
                  });
                }
              },
            ),
          );
          return;
        }

        // Success - navigate to success screen
        final pdfPath = Uri.encodeComponent(conversionResult.outputPath);
        final fileName = Uri.encodeComponent(conversionResult.fileName);

        if (!context.mounted) {
          return;
        }

        context.go('/scan-success?pdfPath=$pdfPath&fileName=$fileName');

        // Clear pages after successful navigation
        Future.microtask(() {
          ref.read(scanProvider.notifier).clearPages();
        });
      },
    );
  }
}