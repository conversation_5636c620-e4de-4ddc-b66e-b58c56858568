import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:myapp/features/file_conversion/domain/entities/conversion_request.dart';
import 'package:myapp/features/file_conversion/presentation/notifiers/image_conversion_notifier.dart';
import 'package:myapp/features/file_conversion/presentation/widgets/page_reorder_widget.dart';
import 'package:myapp/features/file_conversion/presentation/widgets/conversion_progress_widget.dart';

class ImageConversionScreen extends ConsumerStatefulWidget {
  const ImageConversionScreen({super.key});

  @override
  ConsumerState<ImageConversionScreen> createState() => _ImageConversionScreenState();
}

class _ImageConversionScreenState extends ConsumerState<ImageConversionScreen> {
  final List<String> _selectedImages = [];
  String? _orientation = 'Auto';
  final TextEditingController _fileNameController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _fileNameController.text = 'converted_images_${DateTime.now().millisecondsSinceEpoch}.pdf';
  }

  @override
  void dispose() {
    _fileNameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final asyncValue = ref.watch(imageConversionProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Images to PDF'),
      ),
      body: Column(
        children: [
          // Image selection area
          Expanded(
            child: _selectedImages.isEmpty
                ? const Center(
                    child: Text('No images selected'),
                  )
                : PageReorderWidget(
                    imagePaths: _selectedImages,
                    onReorder: (int oldIndex, int newIndex) {
                      // Handle reordering in the UI only for now
                      // The actual reorder will be handled when conversion is triggered
                      setState(() {
                        if (oldIndex < newIndex) {
                          newIndex -= 1;
                        }
                        final item = _selectedImages.removeAt(oldIndex);
                        _selectedImages.insert(newIndex, item);
                      });
                    },
                  ),
          ),
          
          // Orientation selection
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                const Text('Page Orientation:'),
                const Spacer(),
                DropdownButton<String>(
                  value: _orientation,
                  items: const [
                    DropdownMenuItem(value: 'Portrait', child: Text('Portrait')),
                    DropdownMenuItem(value: 'Landscape', child: Text('Landscape')),
                    DropdownMenuItem(value: 'Auto', child: Text('Auto')),
                  ],
                  onChanged: (String? newValue) {
                    setState(() {
                      _orientation = newValue;
                    });
                  },
                ),
              ],
            ),
          ),
          
          // File name input
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: TextField(
              controller: _fileNameController,
              decoration: const InputDecoration(
                labelText: 'Output File Name',
                suffix: Text('.pdf'),
              ),
            ),
          ),
          
          // Convert button
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: ElevatedButton(
              onPressed: _selectedImages.isEmpty || asyncValue.isLoading
                  ? null
                  : () => _convertToPdf(context),
              child: const Text('Convert to PDF'),
            ),
          ),
          
          // Progress indicator when converting
          if (asyncValue.isLoading)
            const ConversionProgressWidget(
              status: 'Converting images to PDF...',
              progress: null, // This would be updated based on actual progress
            ),
        ],
      ),
    );
  }

  void _convertToPdf(BuildContext context) {
    if (_selectedImages.isEmpty) return;

    // Trigger the conversion via the notifier
    ref.read(imageConversionProvider.notifier).convertImages(
      imagePaths: _selectedImages,
      outputFileName: _fileNameController.text,
      orientation: _mapOrientationStringToEnum(_orientation),
    );
  }

  PageOrientation? _mapOrientationStringToEnum(String? orientation) {
    switch (orientation) {
      case 'Portrait':
        return PageOrientation.portrait;
      case 'Landscape':
        return PageOrientation.landscape;
      default:
        return PageOrientation.auto;
    }
  }
}