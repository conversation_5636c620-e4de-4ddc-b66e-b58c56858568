import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:file_picker/file_picker.dart';
import 'package:myapp/features/file_conversion/domain/entities/conversion_type.dart';
import 'package:myapp/features/file_conversion/presentation/notifiers/document_conversion_notifier.dart';
import 'package:myapp/features/file_conversion/presentation/widgets/conversion_progress_widget.dart';

class DocumentConversionScreen extends ConsumerStatefulWidget {
  final ConversionType conversionType;

  const DocumentConversionScreen({super.key, required this.conversionType});

  @override
  ConsumerState<DocumentConversionScreen> createState() => _DocumentConversionScreenState();
}

class _DocumentConversionScreenState extends ConsumerState<DocumentConversionScreen> {
  String? _selectedFilePath;
  String? _fileName;
  String? _fileSize;
  final TextEditingController _outputNameController = TextEditingController();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _outputNameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final asyncValue = ref.watch(documentConversionProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(_getTitle()),
      ),
      body: Column(
        children: [
          // File selection area
          Expanded(
            child: _selectedFilePath == null
                ? Center(
                    child: ElevatedButton(
                      onPressed: _pickFile,
                      child: Text('Select ${_getFileExtension()} File'),
                    ),
                  )
                : _buildFilePreview(),
          ),
          
          // Output file name
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _outputNameController,
              enabled: _selectedFilePath != null,
              decoration: const InputDecoration(
                labelText: 'Output File Name',
                suffix: Text('.pdf'),
              ),
            ),
          ),
          
          // Convert button
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: ElevatedButton(
              onPressed: _selectedFilePath == null || asyncValue.isLoading
                  ? null
                  : _convertFile,
              child: const Text('Convert to PDF'),
            ),
          ),
          
          // Progress indicator when converting
          if (asyncValue.isLoading)
            const ConversionProgressWidget(
              status: 'Converting document to PDF...',
              progress: null, // This would be updated based on actual progress
            ),
          
          // Success/Error messages
          if (asyncValue.hasError)
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                'Conversion failed: ${asyncValue.error}',
                style: const TextStyle(color: Colors.red),
              ),
            ),
        ],
      ),
    );
  }

  String _getTitle() {
    switch (widget.conversionType) {
      case ConversionType.docxToPdf:
        return 'DOCX to PDF';
      case ConversionType.pptToPdf:
        return 'PPT to PDF';
      case ConversionType.xlsxToPdf:
        return 'XLSX to PDF';
      default:
        return 'Document to PDF';
    }
  }

  String _getFileExtension() {
    switch (widget.conversionType) {
      case ConversionType.docxToPdf:
        return '.doc/.docx';
      case ConversionType.pptToPdf:
        return '.ppt/.pptx';
      case ConversionType.xlsxToPdf:
        return '.xls/.xlsx';
      default:
        return 'document';
    }
  }

  Future<void> _pickFile() async {
    try {
      String? extension;
      switch (widget.conversionType) {
        case ConversionType.docxToPdf:
          extension = 'docx';
          break;
        case ConversionType.pptToPdf:
          extension = 'pptx';
          break;
        case ConversionType.xlsxToPdf:
          extension = 'xlsx';
          break;
        default:
          extension = null;
      }

      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: extension != null ? [extension] : null,
      );

      if (result != null && result.files.single.path != null) {
        setState(() {
          _selectedFilePath = result.files.single.path!;
          _fileName = result.files.single.name;
          _fileSize = _formatBytes(result.files.single.size);
          _outputNameController.text = 
              '${result.files.single.name.substring(0, result.files.single.name.lastIndexOf('.'))}.pdf';
        });
      }
    } catch (e) {
      // Handle file picker errors
      print('Error picking file: $e');
    }
  }

  Widget _buildFilePreview() {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.insert_drive_file,
              size: 64,
              color: Colors.blue,
            ),
            const SizedBox(height: 8),
            Text(
              _fileName ?? 'Unknown',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            Text(
              _fileSize ?? 'Unknown size',
              style: const TextStyle(
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatBytes(int bytes) {
    const suffixes = ['B', 'KB', 'MB', 'GB'];
    var i = 0;
    double size = bytes.toDouble();
    
    while (size >= 1024 && i < suffixes.length - 1) {
      size /= 1024;
      i++;
    }
    
    return '${size.toStringAsFixed(1)} ${suffixes[i]}';
  }

  void _convertFile() {
    if (_selectedFilePath == null) return;

    ref.read(documentConversionProvider.notifier).convertDocument(
      filePath: _selectedFilePath!,
      outputFileName: _outputNameController.text,
      type: widget.conversionType,
    );
  }
}