import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:myapp/core/utils/file_size_formatter.dart';
import 'package:myapp/features/sharing/presentation/notifiers/sharing_notifier.dart';

class ScanSuccessScreen extends ConsumerWidget {
  const ScanSuccessScreen({
    super.key,
    required this.pdfPath,
    required this.fileName,
  });

  final String pdfPath;
  final String fileName;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final file = File(pdfPath);
    final exists = file.existsSync();
    final size = exists ? file.lengthSync() : 0;
    final formattedSize = FileSizeFormatter.formatFileSize(size);

    return Scaffold(
      appBar: AppBar(title: const Text('Scan complete')),
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Icon(Icons.check_circle, size: 96, color: Colors.green),
            const SizedBox(height: 24),
            Text(
              'PDF created successfully',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),
            Card(
              margin: EdgeInsets.zero,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('File name', style: Theme.of(context).textTheme.labelMedium),
                    const SizedBox(height: 4),
                    Text(fileName, style: Theme.of(context).textTheme.titleMedium),
                    const SizedBox(height: 12),
                    Text('File size', style: Theme.of(context).textTheme.labelMedium),
                    const SizedBox(height: 4),
                    Text(formattedSize, style: Theme.of(context).textTheme.titleMedium),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 32),
            FilledButton.icon(
              onPressed: () {
                final encodedPath = Uri.encodeComponent(pdfPath);
                final encodedName = Uri.encodeComponent(fileName);
                context.go('/viewer/pdf/$encodedPath?name=$encodedName');
              },
              icon: const Icon(Icons.open_in_new),
              label: const Text('Open PDF'),
            ),
            const SizedBox(height: 12),
            OutlinedButton.icon(
              onPressed: () async {
                final shareUseCase = ref.read(shareFileProvider);
                final result = await shareUseCase(path: pdfPath, mime: 'application/pdf');
                if (!context.mounted) return;
                result.fold(
                  (failure) => ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text(failure.message)),
                  ),
                  (_) => ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Share sheet opened.')),
                  ),
                );
              },
              icon: const Icon(Icons.share_outlined),
              label: const Text('Share'),
            ),
            const SizedBox(height: 24),
            TextButton(
              onPressed: () => context.go('/'),
              child: const Text('Done'),
            ),
          ],
        ),
      ),
    );
  }
}
