import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/file_conversion/domain/entities/scanned_page.dart';
import 'package:myapp/features/file_conversion/presentation/notifiers/scan_notifier.dart';

class ScanCameraScreen extends ConsumerWidget {
  const ScanCameraScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.listen<AsyncValue<List<ScannedPage>>>(scanProvider, (previous, next) {
      if (previous == null) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (!context.mounted) return;
          final notifier = ref.read(scanProvider.notifier);
          notifier.captureImage();
        });
      }

      if (next.hasError && !(previous?.hasError ?? false)) {
        final failure = next.error;
        WidgetsBinding.instance.addPostFrameCallback((_) async {
          if (!context.mounted) return;
          final message = failure is Failure
              ? failure.message
              : 'Failed to launch document scanner.';

          await showDialog<void>(
            context: context,
            builder: (dialogContext) => AlertDialog(
              title: const Text('Scanner Error'),
              content: Text(message),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(dialogContext).pop();
                  },
                  child: const Text('OK'),
                ),
              ],
            ),
          );

          if (context.mounted && Navigator.of(context).canPop()) {
            context.pop();
          }
        });
      }

      next.whenOrNull(data: (pages) {
        if (previous != null && pages.isNotEmpty && context.mounted) {
          context.go('/scan-review');
        }
      });
    });

    final scanState = ref.watch(scanProvider);

    return Scaffold(
      body: Center(
        child: scanState.when(
          loading: () => const _ScannerStatusView(
            icon: Icons.document_scanner,
            title: 'Initializing scanner…',
            subtitle: 'Hold steady while we prepare edge detection.',
            showProgress: true,
          ),
          data: (pages) => _ScannerStatusView(
            icon: Icons.document_scanner_outlined,
            title: 'Ready to scan',
            subtitle: pages.isEmpty
                ? 'Tap the button below to try launching the scanner again.'
                : 'Tap below to capture more pages.',
            onRetry: () => ref.read(scanProvider.notifier).captureImage(),
          ),
          error: (error, stackTrace) => _ScannerStatusView(
            icon: Icons.error_outline,
            title: 'Unable to start scanner',
            subtitle: error is Failure
                ? error.message
                : 'An unexpected error occurred. Please try again.',
            onRetry: () => ref.read(scanProvider.notifier).captureImage(),
          ),
        ),
      ),
    );
  }
}

class _ScannerStatusView extends StatelessWidget {
  const _ScannerStatusView({
    required this.icon,
    required this.title,
    required this.subtitle,
    this.onRetry,
    this.showProgress = false,
  });

  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback? onRetry;
  final bool showProgress;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 72, color: theme.colorScheme.primary),
          const SizedBox(height: 24),
          Text(
            title,
            style: theme.textTheme.titleLarge,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 12),
          Text(
            subtitle,
            style: theme.textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          if (showProgress) ...[
            const SizedBox(height: 24),
            const CircularProgressIndicator(),
          ] else if (onRetry != null) ...[
            const SizedBox(height: 24),
            FilledButton.icon(
              onPressed: onRetry,
              icon: const Icon(Icons.play_arrow),
              label: const Text('Launch scanner'),
            ),
          ],
        ],
      ),
    );
  }
}