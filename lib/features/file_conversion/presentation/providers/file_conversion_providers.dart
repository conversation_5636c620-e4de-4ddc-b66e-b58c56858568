import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:myapp/features/file_conversion/data/datasources/camera_data_source.dart';
import 'package:myapp/features/file_conversion/data/datasources/file_picker_data_source.dart';
import 'package:myapp/features/file_conversion/data/datasources/pdf_converter_data_source.dart';
import 'package:myapp/features/file_conversion/data/datasources/camera_data_source_impl.dart';
import 'package:myapp/features/file_conversion/data/datasources/file_picker_data_source_impl.dart';
import 'package:myapp/features/file_conversion/data/datasources/pdf_converter_data_source_impl.dart';
import 'package:myapp/features/file_conversion/data/repositories/file_conversion_repository_impl.dart';
import 'package:myapp/features/file_conversion/domain/repositories/file_conversion_repository.dart';
import 'package:myapp/features/file_conversion/domain/usecases/convert_docx_to_pdf.dart';
import 'package:myapp/features/file_conversion/domain/usecases/convert_images_to_pdf.dart';
import 'package:myapp/features/file_conversion/domain/usecases/convert_ppt_to_pdf.dart';
import 'package:myapp/features/file_conversion/domain/usecases/convert_xlsx_to_pdf.dart';
import 'package:myapp/features/file_conversion/domain/usecases/scan_document.dart';

part 'file_conversion_providers.g.dart';

// Data Source Providers using @riverpod annotation
@riverpod
CameraDataSource cameraDataSource(Ref ref) {
  return CameraDataSourceImpl();
}

@riverpod
FilePickerDataSource filePickerDataSource(Ref ref) {
  return FilePickerDataSourceImpl();
}

@riverpod
PDFConverterDataSource pdfConverterDataSource(Ref ref) {
  return PDFConverterDataSourceImpl();
}

// Repository Provider
@riverpod
FileConversionRepository fileConversionRepository(Ref ref) {
  return FileConversionRepositoryImpl(
    cameraDataSource: ref.watch(cameraDataSourceProvider),
    filePickerDataSource: ref.watch(filePickerDataSourceProvider),
    pdfConverterDataSource: ref.watch(pdfConverterDataSourceProvider),
  );
}

// Use Case Providers
@riverpod
ConvertImagesToPdfUseCase convertImagesToPdf(Ref ref) {
  return ConvertImagesToPdfUseCase(ref.watch(fileConversionRepositoryProvider));
}

@riverpod
ConvertDocxToPdfUseCase convertDocxToPdf(Ref ref) {
  return ConvertDocxToPdfUseCase(ref.watch(fileConversionRepositoryProvider));
}

@riverpod
ConvertPptToPdfUseCase convertPptToPdf(Ref ref) {
  return ConvertPptToPdfUseCase(ref.watch(fileConversionRepositoryProvider));
}

@riverpod
ConvertXlsxToPdfUseCase convertXlsxToPdf(Ref ref) {
  return ConvertXlsxToPdfUseCase(ref.watch(fileConversionRepositoryProvider));
}

@riverpod
ScanDocumentUseCase scanDocument(Ref ref) {
  return ScanDocumentUseCase(ref.watch(fileConversionRepositoryProvider));
}