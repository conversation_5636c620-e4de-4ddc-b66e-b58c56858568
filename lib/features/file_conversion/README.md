# File Conversion

Overview
- Scan images from camera and convert to PDF.
- Convert Office formats (DOCX/XLSX/PPTX) to PDF when supported.

Structure
- presentation/screens: scan and conversion flows
- presentation/widgets: camera controls, page reorder, progress, overlays
- presentation/notifiers: state for scanning and conversions
- presentation/providers: Riverpod providers for DI
- domain/entities: conversion request/result, scanned page, conversion type
- domain/usecases: scan_document, convert_* use cases
- domain/repositories: `FileConversionRepository` interface
- data/datasources: camera, file picker, PDF converter implementations
- data/repositories: `FileConversionRepositoryImpl`
- data/models: models for requests, results, pages

Key Files
- presentation/screens/scan_camera_screen.dart: Capture pages
- presentation/screens/scan_review_screen.dart: Review/reorder/crop pages
- presentation/screens/image_conversion_screen.dart: Images -> PDF flow
- presentation/screens/document_conversion_screen.dart: Office -> PDF flow
- presentation/widgets/*: UI building blocks for controls and progress
- presentation/notifiers/*: Orchestrate scan and conversion actions
- domain/usecases/convert_images_to_pdf.dart and convert_*: Conversion logic entry points
- data/datasources/*: Platform integrations (camera/file picker/converter)

App Flow
- From Discover, user picks a conversion tool.
- Camera flow: open camera -> capture pages -> review/reorder -> convert -> show/save result.
- Document conversion: pick file -> run convert_* use case -> show/save result.

