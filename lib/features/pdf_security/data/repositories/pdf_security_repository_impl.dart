// features/pdf_security/data/repositories/pdf_security_repository_impl.dart

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../domain/repositories/pdf_security_repository.dart';
import '../datasources/pdf_security_local_datasource.dart';

class PdfSecurityRepositoryImpl implements PdfSecurityRepository {
  final PdfSecurityLocalDataSource dataSource;

  PdfSecurityRepositoryImpl(this.dataSource);

  @override
  Future<Either<Failure, Unit>> setPassword({
    required String path,
    required String newPassword,
  }) async {
    try {
      await dataSource.setPassword(path: path, newPassword: newPassword);
      return const Right(unit);
    } catch (e) {
      if (e.toString().contains('File not found')) {
        return Left(FileNotFoundFailure(path));
      }
      if (e.toString().contains('already password protected')) {
        return Left(const PdfAlreadyEncryptedFailure());
      }
      return Left(PdfEncryptionFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Unit>> removePassword({
    required String path,
    required String currentPassword,
  }) async {
    try {
      await dataSource.removePassword(
        path: path,
        currentPassword: currentPassword,
      );
      return const Right(unit);
    } catch (e) {
      if (e.toString().contains('File not found')) {
        return Left(FileNotFoundFailure(path));
      }
      if (e.toString().contains('not password protected')) {
        return Left(const PdfNotEncryptedFailure());
      }
      if (e.toString().contains('Invalid password')) {
        return Left(const InvalidPasswordFailure());
      }
      return Left(PdfEncryptionFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> isEncrypted(String path) async {
    try {
      final result = await dataSource.isEncrypted(path);
      return Right(result);
    } catch (e) {
      if (e.toString().contains('File not found')) {
        return Left(FileNotFoundFailure(path));
      }
      return Left(PdfEncryptionFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Unit>> validatePassword({
    required String path,
    required String password,
  }) async {
    try {
      await dataSource.validatePassword(path: path, password: password);
      return const Right(unit);
    } catch (e) {
      if (e.toString().contains('File not found')) {
        return Left(FileNotFoundFailure(path));
      }
      if (e.toString().contains('Invalid password')) {
        return Left(const InvalidPasswordFailure());
      }
      return Left(PdfEncryptionFailure(e.toString()));
    }
  }
}
