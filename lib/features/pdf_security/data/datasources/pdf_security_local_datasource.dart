// features/pdf_security/data/datasources/pdf_security_local_datasource.dart

import 'dart:io';
import 'dart:ui';
import 'package:syncfusion_flutter_pdf/pdf.dart';
import 'package:path/path.dart' as path_lib;

abstract class PdfSecurityLocalDataSource {
  Future<void> setPassword({
    required String path,
    required String newPassword,
  });

  Future<void> removePassword({
    required String path,
    required String currentPassword,
  });

  Future<bool> isEncrypted(String path);

  Future<void> validatePassword({
    required String path,
    required String password,
  });
}

class PdfSecurityLocalDataSourceImpl implements PdfSecurityLocalDataSource {
  @override
  Future<void> setPassword({
    required String path,
    required String newPassword,
  }) async {
    try {
      final file = File(path);
      
      if (!await file.exists()) {
        throw Exception('File not found: $path');
      }

      // Check if already encrypted
      final isAlreadyEncrypted = await isEncrypted(path);
      if (isAlreadyEncrypted) {
        throw Exception('PDF is already password protected');
      }

      // Read the PDF file
      final bytes = await file.readAsBytes();
      final document = PdfDocument(inputBytes: bytes);

      // Set the password using AES-256 encryption
      final security = document.security;
      security.userPassword = newPassword;
      security.ownerPassword = newPassword;
      
      // Set encryption algorithm to AES-256
      security.algorithm = PdfEncryptionAlgorithm.aesx256Bit;
      
      // Set permissions - allow all operations with correct password
      security.permissions.addAll([
        PdfPermissionsFlags.print,
        PdfPermissionsFlags.editContent,
        PdfPermissionsFlags.copyContent,
        PdfPermissionsFlags.editAnnotations,
        PdfPermissionsFlags.fillFields,
        PdfPermissionsFlags.accessibilityCopyContent,
        PdfPermissionsFlags.assembleDocument,
        PdfPermissionsFlags.fullQualityPrint,
      ]);

      // Save to a new file with -protected suffix (non-destructive)
      final directory = path_lib.dirname(path);
      final fileName = path_lib.basenameWithoutExtension(path);
      final extension = path_lib.extension(path);
      final newPath = path_lib.join(directory, '$fileName-protected$extension');

      final outputBytes = await document.save();
      document.dispose();

      final outputFile = File(newPath);
      await outputFile.writeAsBytes(outputBytes);
    } catch (e) {
      throw Exception('Failed to set PDF password: ${e.toString()}');
    }
  }

  @override
  Future<void> removePassword({
    required String path,
    required String currentPassword,
  }) async {
    try {
      final file = File(path);
      
      if (!await file.exists()) {
        throw Exception('File not found: $path');
      }

      // Check if encrypted
      final encrypted = await isEncrypted(path);
      if (!encrypted) {
        throw Exception('PDF is not password protected');
      }

      // Read the PDF file with password
      final bytes = await file.readAsBytes();
      final document = PdfDocument(
        inputBytes: bytes,
        password: currentPassword,
      );

      // Check if password is correct by trying to access pages
      try {
        // This will throw if password is wrong
        final _ = document.pages.count;
      } catch (e) {
        document.dispose();
        throw Exception('Invalid password provided');
      }

      // Remove encryption by saving without security
      // Create a new document without encryption
      final newDocument = PdfDocument();
      
      // Copy all pages
      for (int i = 0; i < document.pages.count; i++) {
        final page = document.pages[i];
        newDocument.pages.add().graphics.drawPdfTemplate(
          page.createTemplate(),
          const Offset(0, 0),
        );
      }

      // Save to a new file with -unprotected suffix
      final directory = path_lib.dirname(path);
      final fileName = path_lib.basenameWithoutExtension(path);
      final extension = path_lib.extension(path);
      final newPath = path_lib.join(directory, '$fileName-unprotected$extension');

      final outputBytes = await newDocument.save();
      document.dispose();
      newDocument.dispose();

      final outputFile = File(newPath);
      await outputFile.writeAsBytes(outputBytes);
    } catch (e) {
      if (e.toString().contains('Invalid password')) {
        rethrow;
      }
      throw Exception('Failed to remove PDF password: ${e.toString()}');
    }
  }

  @override
  Future<bool> isEncrypted(String path) async {
    try {
      final file = File(path);
      
      if (!await file.exists()) {
        throw Exception('File not found: $path');
      }

      final bytes = await file.readAsBytes();
      
      // Try to load without password
      try {
        final document = PdfDocument(inputBytes: bytes);
        // If we can access pages count without error, it's not encrypted
        final _ = document.pages.count;
        document.dispose();
        return false;
      } catch (e) {
        // If we get an exception about password, it's encrypted
        if (e.toString().contains('password') || 
            e.toString().contains('encrypted') ||
            e.toString().contains('protected')) {
          return true;
        }
        // Other errors mean file might be corrupted
        rethrow;
      }
    } catch (e) {
      throw Exception('Failed to check PDF encryption status: ${e.toString()}');
    }
  }

  @override
  Future<void> validatePassword({
    required String path,
    required String password,
  }) async {
    try {
      final file = File(path);
      
      if (!await file.exists()) {
        throw Exception('File not found: $path');
      }

      final bytes = await file.readAsBytes();
      final document = PdfDocument(
        inputBytes: bytes,
        password: password,
      );

      try {
        // This will throw if password is wrong
        final _ = document.pages.count;
        document.dispose();
      } catch (e) {
        document.dispose();
        throw Exception('Invalid password provided');
      }
    } catch (e) {
      if (e.toString().contains('Invalid password')) {
        rethrow;
      }
      throw Exception('Failed to validate PDF password: ${e.toString()}');
    }
  }
}
