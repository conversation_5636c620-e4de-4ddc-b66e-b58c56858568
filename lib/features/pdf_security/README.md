# PDF Security

Overview
- Password-related operations for PDFs: check encryption, validate password, set or remove password.

Structure
- presentation/widgets: `PasswordDialog` for entering passwords
- presentation/notifiers: `PdfSecurityNotifier` for state and actions
- domain/entities: `PdfSecurityStatus`
- domain/usecases: is_pdf_encrypted, validate_pdf_password, set_pdf_password, remove_pdf_password
- domain/repositories: `PdfSecurityRepository` interface
- data/datasources: local datasource implementing encryption ops
- data/repositories: `PdfSecurityRepositoryImpl`

Key Files
- presentation/widgets/password_dialog.dart: UI for password input
- presentation/notifiers/pdf_security_notifier.dart: State and orchestration
- domain/usecases/*.dart: Security operations
- data/datasources/pdf_security_local_datasource.dart: Implementation hooks
- data/repositories/pdf_security_repository_impl.dart: Repo implementation

App Flow
- When opening a PDF, use `is_pdf_encrypted`; if encrypted, prompt `PasswordDialog` and validate with `validate_pdf_password`.
- Users may set or remove password via the security tools flow.

