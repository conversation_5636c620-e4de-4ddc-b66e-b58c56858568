// features/pdf_security/presentation/notifiers/pdf_security_notifier.dart

import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../data/datasources/pdf_security_local_datasource.dart';
import '../../data/repositories/pdf_security_repository_impl.dart';
import '../../domain/repositories/pdf_security_repository.dart';
import '../../domain/usecases/is_pdf_encrypted.dart';
import '../../domain/usecases/remove_pdf_password.dart';
import '../../domain/usecases/set_pdf_password.dart';
import '../../domain/usecases/validate_pdf_password.dart';

part 'pdf_security_notifier.g.dart';

/// Provider for PdfSecurityLocalDataSource
@riverpod
PdfSecurityLocalDataSource pdfSecurityLocalDataSource(Ref ref) {
  return PdfSecurityLocalDataSourceImpl();
}

/// Provider for PdfSecurityRepository
@riverpod
PdfSecurityRepository pdfSecurityRepository(Ref ref) {
  final dataSource = ref.watch(pdfSecurityLocalDataSourceProvider);
  return PdfSecurityRepositoryImpl(dataSource);
}

/// Provider for SetPdfPassword use case
@riverpod
SetPdfPassword setPdfPassword(Ref ref) {
  final repository = ref.watch(pdfSecurityRepositoryProvider);
  return SetPdfPassword(repository);
}

/// Provider for RemovePdfPassword use case
@riverpod
RemovePdfPassword removePdfPassword(Ref ref) {
  final repository = ref.watch(pdfSecurityRepositoryProvider);
  return RemovePdfPassword(repository);
}

/// Provider for IsPdfEncrypted use case
@riverpod
IsPdfEncrypted isPdfEncrypted(Ref ref) {
  final repository = ref.watch(pdfSecurityRepositoryProvider);
  return IsPdfEncrypted(repository);
}

/// Provider for ValidatePdfPassword use case
@riverpod
ValidatePdfPassword validatePdfPassword(Ref ref) {
  final repository = ref.watch(pdfSecurityRepositoryProvider);
  return ValidatePdfPassword(repository);
}

/// Notifier for PDF security operations
@riverpod
class PdfSecurityNotifier extends _$PdfSecurityNotifier {
  @override
  FutureOr<void> build() {
    // No initial state needed
  }

  Future<void> setPassword({
    required String path,
    required String newPassword,
  }) async {
    state = const AsyncValue.loading();

    final useCase = ref.watch(setPdfPasswordProvider);
    final result = await useCase(path: path, newPassword: newPassword);

    state = await AsyncValue.guard(() async {
      return result.fold((failure) => throw failure, (_) => null);
    });
  }

  Future<void> removePassword({
    required String path,
    required String currentPassword,
  }) async {
    state = const AsyncValue.loading();

    final useCase = ref.watch(removePdfPasswordProvider);
    final result = await useCase(path: path, currentPassword: currentPassword);

    state = await AsyncValue.guard(() async {
      return result.fold((failure) => throw failure, (_) => null);
    });
  }

  Future<bool> isEncrypted(String path) async {
    final useCase = ref.watch(isPdfEncryptedProvider);
    final result = await useCase(path);

    return result.fold(
      (failure) => throw failure,
      (isEncrypted) => isEncrypted,
    );
  }

  Future<void> validatePassword({
    required String path,
    required String password,
  }) async {
    final useCase = ref.watch(validatePdfPasswordProvider);
    final result = await useCase(path: path, password: password);

    return result.fold((failure) => throw failure, (_) => null);
  }
}
