// features/pdf_security/domain/repositories/pdf_security_repository.dart

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';

abstract class PdfSecurityRepository {
  Future<Either<Failure, Unit>> setPassword({
    required String path,
    required String newPassword,
  });

  Future<Either<Failure, Unit>> removePassword({
    required String path,
    required String currentPassword,
  });

  Future<Either<Failure, bool>> isEncrypted(String path);

  Future<Either<Failure, Unit>> validatePassword({
    required String path,
    required String password,
  });
}
