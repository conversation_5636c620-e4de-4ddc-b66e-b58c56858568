// features/pdf_security/domain/usecases/validate_pdf_password.dart

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../repositories/pdf_security_repository.dart';

class ValidatePdfPassword {
  final PdfSecurityRepository repository;

  ValidatePdfPassword(this.repository);

  Future<Either<Failure, Unit>> call({
    required String path,
    required String password,
  }) {
    return repository.validatePassword(path: path, password: password);
  }
}
