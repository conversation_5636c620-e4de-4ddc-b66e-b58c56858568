// features/pdf_security/domain/usecases/set_pdf_password.dart

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../repositories/pdf_security_repository.dart';

class SetPdfPassword {
  final PdfSecurityRepository repository;

  SetPdfPassword(this.repository);

  Future<Either<Failure, Unit>> call({
    required String path,
    required String newPassword,
  }) {
    return repository.setPassword(path: path, newPassword: newPassword);
  }
}
