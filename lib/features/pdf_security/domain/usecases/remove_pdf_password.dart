// features/pdf_security/domain/usecases/remove_pdf_password.dart

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../repositories/pdf_security_repository.dart';

class RemovePdfPassword {
  final PdfSecurityRepository repository;

  RemovePdfPassword(this.repository);

  Future<Either<Failure, Unit>> call({
    required String path,
    required String currentPassword,
  }) {
    return repository.removePassword(path: path, currentPassword: currentPassword);
  }
}
