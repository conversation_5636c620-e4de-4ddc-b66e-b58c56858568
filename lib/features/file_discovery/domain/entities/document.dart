// features/file_discovery/domain/entities/document.dart

import 'package:freezed_annotation/freezed_annotation.dart';

part 'document.freezed.dart';

@freezed
abstract class Document with _$Document {
  const factory Document({
    required String id,              // Unique identifier (URI-based)
    required String name,            // File name with extension
    required String path,            // Full file path/URI
    required DocumentType type,      // File type enum
    required int sizeInBytes,        // File size in bytes
    required DateTime dateModified,  // Last modified date
  }) = _Document;
}

enum DocumentType {
  pdf,
  docx,
  xlsx,
  pptx,
  unknown;

  String get displayName {
    switch (this) {
      case DocumentType.pdf:
        return 'PDF';
      case DocumentType.docx:
        return 'WORD';
      case DocumentType.xlsx:
        return 'EXCEL';
      case DocumentType.pptx:
        return 'PPT';
      case DocumentType.unknown:
        return 'UNKNOWN';
    }
  }

  String get fileExtension {
    switch (this) {
      case DocumentType.pdf:
        return '.pdf';
      case DocumentType.docx:
        return '.docx';
      case DocumentType.xlsx:
        return '.xlsx';
      case DocumentType.pptx:
        return '.pptx';
      case DocumentType.unknown:
        return '';
    }
  }
}