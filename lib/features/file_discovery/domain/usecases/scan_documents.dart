// features/file_discovery/domain/usecases/scan_documents.dart

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/document.dart';
import '../repositories/document_repository.dart';

class ScanDocuments {
  final DocumentRepository repository;

  ScanDocuments(this.repository);

  Future<Either<Failure, List<Document>>> call() {
    return repository.scanDocuments();
  }
}