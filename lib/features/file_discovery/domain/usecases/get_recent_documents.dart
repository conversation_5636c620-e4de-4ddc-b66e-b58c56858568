// features/file_discovery/domain/usecases/get_recent_documents.dart

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/document.dart';
import '../repositories/document_repository.dart';

class GetRecentDocuments {
  final DocumentRepository repository;

  GetRecentDocuments(this.repository);

  Future<Either<Failure, List<Document>>> call() {
    return repository.getRecentDocuments();
  }
}