// features/file_discovery/domain/usecases/add_to_recent.dart

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/document.dart';
import '../repositories/document_repository.dart';

class AddToRecent {
  final DocumentRepository repository;

  AddToRecent(this.repository);

  Future<Either<Failure, Unit>> call(Document document) async {
    return await repository.addToRecent(document);
  }
}