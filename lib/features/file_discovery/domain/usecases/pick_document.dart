// features/file_discovery/domain/usecases/pick_document.dart

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/document.dart';
import '../repositories/document_repository.dart';

class PickDocument {
  final DocumentRepository repository;

  PickDocument(this.repository);

  Future<Either<Failure, Document>> call() async {
    return await repository.pickDocument();
  }
}