// features/file_discovery/domain/repositories/document_repository.dart

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/document.dart';

abstract class DocumentRepository {
  /// Scans the device storage for compatible document files
  Future<Either<Failure, List<Document>>> scanDocuments();

  /// Allows the user to manually pick a document file
  Future<Either<Failure, Document>> pickDocument();

  /// Gets recently opened documents
  Future<Either<Failure, List<Document>>> getRecentDocuments();

  /// Adds a document to the recently opened list
  Future<Either<Failure, Unit>> addToRecent(Document document);
}