// features/file_discovery/data/datasources/file_system_datasource_impl.dart

import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import 'package:file_picker/file_picker.dart';
import 'package:path_provider/path_provider.dart';
import '../../../../core/constants/file_types.dart';
import '../../domain/entities/document.dart';
import '../models/document_model.dart';
import '../utils/file_scanner.dart' as scanner;
import '../../../../core/utils/performance_utils.dart';

abstract class FileSystemDataSource {
  /// Scans the device storage for compatible document files
  Future<List<DocumentModel>> scanDocuments();

  /// Allows the user to manually pick a document file
  Future<DocumentModel?> pickDocument();

  /// Gets recently opened documents from local storage
  Future<List<DocumentModel>> getRecentDocuments();

  /// Adds a document to the recently opened list
  Future<void> addToRecent(DocumentModel document);
}

class FileSystemDataSourceImpl implements FileSystemDataSource {
  static const String _recentDocsFile = 'recent_documents.json';
  static bool _fullScanNext = false;

  static void requestFullScanOnce() {
    _fullScanNext = true;
  }
  
  @override
  Future<List<DocumentModel>> scanDocuments() async {
    final candidatePaths = <String>[
      '/storage/emulated/0',
      '/storage/emulated/0/Download',
      '/storage/emulated/0/Documents',
      '/storage/emulated/0/Document',
      '/storage/emulated/0/WhatsApp/Media/WhatsApp Documents',
      '/storage/emulated/0/Telegram/Telegram Documents',
    ];

    // Filter roots that exist to minimize work
    final roots = <String>[];
    for (final pth in candidatePaths) {
      final dir = Directory(pth);
      if (await dir.exists()) roots.add(pth);
    }

    if (roots.isEmpty) return [];

    // Run scanning in background (wrapped in timeout). For tests, this wrapper
    // executes in the same isolate but still enforces the timeout.
    final List<Map<String, dynamic>> raw = await measurePerformance('File Scan', () async {
      // Prefer Flutter's compute when available
      if (!kIsWeb) {
        try {
          // Top-level entry that calls the pure scanner util
          return await computeWithTimeout<List<Map<String, dynamic>>, List<String>>(
            _scanPathsEntry,
            roots,
            timeout: const Duration(seconds: 20),
          );
        } catch (_) {
          // Fallback to direct call on failure
          return await scanner.scanPaths(roots);
        }
      }
      // Web or other platforms: fallback
      return await scanner.scanPaths(roots);
    });

    // Optional: batch limit to avoid overwhelming UI with huge lists (initial load)
    List<Map<String, dynamic>> limited = raw;
    if (!_fullScanNext) {
      limited = raw.length > 100 ? raw.sublist(0, 100) : raw;
    }
    _fullScanNext = false;

    // Map to models off the main isolate when possible
    try {
      final models = await computeWithTimeout<List<DocumentModel>, List<Map<String, dynamic>>>(
        _toModelsEntry,
        limited,
        timeout: const Duration(seconds: 10),
      );
      return models;
    } catch (_) {
      return limited.map((m) => DocumentModel.fromJson(m)).toList(growable: false);
    }
  }

  @override
  Future<DocumentModel?> pickDocument() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: FileTypeConstants.supportedExtensions
            .map((ext) => ext.substring(1)) // Remove the dot from extensions
            .toList(),
      );

      if (result != null && result.files.single.path != null) {
        final filePath = result.files.single.path!;
        final documentType = _getDocumentTypeFromPath(filePath);

        // Use file picker result data when available to avoid blocking stat() call
        final sizeInBytes = result.files.single.size ?? 0;

        return DocumentModel(
          id: filePath,
          name: path.basename(filePath),
          path: filePath,
          type: documentType.name,
          sizeInBytes: sizeInBytes,
          dateModifiedMillis: DateTime.now().millisecondsSinceEpoch,
        );
      }
    } catch (e) {
      // Handle any errors during file picking
    }
    
    return null;
  }

  @override
  Future<List<DocumentModel>> getRecentDocuments() async {
    try {
      final dir = await getApplicationDocumentsDirectory();
      final file = File(path.join(dir.path, _recentDocsFile));

      if (!await file.exists()) {
        return [];
      }

      final contents = await file.readAsString();
      if (contents.trim().isEmpty) {
        return [];
      }

      final List<dynamic> jsonList = jsonDecode(contents) as List<dynamic>;
      return jsonList
          .whereType<Map<String, dynamic>>()
          .map(DocumentModel.fromJson)
          .toList();
    } catch (_) {
      // Gracefully degrade to empty list on any error
      return [];
    }
  }

  @override
  Future<void> addToRecent(DocumentModel document) async {
    try {
      final dir = await getApplicationDocumentsDirectory();
      final file = File(path.join(dir.path, _recentDocsFile));

      List<DocumentModel> existing = [];
      if (await file.exists()) {
        final contents = await file.readAsString();
        if (contents.trim().isNotEmpty) {
          final List<dynamic> jsonList = jsonDecode(contents) as List<dynamic>;
          existing = jsonList
              .whereType<Map<String, dynamic>>()
              .map(DocumentModel.fromJson)
              .toList();
        }
      }

      // Remove any existing entry with same id, then insert at start
      existing.removeWhere((d) => d.id == document.id);
      existing.insert(0, document);

      // Cap list size to 20 items
      if (existing.length > 20) {
        existing = existing.sublist(0, 20);
      }

      final encoded = jsonEncode(existing.map((e) => e.toJson()).toList());
      await file.writeAsString(encoded, flush: true);
    } catch (_) {
      // Ignore persisting errors in MVP
    }
  }

  /// Scans a directory and returns all documents with supported extensions
  Future<List<DocumentModel>> _scanDirectory(Directory dir) async {
    final documents = <DocumentModel>[];
    try {
      await for (final file in _getFilesRecursively(dir)) {
        if (FileTypeConstants.supportedExtensions.contains(path.extension(file.path).toLowerCase())) {
          final stat = await file.stat();
          final documentType = _getDocumentTypeFromPath(file.path);

          final documentModel = DocumentModel(
            id: file.path,
            name: path.basename(file.path),
            path: file.path,
            type: documentType.name,
            sizeInBytes: stat.size,
            dateModifiedMillis: stat.modified.millisecondsSinceEpoch,
          );

          documents.add(documentModel);
        }
      }
    } catch (_) {
      // Ignore; return what we found so far
    }
    return documents;
  }

  /// Recursively gets all files from a directory
  Stream<File> _getFilesRecursively(Directory root) async* {
    final queue = <FileSystemEntity>[root];
    
    while (queue.isNotEmpty) {
      final current = queue.removeAt(0);
      
      if (current is Directory) {
        try {
          await for (final entity in current.list()) {
            if (entity is File) {
              yield entity;
            } else if (entity is Directory) {
              // Check if directory is accessible before adding to queue
              if (await entity.exists()) {
                queue.add(entity);
              }
            }
          }
        } catch (e) {
          // Skip directories that can't be accessed due to permissions
        }
      } else if (current is File) {
        yield current;
      }
    }
  }

  /// Determines the document type from a file path
  DocumentType _getDocumentTypeFromPath(String filePath) {
    final extension = path.extension(filePath).toLowerCase();
    
    switch (extension) {
      case '.pdf':
        return DocumentType.pdf;
      case '.docx':
        return DocumentType.docx;
      case '.xlsx':
        return DocumentType.xlsx;
      case '.pptx':
        return DocumentType.pptx;
      default:
        return DocumentType.unknown;
    }
  }
}

// Top-level entry function for isolates: accepts a list of root paths and
// returns a serializable list of document maps.
Future<List<Map<String, dynamic>>> _scanPathsEntry(List<String> roots) async {
  return await scanner.scanPaths(roots);
}

List<DocumentModel> _toModelsEntry(List<Map<String, dynamic>> raw) {
  return raw.map((m) => DocumentModel.fromJson(m)).toList(growable: false);
}
