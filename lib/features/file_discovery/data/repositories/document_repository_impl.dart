// features/file_discovery/data/repositories/document_repository_impl.dart

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../domain/entities/document.dart';
import '../../domain/repositories/document_repository.dart';
import '../datasources/file_system_datasource_impl.dart';
import '../models/document_model.dart';

class DocumentRepositoryImpl implements DocumentRepository {
  final FileSystemDataSource dataSource;

  DocumentRepositoryImpl(this.dataSource);

  @override
  Future<Either<Failure, List<Document>>> scanDocuments() async {
    try {
      final documentModels = await dataSource.scanDocuments();
      final documents = documentModels.map((model) => model.toDomain()).toList();
      return Right(documents);
    } catch (e) {
      return Left(FileSystemFailure('Failed to scan documents: $e'));
    }
  }

  @override
  Future<Either<Failure, Document>> pickDocument() async {
    try {
      final documentModel = await dataSource.pickDocument();
      if (documentModel != null) {
        return Right(documentModel.toDomain());
      } else {
        return Left(FileSystemFailure('No document selected'));
      }
    } catch (e) {
      return Left(FileSystemFailure('Failed to pick document: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Document>>> getRecentDocuments() async {
    try {
      final documentModels = await dataSource.getRecentDocuments();
      final documents = documentModels.map((model) => model.toDomain()).toList();
      return Right(documents);
    } catch (e) {
      return Left(FileSystemFailure('Failed to get recent documents: $e'));
    }
  }

  @override
  Future<Either<Failure, Unit>> addToRecent(Document document) async {
    try {
      final documentModel = DocumentModel.fromDomain(document);
      await dataSource.addToRecent(documentModel);
      return const Right(unit);
    } catch (e) {
      return Left(FileSystemFailure('Failed to add document to recent: $e'));
    }
  }
}