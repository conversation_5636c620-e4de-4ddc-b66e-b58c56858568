// features/file_discovery/data/utils/file_scanner.dart

import 'dart:async';
import 'dart:io';
import 'package:path/path.dart' as p;
import '../../../../core/constants/file_types.dart';

/// Scans the given root directory paths and returns a list of serializable
/// maps describing supported documents.
///
/// The returned map shape:
/// { id, name, path, type, sizeInBytes, dateModifiedMillis }
Future<List<Map<String, dynamic>>> scanPaths(List<String> roots) async {
  final results = <Map<String, dynamic>>[];

  for (final rootPath in roots) {
    final root = Directory(rootPath);
    if (!await root.exists()) continue;

    final queue = <FileSystemEntity>[root];
    while (queue.isNotEmpty) {
      final current = queue.removeAt(0);

      if (current is Directory) {
        // Skip hidden directories
        final base = p.basename(current.path);
        if (base.startsWith('.')) continue;

        try {
          await for (final entity in current.list(followLinks: false)) {
            if (entity is File) {
              final ext = p.extension(entity.path).toLowerCase();
              if (!FileTypeConstants.isSupportedExtension(ext)) continue;

              try {
                final stat = await entity.stat();
                results.add({
                  'id': entity.path,
                  'name': p.basename(entity.path),
                  'path': entity.path,
                  'type': _typeFromExtension(ext),
                  'sizeInBytes': stat.size,
                  'dateModifiedMillis': stat.modified.millisecondsSinceEpoch,
                });
              } catch (_) {
                // Ignore stat errors; continue scanning
              }
            } else if (entity is Directory) {
              // Avoid enqueuing hidden directories
              final childBase = p.basename(entity.path);
              if (!childBase.startsWith('.')) {
                queue.add(entity);
              }
            }
          }
        } catch (_) {
          // Permission or IO error: skip this directory
        }
      }
    }
  }

  return results;
}

String _typeFromExtension(String ext) {
  switch (ext) {
    case '.pdf':
      return 'pdf';
    case '.docx':
      return 'docx';
    case '.xlsx':
      return 'xlsx';
    case '.pptx':
      return 'pptx';
    default:
      return 'unknown';
  }
}

