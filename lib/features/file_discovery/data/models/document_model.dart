// features/file_discovery/data/models/document_model.dart

import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/document.dart';

part 'document_model.freezed.dart';
part 'document_model.g.dart';

@freezed
abstract class DocumentModel with _$DocumentModel {
  const DocumentModel._();

  const factory DocumentModel({
    required String id,
    required String name,
    required String path,
    required String type,           // String for JSON serialization
    required int sizeInBytes,
    required int dateModifiedMillis, // Unix timestamp
  }) = _DocumentModel;

  factory DocumentModel.fromJson(Map<String, dynamic> json) =>
      _$DocumentModelFromJson(json);

  // Conversion to domain entity
  Document toDomain() {
    return Document(
      id: id,
      name: name,
      path: path,
      type: _parseDocumentType(type),
      sizeInBytes: sizeInBytes,
      dateModified: DateTime.fromMillisecondsSinceEpoch(dateModifiedMillis),
    );
  }

  // Factory from domain entity
  factory DocumentModel.fromDomain(Document document) {
    return DocumentModel(
      id: document.id,
      name: document.name,
      path: document.path,
      type: document.type.name,
      sizeInBytes: document.sizeInBytes,
      dateModifiedMillis: document.dateModified.millisecondsSinceEpoch,
    );
  }

  static DocumentType _parseDocumentType(String type) {
    switch (type.toLowerCase()) {
      case 'pdf':
        return DocumentType.pdf;
      case 'docx':
        return DocumentType.docx;
      case 'xlsx':
        return DocumentType.xlsx;
      case 'pptx':
        return DocumentType.pptx;
      default:
        return DocumentType.unknown;
    }
  }
}