# File Discovery

Overview
- Discover documents on device via storage scan (with permission) or via manual system picker.
- Home screen presents tabs and lists of documents, including recents.

Structure
- presentation/screens: `HomeScreen` entry
- presentation/widgets: list item, tabs, permission request
- presentation/notifiers: `DocumentListNotifier` for listing state
- presentation/providers: DI and wiring for discovery
- domain/entities: `Document`
- domain/usecases: scan_documents, get_recent_documents, add_to_recent, pick_document
- domain/repositories: `DocumentRepository` interface
- data/datasources: file system scanning (`FileSystemDatasource` + impl)
- data/repositories: `DocumentRepositoryImpl`
- data/models: `DocumentModel`
- data/utils: `FileScanner`

Key Files
- presentation/screens/home_screen.dart: Main discovery UI
- presentation/widgets/document_list_item.dart: Renders a file row
- presentation/widgets/file_type_tabs.dart: Filters by type
- presentation/widgets/permission_request_widget.dart: Handles permission UX
- presentation/notifiers/document_list_notifier.dart: Orchestrates scans and selections
- domain/usecases/*.dart: Discovery actions
- data/datasources/file_system_datasource_impl.dart: Scanning implementation

App Flow
- On start, if permission granted, run `scan_documents`; else prompt via `permission_request_widget`.
- Display results by type in tabs; include recent documents.
- Selecting a document navigates to the appropriate viewer route with file path/name.

