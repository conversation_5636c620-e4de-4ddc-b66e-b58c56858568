// features/file_discovery/presentation/widgets/file_type_tabs.dart

import 'package:flutter/material.dart';

import '../../domain/entities/document.dart';

class FileTypeTabs extends StatefulWidget {
  final DocumentType selectedType;
  final ValueChanged<DocumentType> onTypeSelected;

  const FileTypeTabs({
    super.key,
    required this.selectedType,
    required this.onTypeSelected,
  });

  @override
  State<FileTypeTabs> createState() => _FileTypeTabsState();
}

class _FileTypeTabsState extends State<FileTypeTabs> {
  static const _types = <DocumentType>[
    DocumentType.unknown,
    DocumentType.pdf,
    DocumentType.docx,
    DocumentType.xlsx,
    DocumentType.pptx,
  ];

  final _scrollController = ScrollController();
  final GlobalKey _containerKey = GlobalKey();
  late final Map<DocumentType, GlobalKey> _tabKeys;

  double _indicatorLeft = 0;
  double _indicatorWidth = 0;
  bool _indicatorReady = false;

  @override
  void initState() {
    super.initState();
    _tabKeys = {
      for (final type in _types) type: GlobalKey(),
    };
    WidgetsBinding.instance.addPostFrameCallback((_) => _updateIndicator());
  }

  @override
  void didUpdateWidget(covariant FileTypeTabs oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.selectedType != widget.selectedType) {
      WidgetsBinding.instance.addPostFrameCallback((_) => _updateIndicator());
    }
  }

  void _updateIndicator() {
    if (!mounted) return;

    final containerContext = _containerKey.currentContext;
    final containerBox = containerContext?.findRenderObject() as RenderBox?;
    final tabKey = _tabKeys[widget.selectedType];
    final tabContext = tabKey?.currentContext;
    final tabBox = tabContext?.findRenderObject() as RenderBox?;

    if (containerBox == null || tabBox == null) {
      return;
    }

    final position = tabBox.localToGlobal(Offset.zero, ancestor: containerBox);
    final newLeft = position.dx;
    final newWidth = tabBox.size.width;

    setState(() {
      _indicatorLeft = newLeft;
      _indicatorWidth = newWidth;
      _indicatorReady = true;
    });

    if (!_scrollController.hasClients) return;

    final tabGlobalLeft = tabBox.localToGlobal(Offset.zero).dx;
    final tabGlobalRight = tabGlobalLeft + newWidth;
    final containerGlobalLeft = containerBox.localToGlobal(Offset.zero).dx;
    final containerGlobalRight = containerGlobalLeft + containerBox.size.width;

    double? targetOffset;
    if (tabGlobalLeft < containerGlobalLeft) {
      targetOffset = _scrollController.offset -
          (containerGlobalLeft - tabGlobalLeft) -
          24;
    } else if (tabGlobalRight > containerGlobalRight) {
      targetOffset = _scrollController.offset +
          (tabGlobalRight - containerGlobalRight) +
          24;
    }

    if (targetOffset != null && _scrollController.hasClients) {
      final position = _scrollController.position;
      final clamped = targetOffset.clamp(
        position.minScrollExtent,
        position.maxScrollExtent,
      );
      _scrollController.animateTo(
        clamped,
        duration: const Duration(milliseconds: 260),
        curve: Curves.easeOutCubic,
      );
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return SizedBox(
      height: 56,
      child: Padding(
        padding: const EdgeInsets.fromLTRB(16, 8, 16, 0),
        child: Container(
          key: _containerKey,
          alignment: Alignment.bottomLeft,
          child: Stack(
            clipBehavior: Clip.none,
            children: [
              ListView.separated(
                controller: _scrollController,
                scrollDirection: Axis.horizontal,
                physics: const BouncingScrollPhysics(),
                padding: const EdgeInsets.only(bottom: 12),
                itemBuilder: (context, index) {
                  final type = _types[index];
                  return _buildTab(context, type);
                },
                separatorBuilder: (context, _) => const SizedBox(width: 16),
                itemCount: _types.length,
              ),
              if (_indicatorReady)
                AnimatedPositioned(
                  duration: const Duration(milliseconds: 260),
                  curve: Curves.easeOutCubic,
                  bottom: 0,
                  left: _indicatorLeft,
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 260),
                    curve: Curves.easeOutCubic,
                    width: _indicatorWidth,
                    height: 3,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTab(BuildContext context, DocumentType type) {
    final isSelected = widget.selectedType == type;
    final label = type == DocumentType.unknown ? 'ALL' : type.displayName;
    final theme = Theme.of(context);

    final labelStyle = theme.textTheme.titleMedium?.copyWith(
      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
      color: isSelected
          ? theme.colorScheme.primary
          : theme.colorScheme.onSurfaceVariant,
    );

    return Container(
      key: _tabKeys[type],
      alignment: Alignment.center,
      child: FilterChip(
        showCheckmark: false,
        backgroundColor: Colors.transparent,
        selectedColor: Colors.transparent,
        side: BorderSide.none,
        labelStyle: labelStyle,
        labelPadding: EdgeInsets.zero,
        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
        visualDensity: VisualDensity.compact,
        label: Text(label),
        selected: isSelected,
        onSelected: (_) => widget.onTypeSelected(type),
      ),
    );
  }
}