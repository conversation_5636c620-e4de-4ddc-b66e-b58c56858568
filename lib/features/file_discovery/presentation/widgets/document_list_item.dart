// features/file_discovery/presentation/widgets/document_list_item.dart

import 'package:flutter/material.dart';
import '../../domain/entities/document.dart';
import '../../../../core/utils/file_size_formatter.dart';
import '../../../../core/utils/date_formatter.dart';
import '../../../file_actions/presentation/widgets/document_actions_button.dart';

class DocumentListItem extends StatelessWidget {
  final Document document;
  final VoidCallback onTap;

  const DocumentListItem({
    super.key,
    required this.document,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    IconData icon;
    Color iconColor;

    switch (document.type) {
      case DocumentType.pdf:
        icon = Icons.picture_as_pdf;
        iconColor = Colors.red;
        break;
      case DocumentType.docx:
        icon = Icons.description;
        iconColor = Colors.blue;
        break;
      case DocumentType.xlsx:
        icon = Icons.table_chart;
        iconColor = Colors.green;
        break;
      case DocumentType.pptx:
        icon = Icons.slideshow;
        iconColor = Colors.orange;
        break;
      case DocumentType.unknown:
        icon = Icons.insert_drive_file;
        iconColor = Colors.grey;
        break;
    }

    return RepaintBoundary(
      child: Card(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        child: ListTile(
          dense: true,
          leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: iconColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: iconColor),
        ),
          title: Text(document.name, overflow: TextOverflow.ellipsis),
          subtitle: DefaultTextStyle(
            style: Theme.of(context).textTheme.bodySmall ?? const TextStyle(),
            child: Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                Flexible(
                  child: Text(
                    document.type.displayName,
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
                const SizedBox(width: 8),
                Text(FileSizeFormatter.formatFileSize(document.sizeInBytes)),
                const SizedBox(width: 8),
                Text(DateFormatter.formatCompactDate(document.dateModified)),
              ],
            ),
          ),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              DocumentActionsButton.fromDocument(document),
              const Icon(Icons.chevron_right),
            ],
          ),
          onTap: onTap,
        ),
      ),
    );
  }
}
