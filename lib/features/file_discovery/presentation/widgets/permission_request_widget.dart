// features/file_discovery/presentation/widgets/permission_request_widget.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class PermissionRequestWidget extends ConsumerWidget {
  final VoidCallback onPermissionRequested;

  const PermissionRequestWidget({
    super.key,
    required this.onPermissionRequested,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return LayoutBuilder(
      builder: (context, constraints) {
  final compact = constraints.maxHeight < 320;
  final padding = compact ? const EdgeInsets.all(16) : const EdgeInsets.symmetric(horizontal: 24, vertical: 32);
  final iconSize = compact ? 72.0 : 96.0;
  final spacingLg = compact ? 20.0 : 28.0;
  final spacingMd = compact ? 12.0 : 16.0;

        final theme = Theme.of(context);
        final colorScheme = theme.colorScheme;

        return Container(
          padding: padding,
          alignment: Alignment.center,
          child: SingleChildScrollView(
            child: Center(
              child: ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 360),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      width: iconSize,
                      height: iconSize,
                      decoration: BoxDecoration(
                        color: colorScheme.primary.withValues(alpha: 0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.assignment_ind,
                        size: iconSize * 0.6,
                        color: colorScheme.primary,
                      ),
                    ),
                    SizedBox(height: spacingLg),
                    Text(
                      'To read and edit your files, please allow All PDF Reader to access all your files.',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        height: 1.3,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: spacingMd),
                    Text(
                      'Granting access lets us scan and sync your documents while keeping everything secure on your device.',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                        height: 1.4,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: spacingLg),
                    SizedBox(
                      width: double.infinity,
                      child: FilledButton(
                        onPressed: onPermissionRequested,
                        child: const Text('Allow'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
