// features/file_discovery/presentation/providers/file_discovery_providers.dart

import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/datasources/file_system_datasource_impl.dart';
import '../../data/repositories/document_repository_impl.dart';
import '../../domain/repositories/document_repository.dart';
import '../../domain/usecases/scan_documents.dart';
import '../../domain/usecases/pick_document.dart';
import '../../domain/usecases/get_recent_documents.dart';
import '../../domain/usecases/add_to_recent.dart';
import '../../domain/entities/document.dart';
import '../notifiers/document_list_notifier.dart';

part 'file_discovery_providers.g.dart';

@riverpod
FileSystemDataSource fileSystemDataSource(Ref ref) {
  return FileSystemDataSourceImpl();
}

@riverpod
DocumentRepository documentRepository(Ref ref) {
  final dataSource = ref.watch(fileSystemDataSourceProvider);
  return DocumentRepositoryImpl(dataSource);
}

@riverpod
ScanDocuments scanDocuments(Ref ref) {
  final repository = ref.watch(documentRepositoryProvider);
  return ScanDocuments(repository);
}

@riverpod
PickDocument pickDocument(Ref ref) {
  final repository = ref.watch(documentRepositoryProvider);
  return PickDocument(repository);
}

@riverpod
GetRecentDocuments getRecentDocuments(Ref ref) {
  final repository = ref.watch(documentRepositoryProvider);
  return GetRecentDocuments(repository);
}

@riverpod
AddToRecent addToRecent(Ref ref) {
  final repository = ref.watch(documentRepositoryProvider);
  return AddToRecent(repository);
}

final recentDocumentsProvider = FutureProvider<List<Document>>((ref) async {
  final getRecent = ref.watch(getRecentDocumentsProvider);
  final result = await getRecent();

  return result.fold(
    (failure) => throw failure,
    (documents) => documents,
  );
});

final filteredDocumentListProvider = Provider.family<List<Document>, DocumentType>(
  (ref, type) {
    final docsAsync = ref.watch(documentListProvider);
    final docs = docsAsync.value ?? const <Document>[];
    if (type == DocumentType.unknown) return docs;
    return docs.where((d) => d.type == type).toList(growable: false);
  },
);
