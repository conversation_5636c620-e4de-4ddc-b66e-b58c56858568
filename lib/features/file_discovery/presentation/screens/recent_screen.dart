import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/widgets/error_display.dart';
import '../../../../core/widgets/loading_indicator.dart';
import '../../domain/entities/document.dart';
import '../providers/file_discovery_providers.dart';
import '../widgets/document_list_item.dart';

class RecentScreen extends ConsumerWidget {
  const RecentScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // TODO: Recent documents leverages existing repository caching once storage is populated.
    final recentDocuments = ref.watch(recentDocumentsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Recent'),
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          final _ = await ref.refresh(recentDocumentsProvider.future);
        },
        child: recentDocuments.when(
          loading: () => ListView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 48),
            children: const [
              Center(
                child: LoadingIndicator(message: 'Loading recent documents...'),
              ),
            ],
          ),
          error: (error, stack) => ListView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 48),
            children: [
              Center(
                child: ErrorDisplay(
                  failure: error as Failure,
                  onRetry: () {
                    ref.invalidate(recentDocumentsProvider);
                  },
                ),
              ),
            ],
          ),
          data: (documents) {
            if (documents.isEmpty) {
              // TODO: Surface dynamically when recent documents feature gains data.
              return ListView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 64),
                children: [
                  Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.history,
                          size: 80,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        const SizedBox(height: 24),
                        Text(
                          'No recent documents',
                          style: Theme.of(context).textTheme.headlineSmall,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Open files to see them appear here automatically.',
                          style: Theme.of(context).textTheme.bodyMedium,
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ],
              );
            }

            return ListView.builder(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: const EdgeInsets.only(top: 8, bottom: 32),
              itemCount: documents.length,
              itemExtent: 72,
              itemBuilder: (context, index) {
                final document = documents[index];
                return DocumentListItem(
                  document: document,
                  onTap: () => _openDocument(context, document),
                );
              },
            );
          },
        ),
      ),
    );
  }
}

void _openDocument(BuildContext context, Document document) {
  // TODO: Extract to shared navigation helper once other screens rely on it.
  String routePath;
  switch (document.type) {
    case DocumentType.pdf:
      routePath = '/viewer/pdf/${Uri.encodeComponent(document.path)}';
      break;
    case DocumentType.docx:
      routePath = '/viewer/docx/${Uri.encodeComponent(document.path)}';
      break;
    case DocumentType.xlsx:
      routePath = '/viewer/xlsx/${Uri.encodeComponent(document.path)}';
      break;
    case DocumentType.pptx:
      routePath = '/viewer/pptx/${Uri.encodeComponent(document.path)}';
      break;
    case DocumentType.unknown:
      return;
  }

  routePath += '?name=${Uri.encodeComponent(document.name)}';
  context.push(routePath);
}
