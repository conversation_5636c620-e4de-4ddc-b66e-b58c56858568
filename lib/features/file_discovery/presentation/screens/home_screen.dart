// features/file_discovery/presentation/screens/home_screen.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/widgets/ad_banner_placeholder.dart';
import '../../../../core/widgets/error_display.dart';
import '../../../../core/widgets/loading_indicator.dart';
import '../../../../core/error/failures.dart';
import '../notifiers/document_list_notifier.dart';
import '../../../permissions/presentation/notifiers/permission_notifier.dart';
import '../widgets/document_list_item.dart';
import '../widgets/file_manager_banner.dart';
import '../widgets/file_type_tabs.dart';
import '../widgets/permission_request_widget.dart';
import '../../domain/entities/document.dart';
import '../providers/file_discovery_providers.dart';
import '../../../file_conversion/presentation/widgets/create_convert_bottom_sheet.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen>
    with AutomaticKeepAliveClientMixin<HomeScreen> {
  DocumentType _selectedType = DocumentType.unknown; // Unknown means ALL

  bool _scanInitialized = false;

  void _maybeStartScanAfterFirstFrame() {
    if (_scanInitialized) return;
    _scanInitialized = true;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(documentListProvider.notifier).refresh();
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final permissionState = ref.watch(permissionProvider);
    final permissionsContent = permissionState.maybeWhen(
      data: (hasPermission) {
        if (!hasPermission) {
          return PermissionRequestWidget(
            onPermissionRequested: () async {
              final granted = await ref
                  .read(permissionProvider.notifier)
                  .requestPermission();
              if (granted) {
                ref.read(documentListProvider.notifier).refresh();
              }
            },
          );
        }
        _maybeStartScanAfterFirstFrame();
        return _buildDocumentList();
      },
      orElse: () => const LoadingIndicator(
        message: 'Checking permissions...',
      ),
    );

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.menu),
          onPressed: () {},
        ),
        title: const Text('PDF READER'),
        actions: [
          IconButton(
            icon: const Icon(Icons.workspace_premium),
            onPressed: () {},
          ),
        ],
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          FileTypeTabs(
            selectedType: _selectedType,
            onTypeSelected: (type) {
              setState(() {
                _selectedType = type;
              });
            },
          ),
          FileManagerBanner(
            onGoPressed: _selectFileAndNavigate,
          ),
          Expanded(child: permissionsContent),
          const SafeArea(
            top: false,
            child: AdBannerPlaceholder(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showCreateConvertBottomSheet,
        tooltip: 'Create or Convert PDF',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildDocumentList() {
    final documentState = _scanInitialized
        ? ref.watch(documentListProvider)
        : const AsyncValue<List<Document>>.data(<Document>[]);
    return RefreshIndicator(
      onRefresh: () {
        return ref.read(documentListProvider.notifier).refresh();
      },
      child: documentState.when(
        loading: () => ListView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 48),
          children: [
            Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const LoadingIndicator(
                    message: 'Scanning documents...',
                  ),
                  const SizedBox(height: 16),
                  OutlinedButton.icon(
                    onPressed: () {
                      ref.read(documentListProvider.notifier).cancelScan();
                    },
                    icon: const Icon(Icons.close),
                    label: const Text('Cancel scan'),
                  ),
                ],
              ),
            ),
          ],
        ),
        error: (error, stack) => ListView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 48),
          children: [
            Center(
              child: ErrorDisplay(
                failure: error as Failure,
                onRetry: () {
                  ref.read(documentListProvider.notifier).refresh();
                },
              ),
            ),
          ],
        ),
        data: (documents) {
          final filteredDocuments = ref.watch(
            filteredDocumentListProvider(_selectedType),
          );

          if (filteredDocuments.isEmpty) {
            return ListView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 64),
              children: [
                Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.document_scanner,
                        size: 80,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(height: 24),
                      Text(
                        'No documents found',
                        style: Theme.of(context).textTheme.headlineSmall,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Select a file or grant permission to scan',
                        style: Theme.of(context).textTheme.bodyMedium,
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ],
            );
          }

          return ListView.builder(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: const EdgeInsets.only(top: 8, bottom: 32),
            itemCount: filteredDocuments.length,
            itemExtent: 72.0,
            itemBuilder: (context, index) {
              final document = filteredDocuments[index];
              return DocumentListItem(
                document: document,
                onTap: () => _openDocument(document),
              );
            },
          );
        },
      ),
    );
  }

  void _selectFileAndNavigate() async {
    try {
      final document = await ref
          .read(documentListProvider.notifier)
          .pickDocument();
      
      if (!mounted) return;
      if (document != null) {
        _openDocument(document);
      }
    } catch (e) {
      if (!mounted) return;
      // Handle error in document selection
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error selecting document: $e'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    }
  }

  void _openDocument(Document document) {
    String routePath;
    switch (document.type) {
      case DocumentType.pdf:
        routePath = '/viewer/pdf/${Uri.encodeComponent(document.path)}';
        break;
      case DocumentType.docx:
        routePath = '/viewer/docx/${Uri.encodeComponent(document.path)}';
        break;
      case DocumentType.xlsx:
        routePath = '/viewer/xlsx/${Uri.encodeComponent(document.path)}';
        break;
      case DocumentType.pptx:
        routePath = '/viewer/pptx/${Uri.encodeComponent(document.path)}';
        break;
      default:
        return; // Unsupported type
    }

    // Add document name as query parameter
    routePath += '?name=${Uri.encodeComponent(document.name)}';

    context.push(routePath);
  }

  void _showCreateConvertBottomSheet() {
    final theme = Theme.of(context);
    showModalBottomSheet<void>(
      context: context,
      isScrollControlled: false,
      backgroundColor: theme.colorScheme.surface,
      elevation: 8,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(20),
        ),
      ),
      builder: (context) => const CreateConvertBottomSheet(),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
