// features/discover/domain/usecases/get_discover_tools.dart

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/tool_entry.dart';

/// Use case to get the list of available tools for the Discover screen.
/// For MVP, this returns a static list of tools.
class GetDiscoverTools {
  /// Returns a list of tool entries.
  /// This is a synchronous operation that returns static data.
  Either<Failure, List<ToolEntry>> call() {
    try {
      final tools = [
        const ToolEntry(
          id: 'scan',
          title: 'Scan Document',
          iconName: 'camera',
          route: '/scan-document',
          enabled: true,
          description: 'Scan documents and convert to PDF',
        ),
        const ToolEntry(
          id: 'convert-images',
          title: 'Convert Images',
          iconName: 'image',
          route: '/convert-images',
          enabled: true,
          description: 'Convert multiple images to PDF',
        ),
        const ToolEntry(
          id: 'convert-docx',
          title: 'DOCX to PDF',
          iconName: 'description',
          route: '/convert-document?type=docxToPdf',
          enabled: true,
          description: 'Convert Word documents to PDF',
        ),
        const ToolEntry(
          id: 'convert-ppt',
          title: 'PPT to PDF',
          iconName: 'slideshow',
          route: '/convert-document?type=pptToPdf',
          enabled: true,
          description: 'Convert PowerPoint to PDF',
        ),
        const ToolEntry(
          id: 'convert-xlsx',
          title: 'XLSX to PDF',
          iconName: 'table_chart',
          route: '/convert-document?type=xlsxToPdf',
          enabled: true,
          description: 'Convert Excel spreadsheets to PDF',
        ),
        const ToolEntry(
          id: 'merge',
          title: 'Merge PDFs',
          iconName: 'merge_type',
          route: '/merge-pdf',
          enabled: true,
          description: 'Combine multiple PDF files into one',
        ),
        const ToolEntry(
          id: 'split',
          title: 'Split PDF',
          iconName: 'call_split',
          route: '/split-pdf',
          enabled: true,
          description: 'Split a PDF into multiple documents',
        ),
        const ToolEntry(
          id: 'manage-pages',
          title: 'Manage Pages',
          iconName: 'grid_view',
          route: '/manage-pages',
          enabled: true,
          description: 'Reorder, rotate, and remove PDF pages',
        ),
        const ToolEntry(
          id: 'extract-pages',
          title: 'Extract Pages',
          iconName: 'file_download',
          route: '/extract-pages',
          enabled: true,
          description: 'Extract selected pages into a new PDF',
        ),
        const ToolEntry(
          id: 'ai',
          title: 'AI Assistant',
          iconName: 'psychology',
          route: '/ai',
          enabled: false,
          description: 'Translate, summarize, and analyze documents',
        ),
      ];
      
      return Right(tools);
    } catch (e) {
      return Left(FileSystemFailure('Failed to load tools: ${e.toString()}'));
    }
  }
}
