// features/discover/domain/entities/tool_entry.dart

import 'package:freezed_annotation/freezed_annotation.dart';

part 'tool_entry.freezed.dart';

@freezed
abstract class ToolEntry with _$ToolEntry {
  const factory ToolEntry({
    required String id,
    required String title,
    required String iconName,
    required String route,
    required bool enabled,
    String? description,
  }) = _ToolEntry;
}
