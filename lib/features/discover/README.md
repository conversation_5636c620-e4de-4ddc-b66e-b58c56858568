# Discover

Overview
- Entry point for discovery of available tools/features. Presents a grid/list of tools and navigates into the corresponding flows.

Structure
- presentation/screens: `DiscoverScreen` renders the tools page
- presentation/widgets: `ToolCard` displays a single tool
- presentation/notifiers: `DiscoverToolsNotifier` loads/filters tools
- domain/entities: `ToolEntry` represents a tool
- domain/usecases: `GetDiscoverTools` provides the list of tools

Key Files
- presentation/screens/discover_screen.dart: UI screen showing all tools
- presentation/widgets/tool_card.dart: Card widget for a tool entry
- presentation/notifiers/discover_tools_notifier.dart: State for tool list
- domain/usecases/get_discover_tools.dart: Fetches tool entries
- domain/entities/tool_entry.dart: Model for tools displayed

App Flow
- App navigates to `DiscoverScreen` via route `/discover`
- Notifier loads tools via `GetDiscoverTools`
- User taps a `ToolCard` to navigate to the feature flow (e.g., conversion, page management)

