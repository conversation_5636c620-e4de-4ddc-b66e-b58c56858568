// features/discover/presentation/notifiers/discover_tools_notifier.dart

import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../domain/entities/tool_entry.dart';
import '../../domain/usecases/get_discover_tools.dart';

part 'discover_tools_notifier.g.dart';

/// Provider for GetDiscoverTools use case
@riverpod
GetDiscoverTools getDiscoverTools(Ref ref) {
  return GetDiscoverTools();
}

/// Notifier that manages the state of discover tools
@riverpod
class DiscoverToolsNotifier extends _$DiscoverToolsNotifier {
  @override
  Future<List<ToolEntry>> build() async {
    ref.keepAlive();
    final getTools = ref.watch(getDiscoverToolsProvider);
    final result = getTools();

    return result.fold((failure) => throw failure, (tools) => tools);
  }
}
