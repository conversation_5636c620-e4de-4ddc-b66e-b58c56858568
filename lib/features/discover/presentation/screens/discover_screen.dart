// features/discover/presentation/screens/discover_screen.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/widgets/error_display.dart';
import '../../../../core/widgets/loading_indicator.dart';
import '../notifiers/discover_tools_notifier.dart';
import '../widgets/tool_card.dart';

class DiscoverScreen extends ConsumerWidget {
  const DiscoverScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final toolsState = ref.watch(discoverToolsProvider);

    return Scaffold(
      appBar: AppBar(title: const Text('Discover')),
      body: toolsState.when(
        data: (tools) {
          if (tools.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.explore_off,
                    size: 64,
                    color: Theme.of(context).colorScheme.outline,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No tools available',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Check back soon for new features',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            );
          }

          // Use simpler GridView.builder for better performance
          return GridView.builder(
            padding: const EdgeInsets.all(16),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 1.0,
            ),
            itemCount: tools.length,
            itemBuilder: (context, index) {
              final tool = tools[index];
              return ToolCard(
                key: ValueKey(tool.id),
                tool: tool,
                onTap: () {
                  if (tool.enabled) {
                    context.go(tool.route);
                  } else {
                    _showComingSoonSheet(context, tool.title);
                  }
                },
              );
            },
          );
        },
        loading: () => const LoadingIndicator(),
        error: (error, stack) {
          final failure = _mapError(error);
          return ErrorDisplay(
            failure: failure,
            onRetry: () {
              ref.invalidate(discoverToolsProvider);
            },
          );
        },
      ),
    );
  }

  void _showComingSoonSheet(BuildContext context, String toolName) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.hourglass_empty,
              size: 48,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 16),
            Text('Coming Soon', style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 8),
            Text(
              '$toolName will be available in a future update.',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            FilledButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Got it'),
            ),
          ],
        ),
      ),
    );
  }

  Failure _mapError(Object error) {
    if (error is Failure) {
      return error;
    }
    return FileSystemFailure(error.toString());
  }
}
