// features/discover/presentation/widgets/tool_card.dart

import 'package:flutter/material.dart';
import '../../domain/entities/tool_entry.dart';

class ToolCard extends StatelessWidget {
  final ToolEntry tool;
  final VoidCallback onTap;

  const ToolCard({
    super.key,
    required this.tool,
    required this.onTap,
  });

  static IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'camera':
        return Icons.document_scanner;
      case 'image':
        return Icons.image;
      case 'description':
        return Icons.description;
      case 'slideshow':
        return Icons.slideshow;
      case 'table_chart':
        return Icons.table_chart;
      case 'merge_type':
        return Icons.merge_type;
      case 'call_split':
        return Icons.call_split;
      case 'grid_view':
        return Icons.grid_view;
      case 'file_download':
        return Icons.file_download;
      case 'transform':
        return Icons.transform;
      case 'psychology':
        return Icons.psychology;
      default:
        return Icons.apps;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Wrap in RepaintBoundary to isolate repaints
    return RepaintBoundary(
      child: Card(
        elevation: tool.enabled ? 2 : 1,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  _getIconData(tool.iconName),
                  size: 40.0,
                  color: tool.enabled
                      ? colorScheme.primary
                      : colorScheme.onSurface.withValues(alpha: 0.38),
                ),
                const SizedBox(height: 8),
                Flexible(
                  child: Text(
                    tool.title,
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: tool.enabled
                          ? colorScheme.onSurface
                          : colorScheme.onSurface.withValues(alpha: 0.38),
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                if (tool.description != null) ...[
                  const SizedBox(height: 4),
                  Flexible(
                    child: Text(
                      tool.description!,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
                if (!tool.enabled) ...[
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: colorScheme.surfaceContainerHighest,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      'Coming Soon',
                      style: theme.textTheme.labelSmall?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}
