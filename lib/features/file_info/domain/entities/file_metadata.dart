// features/file_info/domain/entities/file_metadata.dart

import 'package:freezed_annotation/freezed_annotation.dart';

part 'file_metadata.freezed.dart';

@freezed
abstract class FileMetadata with _$FileMetadata {
  const factory FileMetadata({
    required String path,
    required String name,
    required int sizeBytes,
    required DateTime createdAt,
    required DateTime modifiedAt,
    required String mime,
    String? author,
    required bool isEncrypted,
  }) = _FileMetadata;
}
