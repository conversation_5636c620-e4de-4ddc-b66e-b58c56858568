// features/file_info/domain/usecases/get_file_metadata.dart

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/file_metadata.dart';
import '../repositories/file_metadata_repository.dart';

class GetFileMetadata {
  final FileMetadataRepository repository;

  GetFileMetadata(this.repository);

  Future<Either<Failure, FileMetadata>> call(String path) {
    return repository.getMetadata(path);
  }
}
