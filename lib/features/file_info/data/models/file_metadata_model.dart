// features/file_info/data/models/file_metadata_model.dart

import '../../domain/entities/file_metadata.dart';

class FileMetadataModel {
  final String path;
  final String name;
  final int sizeBytes;
  final DateTime createdAt;
  final DateTime modifiedAt;
  final String mime;
  final String? author;
  final bool isEncrypted;

  const FileMetadataModel({
    required this.path,
    required this.name,
    required this.sizeBytes,
    required this.createdAt,
    required this.modifiedAt,
    required this.mime,
    this.author,
    required this.isEncrypted,
  });

  FileMetadata toEntity() {
    return FileMetadata(
      path: path,
      name: name,
      sizeBytes: sizeBytes,
      createdAt: createdAt,
      modifiedAt: modifiedAt,
      mime: mime,
      author: author,
      isEncrypted: isEncrypted,
    );
  }
}
