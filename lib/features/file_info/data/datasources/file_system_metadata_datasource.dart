// features/file_info/data/datasources/file_system_metadata_datasource.dart

import 'dart:io';
import 'package:path/path.dart' as path_lib;
import 'package:syncfusion_flutter_pdf/pdf.dart';
import '../models/file_metadata_model.dart';

abstract class FileSystemMetadataDataSource {
  Future<FileMetadataModel> getMetadata(String filePath);
}

class FileSystemMetadataDataSourceImpl implements FileSystemMetadataDataSource {
  @override
  Future<FileMetadataModel> getMetadata(String filePath) async {
    try {
      final file = File(filePath);

      if (!await file.exists()) {
        throw Exception('File not found: $filePath');
      }

      final stat = await file.stat();
      final fileName = path_lib.basename(filePath);
      final extension = path_lib.extension(filePath).toLowerCase();

      final mimeType = _resolveMimeType(extension);
      final metadata = await _readAdditionalMetadata(
        file: file,
        extension: extension,
      );

      return FileMetadataModel(
        path: filePath,
        name: fileName,
        sizeBytes: stat.size,
        createdAt: stat.changed,
        modifiedAt: stat.modified,
        mime: mimeType,
        author: metadata.author,
        isEncrypted: metadata.isEncrypted,
      );
    } on FileSystemException {
      rethrow;
    } catch (e) {
      throw Exception('Failed to read file metadata: ${e.toString()}');
    }
  }

  String _resolveMimeType(String extension) {
    switch (extension) {
      case '.pdf':
        return 'application/pdf';
      case '.docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      case '.xlsx':
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      case '.pptx':
        return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
      default:
        return 'application/octet-stream';
    }
  }

  Future<_PdfMetadata> _readAdditionalMetadata({
    required File file,
    required String extension,
  }) async {
    if (extension != '.pdf') {
      return const _PdfMetadata(author: null, isEncrypted: false);
    }

    try {
      final bytes = await file.readAsBytes();
      final document = PdfDocument(inputBytes: bytes);
      final author = document.documentInformation.author;
      document.dispose();
      return _PdfMetadata(
        author: author == null || author.isEmpty ? null : author,
        isEncrypted: false,
      );
    } catch (e) {
      if (_isPdfPasswordError(e)) {
        return const _PdfMetadata(author: null, isEncrypted: true);
      }
      rethrow;
    }
  }

  bool _isPdfPasswordError(Object error) {
    final message = error.toString().toLowerCase();
    return message.contains('password') ||
        message.contains('encrypted') ||
        message.contains('protect');
  }
}

class _PdfMetadata {
  final String? author;
  final bool isEncrypted;

  const _PdfMetadata({required this.author, required this.isEncrypted});
}
