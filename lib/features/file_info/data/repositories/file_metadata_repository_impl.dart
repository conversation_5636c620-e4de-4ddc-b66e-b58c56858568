// features/file_info/data/repositories/file_metadata_repository_impl.dart

import 'dart:io';

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../domain/entities/file_metadata.dart';
import '../../domain/repositories/file_metadata_repository.dart';
import '../datasources/file_system_metadata_datasource.dart';

class FileMetadataRepositoryImpl implements FileMetadataRepository {
  final FileSystemMetadataDataSource dataSource;

  FileMetadataRepositoryImpl(this.dataSource);

  @override
  Future<Either<Failure, FileMetadata>> getMetadata(String path) async {
    try {
      final metadata = await dataSource.getMetadata(path);
      return Right(metadata.toEntity());
    } on FileSystemException catch (e) {
      return Left(FileSystemFailure(e.message));
    } catch (e) {
      if (e.toString().contains('File not found')) {
        return Left(FileNotFoundFailure(path));
      }
      return Left(MetadataReadFailure(e.toString()));
    }
  }
}
