// features/file_info/presentation/screens/file_info_screen.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/utils/file_size_formatter.dart';
import '../../../../core/widgets/error_display.dart';
import '../../../../core/widgets/loading_indicator.dart';
import '../../domain/entities/file_metadata.dart';
import '../notifiers/file_info_notifier.dart';

class FileInfoScreen extends ConsumerWidget {
  final String filePath;

  const FileInfoScreen({super.key, required this.filePath});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final metadataState = ref.watch(fileInfoProvider(filePath));

    return Scaffold(
      appBar: AppBar(
        title: const Text('File Information'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              ref.read(fileInfoProvider(filePath).notifier).refresh();
            },
          ),
        ],
      ),
      body: metadataState.when(
        data: (metadata) => _buildMetadataView(context, metadata),
        loading: () => const LoadingIndicator(),
        error: (error, stack) {
          final failure = _mapError(error);
          return ErrorDisplay(
            failure: failure,
            onRetry: () {
              ref.read(fileInfoProvider(filePath).notifier).refresh();
            },
          );
        },
      ),
    );
  }

  Widget _buildMetadataView(BuildContext context, FileMetadata metadata) {
    final theme = Theme.of(context);
    final dateFormat = DateFormat('MMM dd, yyyy HH:mm');

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildInfoCard(
          context,
          icon: Icons.insert_drive_file,
          title: 'File Name',
          value: metadata.name,
        ),
        const SizedBox(height: 12),
        _buildInfoCard(
          context,
          icon: Icons.folder,
          title: 'Path',
          value: metadata.path,
        ),
        const SizedBox(height: 12),
        _buildInfoCard(
          context,
          icon: Icons.storage,
          title: 'Size',
          value: FileSizeFormatter.formatFileSize(metadata.sizeBytes),
        ),
        const SizedBox(height: 12),
        _buildInfoCard(
          context,
          icon: Icons.description,
          title: 'Type',
          value: metadata.mime,
        ),
        const SizedBox(height: 12),
        _buildInfoCard(
          context,
          icon: Icons.calendar_today,
          title: 'Modified',
          value: dateFormat.format(metadata.modifiedAt),
        ),
        const SizedBox(height: 12),
        _buildInfoCard(
          context,
          icon: Icons.access_time,
          title: 'Created',
          value: dateFormat.format(metadata.createdAt),
        ),
        if (metadata.author != null) ...[
          const SizedBox(height: 12),
          _buildInfoCard(
            context,
            icon: Icons.person,
            title: 'Author',
            value: metadata.author!,
          ),
        ],
        const SizedBox(height: 12),
        _buildInfoCard(
          context,
          icon: metadata.isEncrypted ? Icons.lock : Icons.lock_open,
          title: 'Security',
          value: metadata.isEncrypted ? 'Password Protected' : 'Not Protected',
          valueColor: metadata.isEncrypted
              ? theme.colorScheme.primary
              : theme.colorScheme.onSurfaceVariant,
        ),
      ],
    );
  }

  Widget _buildInfoCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String value,
    Color? valueColor,
  }) {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(icon, size: 24, color: theme.colorScheme.primary),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.labelMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    value,
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: valueColor ?? theme.colorScheme.onSurface,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Failure _mapError(Object error) {
    if (error is Failure) {
      return error;
    }
    return MetadataReadFailure(error.toString());
  }
}
