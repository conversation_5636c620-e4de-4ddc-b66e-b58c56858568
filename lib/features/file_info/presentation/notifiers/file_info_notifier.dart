// features/file_info/presentation/notifiers/file_info_notifier.dart

import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../data/datasources/file_system_metadata_datasource.dart';
import '../../data/repositories/file_metadata_repository_impl.dart';
import '../../domain/entities/file_metadata.dart';
import '../../domain/repositories/file_metadata_repository.dart';
import '../../domain/usecases/get_file_metadata.dart';

part 'file_info_notifier.g.dart';

/// Provider for FileSystemMetadataDataSource
@riverpod
FileSystemMetadataDataSource fileSystemMetadataDataSource(Ref ref) {
  return FileSystemMetadataDataSourceImpl();
}

/// Provider for FileMetadataRepository
@riverpod
FileMetadataRepository fileMetadataRepository(Ref ref) {
  final dataSource = ref.watch(fileSystemMetadataDataSourceProvider);
  return FileMetadataRepositoryImpl(dataSource);
}

/// Provider for GetFileMetadata use case
@riverpod
GetFileMetadata getFileMetadata(Ref ref) {
  final repository = ref.watch(fileMetadataRepositoryProvider);
  return GetFileMetadata(repository);
}

/// Notifier that manages file metadata state
@riverpod
class FileInfoNotifier extends _$FileInfoNotifier {
  @override
  Future<FileMetadata> build(String filePath) async {
    final getMetadata = ref.watch(getFileMetadataProvider);
    final result = await getMetadata(filePath);

    return result.fold((failure) => throw failure, (metadata) => metadata);
  }

  /// Refresh the file metadata
  Future<void> refresh() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final getMetadata = ref.watch(getFileMetadataProvider);
      final result = await getMetadata(filePath);

      return result.fold((failure) => throw failure, (metadata) => metadata);
    });
  }
}
