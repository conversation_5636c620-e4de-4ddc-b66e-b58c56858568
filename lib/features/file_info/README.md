# File Info

Overview
- Display metadata for a selected file (name, size, type, modified time, etc.).

Structure
- presentation/screens: `FileInfoScreen`
- presentation/notifiers: `FileInfoNotifier`
- domain/entities: `FileMetadata`
- domain/usecases: `GetFileMetadata`
- domain/repositories: `FileMetadataRepository` interface
- data/datasources: file system metadata data source
- data/repositories: `FileMetadataRepositoryImpl`
- data/models: `FileMetadataModel`

Key Files
- presentation/screens/file_info_screen.dart: Metadata UI
- presentation/notifiers/file_info_notifier.dart: State and loading
- domain/usecases/get_file_metadata.dart: Use case to fetch metadata
- data/datasources/file_system_metadata_datasource.dart: Low-level metadata reads
- data/repositories/file_metadata_repository_impl.dart: Repository implementation

App Flow
- Invoked from the action sheet or other entry.
- Notifier triggers `GetFileMetadata` for the selected path; renders results in `FileInfoScreen`.

