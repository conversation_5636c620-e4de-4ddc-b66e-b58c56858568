import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:myapp/features/pdf_page_management/presentation/notifiers/merge_pdf_notifier.dart';
import 'package:myapp/features/file_discovery/domain/entities/document.dart';

class MergePdfScreen extends ConsumerWidget {
  const MergePdfScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mergeState = ref.watch(mergePdfProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Merge PDFs'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              final doc = Document(
                id: DateTime.now().toIso8601String(),
                name: 'New Document',
                path: '/fake/path',
                type: DocumentType.pdf,
                sizeInBytes: 100,
                dateModified: DateTime.now(),
              );
              ref.read(mergePdfProvider.notifier).addFile(doc);
            },
          )
        ],
      ),
      body: mergeState.when(
        data: (selectedFiles) {
          final canMerge = selectedFiles.length >= 2;
          return Column(
            children: [
              Expanded(
                child: selectedFiles.isEmpty
                    ? const Center(child: Text('Select at least two PDFs to merge.'))
                    : ListView.builder(
                        itemCount: selectedFiles.length,
                        itemBuilder: (context, index) {
                          final file = selectedFiles[index];
                          return ListTile(
                            title: Text(file.name),
                            trailing: IconButton(
                              icon: const Icon(Icons.remove_circle),
                              onPressed: () {
                                ref.read(mergePdfProvider.notifier).removeFile(file);
                              },
                            ),
                          );
                        },
                      ),
              ),
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: ElevatedButton(
                  onPressed: canMerge ? () {
                    ref.read(mergePdfProvider.notifier).mergeFiles();
                  } : null,
                  child: const Text('Merge'),
                ),
              ),
            ],
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(child: Text('Error: $error')),
      ),
    );
  }
}
