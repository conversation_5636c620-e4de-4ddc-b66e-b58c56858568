import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:myapp/features/pdf_page_management/presentation/notifiers/split_pdf_notifier.dart';
import 'package:myapp/features/pdf_page_management/presentation/widgets/split_range_input.dart';

class SplitPdfScreen extends ConsumerWidget {
  const SplitPdfScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final pageRange = ref.watch(splitPdfProvider);
    final formKey = GlobalKey<FormState>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Split PDF'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: formKey,
          child: Column(
            children: [
              SplitRangeInput(
                onChanged: (value) {
                  ref.read(splitPdfProvider.notifier).setPageRange(value);
                },
              ),
              const SizedBox(height: 20),
              ElevatedButton(
                onPressed: pageRange.isEmpty
                    ? null
                    : () {
                        if (formKey.currentState?.validate() ?? false) {
                          // TODO: Implement split logic
                        }
                      },
                child: const Text('Split'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
