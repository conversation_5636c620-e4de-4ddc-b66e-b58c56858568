import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:myapp/features/pdf_page_management/presentation/notifiers/extract_pdf_notifier.dart';
import 'package:myapp/features/pdf_page_management/presentation/widgets/page_thumbnail_grid.dart';

class ExtractPdfScreen extends ConsumerWidget {
  const ExtractPdfScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedPages = ref.watch(extractPdfProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Extract Pages'),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: () {
              // TODO: Implement extract logic
            },
          ),
        ],
      ),
      body: PageThumbnailGrid(
        pageCount: 10, // Dummy data
        thumbnailBuilder: (context, index) {
          return Container(
            color: Colors.grey[300],
            child: Center(
              child: Text('Page ${index + 1}'),
            ),
          );
        },
      ),
    );
  }
}
