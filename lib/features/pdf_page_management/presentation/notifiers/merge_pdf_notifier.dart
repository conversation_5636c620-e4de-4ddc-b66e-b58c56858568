import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:myapp/features/file_discovery/domain/entities/document.dart';

part 'merge_pdf_notifier.g.dart';

@riverpod
class MergePdfNotifier extends _$MergePdfNotifier {
  @override
  Future<List<Document>> build() async {
    return [];
  }

  void addFile(Document file) {
    state = AsyncData([...state.value!, file]);
  }

  void removeFile(Document file) {
    state = AsyncData(state.value!.where((element) => element.id != file.id).toList());
  }

  Future<void> mergeFiles() async {
    state = const AsyncLoading();
    // In a real app, you would call the use case here.
    await Future.delayed(const Duration(seconds: 2));
    // For now, we just reset the state
    state = const AsyncData([]);
  }
}
