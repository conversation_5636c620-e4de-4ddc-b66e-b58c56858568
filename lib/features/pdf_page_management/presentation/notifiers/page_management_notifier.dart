import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:myapp/features/pdf_page_management/domain/entities/page_selection.dart';

part 'page_management_notifier.g.dart';

@riverpod
class PageManagementNotifier extends _$PageManagementNotifier {
  @override
  List<PageEditAction> build() {
    return [];
  }

  void addEdit(PageEditAction edit) {
    state = [...state, edit];
  }

  void removeEdit(PageEditAction edit) {
    state = state.where((element) => element.pageIndex != edit.pageIndex).toList();
  }
}
