import 'package:flutter/material.dart';

class SplitRangeInput extends StatelessWidget {
  final ValueChanged<String> onChanged;

  const SplitRangeInput({super.key, required this.onChanged});

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      onChanged: onChanged,
      decoration: const InputDecoration(
        labelText: 'Page range (e.g., 1-3,5)',
        border: OutlineInputBorder(),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'Cannot be empty';
        }
        final pageRange = value.trim();
        if (!RegExp(r'^\\d+(-\\d+)?(,\\d+(-\\d+)?)*$').hasMatch(pageRange)) {
          return 'Invalid format';
        }
        return null;
      },
      autovalidateMode: AutovalidateMode.onUserInteraction,
    );
  }
}
