import 'package:flutter/material.dart';

typedef ThumbnailBuilder = Widget Function(BuildContext context, int index);

class PageThumbnailGrid extends StatelessWidget {
  final int pageCount;
  final ThumbnailBuilder thumbnailBuilder;

  const PageThumbnailGrid({
    super.key,
    required this.pageCount,
    required this.thumbnailBuilder,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 8.0,
        mainAxisSpacing: 8.0,
      ),
      itemCount: pageCount,
      itemBuilder: (context, index) {
        return thumbnailBuilder(context, index);
      },
    );
  }
}
