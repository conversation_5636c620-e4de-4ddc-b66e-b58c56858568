import 'package:dartz/dartz.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/file_discovery/domain/entities/document.dart';
import 'package:myapp/features/pdf_page_management/domain/repositories/pdf_page_repository.dart';
import 'package:myapp/features/pdf_page_management/domain/entities/pdf_operation_result.dart';

class SplitPdfUseCase {
  final PdfPageRepository repository;

  SplitPdfUseCase(this.repository);

  Future<Either<Failure, PdfOperationResult>> call(SplitPdfParams params) async {
    final pageRange = params.pageRange.trim();
    if (pageRange.isEmpty) {
      return Left(const FileValidationFailure('Page range cannot be empty'));
    }
    return await repository.splitPdf(params.document, pageRange);
  }
}

class SplitPdfParams {
  final Document document;
  final String pageRange;

  SplitPdfParams({required this.document, required this.pageRange});
}
