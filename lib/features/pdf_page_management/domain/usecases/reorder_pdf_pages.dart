import 'package:dartz/dartz.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/file_discovery/domain/entities/document.dart';
import 'package:myapp/features/pdf_page_management/domain/repositories/pdf_page_repository.dart';
import 'package:myapp/features/pdf_page_management/domain/entities/pdf_operation_result.dart';
import 'package:myapp/features/pdf_page_management/domain/entities/page_selection.dart';

class ReorderPdfPagesUseCase {
  final PdfPageRepository repository;

  ReorderPdfPagesUseCase(this.repository);

  Future<Either<Failure, PdfOperationResult>> call(ReorderPdfPagesParams params) async {
    return await repository.reorderPages(params.document, params.edits);
  }
}

class ReorderPdfPagesParams {
  final Document document;
  final List<PageEditAction> edits;

  ReorderPdfPagesParams({required this.document, required this.edits});
}
