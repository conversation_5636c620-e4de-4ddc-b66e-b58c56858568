import 'package:dartz/dartz.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/file_discovery/domain/entities/document.dart';
import 'package:myapp/features/pdf_page_management/domain/repositories/pdf_page_repository.dart';
import 'package:myapp/features/pdf_page_management/domain/entities/pdf_operation_result.dart';

class ExtractPdfPagesUseCase {
  final PdfPageRepository repository;

  ExtractPdfPagesUseCase(this.repository);

  Future<Either<Failure, PdfOperationResult>> call(ExtractPdfPagesParams params) async {
    return await repository.extractPages(params.document, params.pageIndices);
  }
}

class ExtractPdfPagesParams {
  final Document document;
  final List<int> pageIndices;

  ExtractPdfPagesParams({required this.document, required this.pageIndices});
}
