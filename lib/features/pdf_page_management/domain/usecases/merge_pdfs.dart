import 'package:dartz/dartz.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/file_discovery/domain/entities/document.dart';
import 'package:myapp/features/pdf_page_management/domain/repositories/pdf_page_repository.dart';
import 'package:myapp/features/pdf_page_management/domain/entities/pdf_operation_result.dart';

class MergePdfsUseCase {
  final PdfPageRepository repository;

  MergePdfsUseCase(this.repository);

  Future<Either<Failure, PdfOperationResult>> call(MergePdfsParams params) async {
    if (params.documents.length < 2) {
      return Left(const FileValidationFailure('At least two PDFs are required for merging.'));
    }
    return await repository.mergePdfs(params.documents);
  }
}

class MergePdfsParams {
  final List<Document> documents;

  MergePdfsParams({required this.documents});
}
