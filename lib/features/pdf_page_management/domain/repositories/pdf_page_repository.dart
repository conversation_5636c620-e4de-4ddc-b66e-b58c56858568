import 'package:dartz/dartz.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/file_discovery/domain/entities/document.dart';
import 'package:myapp/features/pdf_page_management/domain/entities/pdf_operation_result.dart';
import 'package:myapp/features/pdf_page_management/domain/entities/page_selection.dart';

abstract class PdfPageRepository {
  Future<Either<Failure, PdfOperationResult>> mergePdfs(List<Document> documents);
  Future<Either<Failure, PdfOperationResult>> splitPdf(Document document, String pageRange);
  Future<Either<Failure, PdfOperationResult>> reorderPages(Document document, List<PageEditAction> edits);
  Future<Either<Failure, PdfOperationResult>> extractPages(Document document, List<int> pageIndices);
}
