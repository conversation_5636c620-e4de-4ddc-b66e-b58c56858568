# PDF Page Management

Overview
- Manage pages in PDF files: split, merge, extract, and reorder pages.

Structure
- presentation/screens: flows for split/extract/merge and overall page management
- presentation/widgets: UI helpers (thumbnails, range input)
- presentation/notifiers: per-operation notifiers (split, merge, extract, page management)
- domain/entities: `PageSelection`, `PdfOperationResult`
- domain/usecases: split_pdf, merge_pdfs, extract_pdf_pages, reorder_pdf_pages
- domain/repositories: `PdfPageRepository` interface
- data/datasources: `PdfPageDatasource` (+ Syncfusion-backed impl)
- data/repositories: `PdfPageRepositoryImpl`
- data/models: `PdfOperationResultModel`

Key Files
- presentation/screens/page_management_screen.dart: Entry for page tools
- presentation/screens/split_pdf_screen.dart: Split flow
- presentation/screens/extract_pdf_screen.dart: Extract pages flow
- presentation/screens/merge_pdf_screen.dart: Merge PDFs flow
- presentation/widgets/page_thumbnail_grid.dart: Page selection UI
- presentation/widgets/split_range_input.dart: Input ranges for split
- presentation/notifiers/*.dart: Operation-specific state and results
- domain/usecases/*.dart: Core operations
- data/datasources/syncfusion_pdf_page_datasource.dart: PDF operations implementation

App Flow
- From actions or Discover, open desired tool screen.
- User selects files/pages/range; run corresponding use case.
- Render `PdfOperationResult` and provide further actions (save/share/navigate).

