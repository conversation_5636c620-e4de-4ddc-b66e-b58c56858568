import 'package:myapp/features/file_discovery/domain/entities/document.dart';
import 'package:myapp/features/pdf_page_management/domain/entities/pdf_operation_result.dart';
import 'package:myapp/features/pdf_page_management/domain/entities/page_selection.dart';

abstract class PdfPageDatasource {
  Future<PdfOperationResult> mergePdfs(List<Document> documents);
  Future<PdfOperationResult> splitPdf(Document document, String pageRange);
  Future<PdfOperationResult> reorderPages(Document document, List<PageEditAction> edits);
  Future<PdfOperationResult> extractPages(Document document, List<int> pageIndices);
}
