import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:syncfusion_flutter_pdf/pdf.dart';
import 'package:myapp/features/file_discovery/domain/entities/document.dart';
import 'package:myapp/features/pdf_page_management/domain/entities/pdf_operation_result.dart';
import 'package:myapp/features/pdf_page_management/data/datasources/pdf_page_datasource.dart';
import 'package:myapp/features/pdf_page_management/domain/entities/page_selection.dart';

class SyncfusionPdfPageDatasource implements PdfPageDatasource {
  @override
  Future<PdfOperationResult> mergePdfs(List<Document> documents) async {
    final PdfDocument mergedDocument = PdfDocument();

    for (final doc in documents) {
      final fileBytes = await File(doc.path).readAsBytes();
      final tempDocument = PdfDocument(inputBytes: fileBytes);
      
      for (int i = 0; i < tempDocument.pages.count; i++) {
        final PdfPage page = tempDocument.pages[i];
        mergedDocument.pages.add().graphics.drawPdfTemplate(
              page.createTemplate(),
              const Offset(0, 0),
            );
      }
      tempDocument.dispose();
    }

    final appDocumentsDir = await getApplicationDocumentsDirectory();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final outputFileName = 'Merged_$timestamp.pdf';
    final outputPath = '${appDocumentsDir.path}/$outputFileName';
    
    final mergedPdfBytes = await mergedDocument.save();
    final outputFile = File(outputPath);
    await outputFile.writeAsBytes(mergedPdfBytes);

    final pageCount = mergedDocument.pages.count;
    mergedDocument.dispose();

    return PdfOperationResult(
      outputPath: outputPath,
      displayName: outputFileName,
      pageCount: pageCount,
      fileSize: await outputFile.length(),
    );
  }

  @override
  Future<PdfOperationResult> splitPdf(Document document, String pageRange) async {
    final fileBytes = await File(document.path).readAsBytes();
    final sourceDocument = PdfDocument(inputBytes: fileBytes);
    final newDocument = PdfDocument();

    final pagesToInclude = _parsePageRange(pageRange, sourceDocument.pages.count);

    for (final pageIndex in pagesToInclude) {
      newDocument.pages.add().graphics.drawPdfTemplate(
            sourceDocument.pages[pageIndex].createTemplate(),
            const Offset(0, 0),
          );
    }

    final appDocumentsDir = await getApplicationDocumentsDirectory();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final outputFileName = 'Split_${document.name}_$timestamp.pdf';
    final outputPath = '${appDocumentsDir.path}/$outputFileName';

    final newPdfBytes = await newDocument.save();
    final outputFile = File(outputPath);
    await outputFile.writeAsBytes(newPdfBytes);

    final pageCount = newDocument.pages.count;
    newDocument.dispose();
    sourceDocument.dispose();

    return PdfOperationResult(
      outputPath: outputPath,
      displayName: outputFileName,
      pageCount: pageCount,
      fileSize: await outputFile.length(),
    );
  }

  @override
  Future<PdfOperationResult> reorderPages(Document document, List<PageEditAction> edits) async {
    final fileBytes = await File(document.path).readAsBytes();
    final sourceDocument = PdfDocument(inputBytes: fileBytes);
    final newDocument = PdfDocument();

    final newPageOrder = List.generate(sourceDocument.pages.count, (index) => index);

    for (final edit in edits) {
      if (edit.isDeleted) {
        newPageOrder.remove(edit.pageIndex);
      } else {
        final oldIndex = newPageOrder.indexOf(edit.pageIndex);
        newPageOrder.removeAt(oldIndex);
        newPageOrder.insert(edit.pageIndex, edit.pageIndex);
      }
    }

    for (final pageIndex in newPageOrder) {
      final page = sourceDocument.pages[pageIndex];
      newDocument.pages.add().graphics.drawPdfTemplate(
            page.createTemplate(),
            const Offset(0, 0),
          );
    }

    final appDocumentsDir = await getApplicationDocumentsDirectory();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final outputFileName = 'Reordered_${document.name}_$timestamp.pdf';
    final outputPath = '${appDocumentsDir.path}/$outputFileName';

    final newPdfBytes = await newDocument.save();
    final outputFile = File(outputPath);
    await outputFile.writeAsBytes(newPdfBytes);

    final pageCount = newDocument.pages.count;
    newDocument.dispose();
    sourceDocument.dispose();

    return PdfOperationResult(
      outputPath: outputPath,
      displayName: outputFileName,
      pageCount: pageCount,
      fileSize: await outputFile.length(),
    );
  }

  @override
  Future<PdfOperationResult> extractPages(Document document, List<int> pageIndices) async {
    final fileBytes = await File(document.path).readAsBytes();
    final sourceDocument = PdfDocument(inputBytes: fileBytes);
    final newDocument = PdfDocument();

    for (final pageIndex in pageIndices) {
      newDocument.pages.add().graphics.drawPdfTemplate(
            sourceDocument.pages[pageIndex].createTemplate(),
            const Offset(0, 0),
          );
    }

    final appDocumentsDir = await getApplicationDocumentsDirectory();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final outputFileName = 'Extracted_${document.name}_$timestamp.pdf';
    final outputPath = '${appDocumentsDir.path}/$outputFileName';

    final newPdfBytes = await newDocument.save();
    final outputFile = File(outputPath);
    await outputFile.writeAsBytes(newPdfBytes);

    final pageCount = newDocument.pages.count;
    newDocument.dispose();
    sourceDocument.dispose();

    return PdfOperationResult(
      outputPath: outputPath,
      displayName: outputFileName,
      pageCount: pageCount,
      fileSize: await outputFile.length(),
    );
  }

  List<int> _parsePageRange(String pageRange, int totalPages) {
    final pages = <int>{};
    final parts = pageRange.split(',');

    for (final part in parts) {
      if (part.contains('-')) {
        final range = part.split('-');
        final start = int.parse(range[0]) - 1;
        final end = int.parse(range[1]) - 1;
        for (var i = start; i <= end; i++) {
          if (i < totalPages) {
            pages.add(i);
          }
        }
      } else {
        final page = int.parse(part) - 1;
        if (page < totalPages) {
          pages.add(page);
        }
      }
    }
    return pages.toList();
  }
}
