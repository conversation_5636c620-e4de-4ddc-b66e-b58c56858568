import 'package:dartz/dartz.dart';
import 'package:myapp/core/error/failures.dart';
import 'package:myapp/features/file_discovery/domain/entities/document.dart';
import 'package:myapp/features/pdf_page_management/domain/entities/pdf_operation_result.dart';
import 'package:myapp/features/pdf_page_management/domain/repositories/pdf_page_repository.dart';
import 'package:myapp/features/pdf_page_management/data/datasources/pdf_page_datasource.dart';
import 'package:myapp/features/pdf_page_management/domain/entities/page_selection.dart';

class PdfPageRepositoryImpl implements PdfPageRepository {
  final PdfPageDatasource datasource;

  PdfPageRepositoryImpl({required this.datasource});

  @override
  Future<Either<Failure, PdfOperationResult>> mergePdfs(List<Document> documents) async {
    try {
      final result = await datasource.mergePdfs(documents);
      return Right(result);
    } catch (e) {
      return Left(OperationFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, PdfOperationResult>> splitPdf(Document document, String pageRange) async {
    try {
      final result = await datasource.splitPdf(document, pageRange);
      return Right(result);
    } catch (e) {
      return Left(OperationFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, PdfOperationResult>> reorderPages(Document document, List<PageEditAction> edits) async {
    try {
      final result = await datasource.reorderPages(document, edits);
      return Right(result);
    } catch (e) {
      return Left(OperationFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, PdfOperationResult>> extractPages(Document document, List<int> pageIndices) async {
    try {
      final result = await datasource.extractPages(document, pageIndices);
      return Right(result);
    } catch (e) {
      return Left(OperationFailure(e.toString()));
    }
  }
}
