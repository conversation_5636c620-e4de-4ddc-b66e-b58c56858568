// features/sharing/domain/usecases/share_file.dart

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../repositories/sharing_repository.dart';

class ShareFile {
  final SharingRepository repository;

  ShareFile(this.repository);

  Future<Either<Failure, Unit>> call({
    required String path,
    required String mime,
  }) {
    return repository.shareFile(path: path, mime: mime);
  }
}
