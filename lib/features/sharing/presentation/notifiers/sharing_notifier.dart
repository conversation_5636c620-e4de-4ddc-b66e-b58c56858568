// features/sharing/presentation/notifiers/sharing_notifier.dart

import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../data/datasources/share_platform_datasource.dart';
import '../../data/repositories/sharing_repository_impl.dart';
import '../../domain/repositories/sharing_repository.dart';
import '../../domain/usecases/share_file.dart';

part 'sharing_notifier.g.dart';

/// Provider for SharePlatformDataSource
@riverpod
SharePlatformDataSource sharePlatformDataSource(Ref ref) {
  return SharePlatformDataSourceImpl();
}

/// Provider for SharingRepository
@riverpod
SharingRepository sharingRepository(Ref ref) {
  final dataSource = ref.watch(sharePlatformDataSourceProvider);
  return SharingRepositoryImpl(dataSource);
}

/// Provider for ShareFile use case
@riverpod
ShareFile shareFile(Ref ref) {
  final repository = ref.watch(sharingRepositoryProvider);
  return ShareFile(repository);
}

/// Notifier for sharing operations
@riverpod
class SharingNotifier extends _$SharingNotifier {
  @override
  FutureOr<void> build() {
    // No initial state needed
  }

  Future<void> share({required String path, required String mime}) async {
    state = const AsyncValue.loading();

    final shareUseCase = ref.watch(shareFileProvider);
    final result = await shareUseCase(path: path, mime: mime);

    state = await AsyncValue.guard(() async {
      return result.fold((failure) => throw failure, (_) => null);
    });
  }
}
