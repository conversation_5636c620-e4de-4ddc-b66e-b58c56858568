// features/sharing/data/repositories/sharing_repository_impl.dart

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../domain/repositories/sharing_repository.dart';
import '../datasources/share_platform_datasource.dart';

class SharingRepositoryImpl implements SharingRepository {
  final SharePlatformDataSource dataSource;

  SharingRepositoryImpl(this.dataSource);

  @override
  Future<Either<Failure, Unit>> shareFile({
    required String path,
    required String mime,
  }) async {
    try {
      await dataSource.shareFile(path: path, mime: mime);
      return const Right(unit);
    } catch (e) {
      if (e.toString().contains('File not found')) {
        return Left(FileNotFoundFailure(path));
      }
      return Left(SharingFailure(e.toString()));
    }
  }
}
