// features/sharing/data/datasources/share_platform_datasource.dart

import 'dart:io';
import 'package:share_plus/share_plus.dart';

abstract class SharePlatformDataSource {
  Future<void> shareFile({
    required String path,
    required String mime,
  });
}

class SharePlatformDataSourceImpl implements SharePlatformDataSource {
  @override
  Future<void> shareFile({
    required String path,
    required String mime,
  }) async {
    try {
      final file = File(path);
      
      if (!await file.exists()) {
        throw Exception('File not found: $path');
      }

      // Share file using share_plus
      // The XFile constructor accepts the file path and automatically
      // handles file reading without loading the entire file into memory
      final xFile = XFile(
        path,
        mimeType: mime,
      );
      
      await Share.shareXFiles(
        [xFile],
        text: 'Sharing document',
      );
    } catch (e) {
      throw Exception('Failed to share file: ${e.toString()}');
    }
  }
}
