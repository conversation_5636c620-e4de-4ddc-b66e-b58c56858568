# Sharing

Overview
- Share documents through the platform share sheet or other integrations.

Structure
- presentation/notifiers: `SharingNotifier` coordinates share actions
- domain/usecases: `ShareFile`
- domain/repositories: `SharingRepository` interface
- data/datasources: platform share interactions
- data/repositories: `SharingRepositoryImpl`

Key Files
- presentation/notifiers/sharing_notifier.dart: State and triggers for sharing
- domain/usecases/share_file.dart: Encapsulates share intent
- data/datasources/share_platform_datasource.dart: Platform-specific share calls
- data/repositories/sharing_repository_impl.dart: Repo implementation

App Flow
- Triggered from action sheets or info screens to share the current file.
- Notifier uses `ShareFile` which calls platform datasource to present the share sheet.

