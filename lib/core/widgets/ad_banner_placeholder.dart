import 'package:flutter/material.dart';

/// Placeholder widget for a persistent ad banner.
/// Replace this with the actual ad SDK integration when available.
class AdBannerPlaceholder extends StatelessWidget {
  final double height;

  const AdBannerPlaceholder({
    super.key,
    this.height = 56,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      height: height,
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHigh.withValues(alpha: 0.4),
        border: Border(
          top: BorderSide(color: colorScheme.outlineVariant.withValues(alpha: 0.6)),
        ),
      ),
      alignment: Alignment.center,
      child: Text(
        'Advertisement',
        style: theme.textTheme.bodyMedium?.copyWith(
          color: colorScheme.onSurfaceVariant,
          fontWeight: FontWeight.w600,
          letterSpacing: 0.3,
        ),
      ),
    );
  }
}
