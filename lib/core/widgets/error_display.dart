// core/widgets/error_display.dart

import 'package:flutter/material.dart';
import '../error/failures.dart';

class ErrorDisplay extends StatelessWidget {
  final Failure failure;
  final VoidCallback? onRetry;
  final bool showIcon;
  final bool isSelectable;

  const ErrorDisplay({
    super.key,
    required this.failure,
    this.onRetry,
    this.showIcon = true,
    this.isSelectable = true,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (showIcon) ...[
              Icon(
                _getErrorIcon(failure),
                size: 64,
                color: Theme.of(context).colorScheme.error,
              ),
              const SizedBox(height: 16),
            ],
            if (isSelectable)
              SelectableText.rich(
                TextSpan(
                  text: failure.message,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Theme.of(context).colorScheme.error,
                  ),
                ),
                textAlign: TextAlign.center,
              )
            else
              Text(
                failure.message,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Theme.of(context).colorScheme.error,
                ),
                textAlign: TextAlign.center,
              ),
            if (onRetry != null) ...[
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: const Text('Retry'),
                style: ElevatedButton.styleFrom(
                  foregroundColor: Theme.of(context).colorScheme.onError,
                  backgroundColor: Theme.of(context).colorScheme.error,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  IconData _getErrorIcon(Failure failure) {
    if (failure is StoragePermissionFailure || failure is PermissionDeniedException || failure is PermissionPermanentlyDeniedFailure) {
      return Icons.storage;
    }
    if (failure is FileNotFoundFailure || failure is FileSystemFailure) {
      return Icons.folder_off;
    }
    if (failure is FileReadFailure || failure is CorruptedFileFailure) {
      return Icons.file_download_off;
    }
    if (failure is UnsupportedFileFormatFailure) {
      return Icons.file_present;
    }
    if (failure is NoDocumentsFoundFailure) {
      return Icons.description;
    }
    return Icons.error_outline;
  }
}