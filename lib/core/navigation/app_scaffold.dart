// core/navigation/app_scaffold.dart

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
// No throttle/debounce: navigate immediately for zero added delay
import 'dart:developer' as developer;
import '../utils/performance_utils.dart' show logNextFrames;
import '../../features/file_discovery/presentation/notifiers/document_list_notifier.dart';
import '../../features/discover/presentation/notifiers/discover_tools_notifier.dart';

class AppScaffold extends ConsumerStatefulWidget {
  final StatefulNavigationShell navigationShell;

  const AppScaffold({super.key, required this.navigationShell});

  @override
  ConsumerState<AppScaffold> createState() => _AppScaffoldState();
}

class _AppScaffoldState extends ConsumerState<AppScaffold> {

  @override
  void initState() {
    super.initState();
    // Pre-warm Discover data (already loaded in splash, but ensure it's cached)
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        // Trigger provider build in background
        // ignore: unused_result
        ref.read(discoverToolsProvider);
      } catch (_) {}
    });
  }

  void _onDestinationSelected(int index) {
    final task = developer.TimelineTask()..start('nav_switch:$index');
    final sw = Stopwatch()..start();

    final previous = widget.navigationShell.currentIndex;
    if (previous == 0 && index != 0) {
      // Cancel heavy scans when leaving Home
      try {
        ref.read(documentListProvider.notifier).cancelScan();
      } catch (_) {}
    }

    widget.navigationShell.goBranch(
      index,
      initialLocation: index == widget.navigationShell.currentIndex,
    );
    // Capture a few frames' timings without DevTools
    logNextFrames(count: 5, label: 'nav:$index');
    WidgetsBinding.instance.addPostFrameCallback((_) {
      sw.stop();
      task.finish();
      developer.log('nav switch to index=$index took ${sw.elapsedMilliseconds}ms', name: 'nav');
      // Also echo to console via debugPrint for easier visibility
      // in environments where developer.log isn't shown.
      // ignore: avoid_print
      debugPrint('[nav] switch index=$index ${sw.elapsedMilliseconds}ms');
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: widget.navigationShell,
      bottomNavigationBar: NavigationBar(
        selectedIndex: widget.navigationShell.currentIndex,
        animationDuration: Duration.zero,
        destinations: [
          const NavigationDestination(
            icon: Icon(Icons.home_outlined),
            selectedIcon: Icon(Icons.home),
            label: 'Home',
          ),
          const NavigationDestination(
            icon: Icon(Icons.history),
            selectedIcon: Icon(Icons.history),
            label: 'Recent',
          ),
          const NavigationDestination(
            icon: Icon(Icons.favorite_border),
            selectedIcon: Icon(Icons.favorite),
            label: 'Favorite',
          ),
          NavigationDestination(
            icon: Badge(
              label: Text(
                'NEW',
                style: const TextStyle(fontSize: 10, fontWeight: FontWeight.w600),
              ),
              child: const Icon(Icons.travel_explore_outlined),
            ),
            selectedIcon: Badge(
              label: Text(
                'NEW',
                style: const TextStyle(fontSize: 10, fontWeight: FontWeight.w600),
              ),
              child: const Icon(Icons.travel_explore),
            ),
            label: 'Discover',
          ),
        ],
        onDestinationSelected: _onDestinationSelected,
      ),
    );
  }
}
