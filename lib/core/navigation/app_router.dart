// core/navigation/app_router.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import 'app_scaffold.dart';
import '../../features/discover/presentation/screens/discover_screen.dart';
import '../startup/splash_screen.dart';
import '../../features/document_viewer/presentation/screens/docx_viewer_screen.dart';
import '../../features/document_viewer/presentation/screens/pdf_viewer_screen.dart';
import '../../features/document_viewer/presentation/screens/pptx_viewer_screen.dart';
import '../../features/document_viewer/presentation/screens/xlsx_viewer_screen.dart';
import '../../features/file_discovery/presentation/screens/home_screen.dart';
import '../../features/file_discovery/presentation/screens/recent_screen.dart';
import '../../features/file_discovery/presentation/screens/favorite_screen.dart';
import '../../features/file_info/presentation/screens/file_info_screen.dart';

// File Conversion Screens
import '../../features/file_conversion/domain/entities/conversion_type.dart';
import '../../features/file_conversion/presentation/screens/scan_camera_screen.dart';
import '../../features/file_conversion/presentation/screens/scan_review_screen.dart';
import '../../features/file_conversion/presentation/screens/scan_success_screen.dart';
import '../../features/file_conversion/presentation/screens/image_conversion_screen.dart';
import '../../features/file_conversion/presentation/screens/document_conversion_screen.dart';
import '../../features/pdf_page_management/presentation/screens/extract_pdf_screen.dart';
import '../../features/pdf_page_management/presentation/screens/merge_pdf_screen.dart';
import '../../features/pdf_page_management/presentation/screens/page_management_screen.dart';
import '../../features/pdf_page_management/presentation/screens/split_pdf_screen.dart';

final routerProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: '/splash',
    routes: [
      GoRoute(
        path: '/splash',
        builder: (context, state) => const SplashScreen(),
      ),
      StatefulShellRoute.indexedStack(
        builder: (context, state, navigationShell) =>
            AppScaffold(navigationShell: navigationShell),
        branches: [
          StatefulShellBranch(
            routes: [
              GoRoute(
                path: '/',
                builder: (context, state) => const HomeScreen(),
                routes: [
                  GoRoute(
                    path: 'viewer/pdf/:path',
                    builder: (context, state) {
                      final path = state.pathParameters['path']!;
                      final name =
                          state.uri.queryParameters['name'] ?? 'Document';
                      return PDFViewerScreen(
                        documentPath: Uri.decodeComponent(path),
                        documentName: Uri.decodeComponent(name),
                      );
                    },
                  ),
                  GoRoute(
                    path: 'viewer/docx/:path',
                    builder: (context, state) {
                      final path = state.pathParameters['path']!;
                      final name =
                          state.uri.queryParameters['name'] ?? 'Document';
                      return DOCXViewerScreen(
                        documentPath: Uri.decodeComponent(path),
                        documentName: Uri.decodeComponent(name),
                      );
                    },
                  ),
                  GoRoute(
                    path: 'viewer/xlsx/:path',
                    builder: (context, state) {
                      final path = state.pathParameters['path']!;
                      final name =
                          state.uri.queryParameters['name'] ?? 'Spreadsheet';
                      return XLSXViewerScreen(
                        documentPath: Uri.decodeComponent(path),
                        documentName: Uri.decodeComponent(name),
                      );
                    },
                  ),
                  GoRoute(
                    path: 'viewer/pptx/:path',
                    builder: (context, state) {
                      final path = state.pathParameters['path']!;
                      final name =
                          state.uri.queryParameters['name'] ?? 'Presentation';
                      return PPTXViewerScreen(
                        documentPath: Uri.decodeComponent(path),
                        documentName: Uri.decodeComponent(name),
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
          StatefulShellBranch(
            routes: [
              GoRoute(
                path: '/recent',
                builder: (context, state) => const RecentScreen(),
              ),
            ],
          ),
          StatefulShellBranch(
            routes: [
              GoRoute(
                path: '/favorite',
                builder: (context, state) => const FavoriteScreen(),
              ),
            ],
          ),
          StatefulShellBranch(
            routes: [
              GoRoute(
                path: '/discover',
                builder: (context, state) => const DiscoverScreen(),
              ),
            ],
          ),
        ],
      ),
      // Document viewer routes
      GoRoute(
        path: '/file-info',
        builder: (context, state) {
          final path = state.uri.queryParameters['path'];
          if (path == null) {
            return Scaffold(
              appBar: AppBar(title: const Text('Error')),
              body: const Center(child: Text('File path is required')),
            );
          }
          return FileInfoScreen(filePath: Uri.decodeComponent(path));
        },
      ),
      // File conversion routes
      GoRoute(
        path: '/scan-document',
        builder: (context, state) => const ScanCameraScreen(),
      ),
      GoRoute(
        path: '/scan-review',
        builder: (context, state) => const ScanReviewScreen(),
      ),
      GoRoute(
        path: '/scan-success',
        builder: (context, state) {
          final pdfPath = state.uri.queryParameters['pdfPath'];
          final fileName = state.uri.queryParameters['fileName'];

          if (pdfPath == null || fileName == null) {
            return Scaffold(
              appBar: AppBar(title: const Text('Error')),
              body: const Center(child: Text('Missing PDF information')),
            );
          }

          return ScanSuccessScreen(
            pdfPath: Uri.decodeComponent(pdfPath),
            fileName: Uri.decodeComponent(fileName),
          );
        },
      ),
      GoRoute(
        path: '/convert-images',
        builder: (context, state) => const ImageConversionScreen(),
      ),
      GoRoute(
        path: '/convert-document',
        builder: (context, state) {
          final type = state.uri.queryParameters['type'] ?? 'docxToPdf';
          return DocumentConversionScreen(
            conversionType: _mapStringToConversionType(type),
          );
        },
      ),
      GoRoute(
        path: '/merge-pdf',
        builder: (context, state) => const MergePdfScreen(),
      ),
      GoRoute(
        path: '/split-pdf',
        builder: (context, state) => const SplitPdfScreen(),
      ),
      GoRoute(
        path: '/manage-pages',
        builder: (context, state) => const PageManagementScreen(),
      ),
      GoRoute(
        path: '/extract-pages',
        builder: (context, state) => const ExtractPdfScreen(),
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(title: const Text('Page Not Found')),
      body: Center(child: Text('Page not found: ${state.uri}')),
    ),
  );
});

// Helper function to map string to ConversionType
ConversionType _mapStringToConversionType(String type) {
  switch (type) {
    case 'docxToPdf':
      return ConversionType.docxToPdf;
    case 'pptToPdf':
      return ConversionType.pptToPdf;
    case 'xlsxToPdf':
      return ConversionType.xlsxToPdf;
    case 'imageToPdf':
      return ConversionType.imageToPdf;
    case 'scanToPdf':
      return ConversionType.scanToPdf;
    default:
      return ConversionType.docxToPdf; // Default to docx conversion
  }
}
