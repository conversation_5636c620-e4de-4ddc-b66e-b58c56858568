// core/utils/file_size_formatter.dart

class FileSizeFormatter {
  /// Format bytes to human readable format (B, KB, MB, GB)
  static String formatFileSize(int bytes) {
    if (bytes < 0) {
      throw ArgumentError('Bytes cannot be negative');
    }

    const suffixes = ['B', 'KB', 'MB', 'GB'];
    var size = bytes.toDouble();
    var suffixIndex = 0;

    while (size >= 1024 && suffixIndex < suffixes.length - 1) {
      size /= 1024;
      suffixIndex++;
    }

    if (suffixIndex == 0) {
      return '${size.toInt()} ${suffixes[suffixIndex]}';
    } else {
      // Show one decimal place for KB, MB, GB
      return '${size.toStringAsFixed(1)} ${suffixes[suffixIndex]}';
    }
  }

  /// Format bytes to compact format (e.g., "2.5MB")
  static String formatFileSizeCompact(int bytes) {
    if (bytes < 0) {
      throw ArgumentError('Bytes cannot be negative');
    }

    const suffixes = ['', 'K', 'M', 'G'];
    var size = bytes.toDouble();
    var suffixIndex = 0;

    while (size >= 1024 && suffixIndex < suffixes.length - 1) {
      size /= 1024;
      suffixIndex++;
    }

    if (suffixIndex == 0) {
      return '${size.toInt()}${suffixes[suffixIndex]}';
    } else {
      // Show one decimal place for KB, MB, GB
      return '${size.toStringAsFixed(1)}${suffixes[suffixIndex]}';
    }
  }

  /// Check if file size exceeds the limit (50MB for MVP)
  static bool isFileSizeAllowed(int bytes) {
    return bytes <= 50 * 1024 * 1024; // 50MB
  }

  /// Get file size category for UI display
  static String getFileSizeCategory(int bytes) {
    if (bytes < 1024) return 'tiny'; // < 1KB
    if (bytes < 1024 * 1024) return 'small'; // < 1MB
    if (bytes < 10 * 1024 * 1024) return 'medium'; // < 10MB
    if (bytes < 50 * 1024 * 1024) return 'large'; // < 50MB
    return 'huge'; // >= 50MB
  }
}