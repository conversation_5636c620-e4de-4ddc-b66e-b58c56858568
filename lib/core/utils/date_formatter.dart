// core/utils/date_formatter.dart

import 'package:intl/intl.dart';

class DateFormatter {
  /// Format date for display in file lists
  static String formatDateModified(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final dateOnly = DateTime(date.year, date.month, date.day);

    // Today
    if (dateOnly == today) {
      return 'Today ${_formatTime(date)}';
    }

    // Yesterday
    if (dateOnly == yesterday) {
      return 'Yesterday ${_formatTime(date)}';
    }

    // This week (show day name and time)
    if (date.isAfter(today.subtract(const Duration(days: 7)))) {
      return '${_getDayName(date)} ${_formatTime(date)}';
    }

    // This year (show month, day, and time)
    if (date.year == now.year) {
      return '${_formatMonthDay(date)} ${_formatTime(date)}';
    }

    // Older (show full date and time)
    return '${_formatFullDate(date)} ${_formatTime(date)}';
  }

  /// Format date for relative display (e.g., "2 days ago")
  static String formatRelativeDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        if (difference.inMinutes <= 1) {
          return 'Just now';
        }
        return '${difference.inMinutes} minute${difference.inMinutes == 1 ? '' : 's'} ago';
      }
      return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} ago';
    }

    if (difference.inDays == 1) {
      return 'Yesterday';
    }

    if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    }

    if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return '$weeks week${weeks == 1 ? '' : 's'} ago';
    }

    if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return '$months month${months == 1 ? '' : 's'} ago';
    }

    final years = (difference.inDays / 365).floor();
    return '$years year${years == 1 ? '' : 's'} ago';
  }

  /// Format date for compact display (e.g., "Jan 15")
  static String formatCompactDate(DateTime date) {
    final now = DateTime.now();

    if (date.year == now.year) {
      return _formatMonthDay(date);
    }

    return _formatShortYear(date);
  }

  // Helper methods
  static String _formatTime(DateTime date) {
    return DateFormat('h:mm a').format(date); // e.g., "2:30 PM"
  }

  static String _getDayName(DateTime date) {
    return DateFormat('EEE').format(date); // e.g., "Mon", "Tue"
  }

  static String _formatMonthDay(DateTime date) {
    return DateFormat('MMM d').format(date); // e.g., "Jan 15"
  }

  static String _formatFullDate(DateTime date) {
    return DateFormat('MMM d, yyyy').format(date); // e.g., "Jan 15, 2025"
  }

  static String _formatShortYear(DateTime date) {
    return DateFormat('MMM d, yy').format(date); // e.g., "Jan 15, '25"
  }

  /// Check if date is today
  static bool isToday(DateTime date) {
    final now = DateTime.now();
    return DateTime(date.year, date.month, date.day) ==
           DateTime(now.year, now.month, now.day);
  }

  /// Check if date is yesterday
  static bool isYesterday(DateTime date) {
    final now = DateTime.now();
    final yesterday = now.subtract(const Duration(days: 1));
    return DateTime(date.year, date.month, date.day) ==
           DateTime(yesterday.year, yesterday.month, yesterday.day);
  }

  /// Check if date is within the last week
  static bool isWithinWeek(DateTime date) {
    final now = DateTime.now();
    final weekAgo = now.subtract(const Duration(days: 7));
    return date.isAfter(weekAgo);
  }
}