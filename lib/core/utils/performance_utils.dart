// core/utils/performance_utils.dart

import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';
import 'dart:developer' as developer;

class Debouncer {
  Debouncer(this.delay);
  final Duration delay;
  Timer? _timer;

  void call(void Function() action) {
    _timer?.cancel();
    _timer = Timer(delay, action);
  }

  void dispose() {
    _timer?.cancel();
  }
}

class Throttler {
  Throttler(this.window);
  final Duration window;
  DateTime? _last;

  void call(void Function() action) {
    final now = DateTime.now();
    if (_last == null || now.difference(_last!) >= window) {
      _last = now;
      action();
    }
  }
}

Future<T> measurePerformance<T>(String label, Future<T> Function() action) async {
  final sw = Stopwatch()..start();
  try {
    final result = await action();
    return result;
  } finally {
    sw.stop();
    if (kDebugMode) {
      debugPrint('[perf] $label took ${sw.elapsedMilliseconds} ms');
    }
  }
}

/// Runs [callback] with a timeout using <PERSON>lutter's `compute` to execute in a background isolate.
Future<R> computeWithTimeout<R, Q>(FutureOr<R> Function(Q) callback, Q message,
    {Duration timeout = const Duration(seconds: 30)}) async {
  return compute(callback, message).timeout(timeout);
}

/// Logs the next [count] frames' build/raster durations to console in debug.
/// Useful when DevTools is unavailable. Call it around interactions
/// (e.g., after navigation) to capture a few frames.
void logNextFrames({int count = 5, String label = ''}) {
  if (!kDebugMode) return;
  int remaining = count;
  void callback(List<FrameTiming> timings) {
    for (final t in timings) {
      final buildMs = t.buildDuration.inMilliseconds;
      final rasterMs = t.rasterDuration.inMilliseconds;
      developer.log('[frames] $label build=${buildMs}ms raster=${rasterMs}ms', name: 'perf');
      remaining--;
      if (remaining <= 0) {
        SchedulerBinding.instance.removeTimingsCallback(callback);
        break;
      }
    }
  }
  SchedulerBinding.instance.addTimingsCallback(callback);
}
