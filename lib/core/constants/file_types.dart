// core/constants/file_types.dart

class FileTypeConstants {
  static const List<String> supportedExtensions = ['.pdf', '.docx', '.xlsx', '.pptx'];
  
  static const String pdfMimeType = 'application/pdf';
  static const String docxMimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
  static const String xlsxMimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
  static const String pptxMimeType = 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
  
  static const Map<String, String> mimeTypeMap = {
    '.pdf': pdfMimeType,
    '.docx': docxMimeType,
    '.xlsx': xlsxMimeType,
    '.pptx': pptxMimeType,
  };
  
  static String? getMimeType(String extension) {
    return mimeTypeMap[extension.toLowerCase()];
  }
  
  static bool isSupportedExtension(String extension) {
    return supportedExtensions.contains(extension.toLowerCase());
  }
}