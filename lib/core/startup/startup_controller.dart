// core/startup/startup_controller.dart

import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../features/discover/presentation/notifiers/discover_tools_notifier.dart';
import '../../features/file_discovery/presentation/notifiers/document_list_notifier.dart';
import '../utils/performance_utils.dart';
import 'startup_metrics.dart';

class StartupState {
  final double progress; // 0.0 - 1.0
  final String message;
  final List<String> logs;

  const StartupState({
    required this.progress,
    required this.message,
    this.logs = const [],
  });

  factory StartupState.initial() => const StartupState(
        progress: 0.0,
        message: 'Starting…',
        logs: ['Starting…'],
      );

  StartupState copyWith({
    double? progress,
    String? message,
    List<String>? logs,
  }) {
    return StartupState(
      progress: progress ?? this.progress,
      message: message ?? this.message,
      logs: logs ?? this.logs,
    );
  }
}

final startupControllerProvider = Provider<StartupController>(
  (ref) {
    final controller = StartupController(ref);
    ref.onDispose(controller.dispose);
    return controller;
  },
);

class StartupController {
  StartupController(this.ref);

  final Ref ref;
  StartupState _state = StartupState.initial();
  Future<void>? _inFlight;
  bool _disposed = false;
  void Function(StartupState state)? _progressCallback;
  Future<String>? _homeWarmup;
  Future<String>? _discoverWarmup;

  StartupState get state => _state;

  Future<void> run({void Function(StartupState state)? onProgress}) {
    if (_disposed) return Future.value();

    if (_inFlight != null) {
      onProgress?.call(_state);
      return _inFlight!;
    }

    _progressCallback = onProgress;
    _inFlight = _runInternal().whenComplete(() {
      _progressCallback = null;
      _inFlight = null;
    });
    return _inFlight!;
  }

  Future<void> _runInternal() async {
    StartupMetrics.instance.startStage('startup_pipeline', 'Splash warm-up pipeline');
    void emit(StartupState newState) {
      if (_disposed) return;
      _state = newState;
      _progressCallback?.call(newState);
    }

    void update({double? progress, String? message, bool appendLog = false}) {
      if (_disposed) return;
      final newMessage = message ?? _state.message;
      final newProgress = progress ?? _state.progress;
      final shouldAppend = appendLog &&
          (_state.logs.isEmpty || _state.logs.last != newMessage);
      final newLogs = shouldAppend
          ? [..._state.logs, newMessage]
          : _state.logs;

      emit(
        _state.copyWith(
          progress: newProgress,
          message: newMessage,
          logs: newLogs,
        ),
      );
    }

    void stage(double progress, String message) {
      debugPrint('[startup] $message');
      update(progress: progress, message: message, appendLog: true);
    }

    void logMessage(String message) {
      debugPrint('[startup] $message');
      update(message: message, appendLog: true);
    }

    var pipelineFailed = false;
    final pipelineWatch = Stopwatch()..start();
    final minimumSplashTime = Future.delayed(const Duration(milliseconds: 800));
    
    try {
      stage(0.05, 'Preparing UI…');
      final frameTimedOut = await _waitForNextFrame();
      if (_disposed) return;
      if (frameTimedOut) {
        logMessage('First frame slow – continuing warm-up to unblock splash');
      }
      if (_disposed) return;

      logMessage('Kickstarting background warm-up');
      update(progress: 0.15, message: 'Starting warm-ups…', appendLog: true);

      final homePreload = _preloadHome();
      final explorePreload = _preloadDiscover();

      int completed = 0;
      const total = 2;
      const base = 0.15;
      const span = 0.35; // progress allocated for async tasks

      void markSuccess(String label, {String? message}) {
        if (_disposed) return;
        completed++;
        final updated = (base + (completed / total) * span).clamp(0.0, 0.7);
        final status = message ?? '$label ready';
        debugPrint('[startup] $status');
        update(progress: updated, message: status, appendLog: true);
      }

      void markFailure(String label, Object error, StackTrace stack) {
        if (_disposed) return;
        completed++;
        final updated = (base + (completed / total) * span).clamp(0.0, 0.7);
        final detail = '$label failed: $error';
        debugPrint('[startup] $detail');
        update(progress: updated, message: detail, appendLog: true);
        if (kDebugMode) {
          debugPrint(stack.toString());
        }
      }

      // Run both preloads in parallel without blocking
      unawaited(
        homePreload.then((message) => markSuccess('Home', message: message)).catchError(
          (Object error, StackTrace stack) => markFailure('Home preload', error, stack),
        ),
      );

      unawaited(
        explorePreload.then((message) => markSuccess('Explore', message: message)).catchError(
          (Object error, StackTrace stack) => markFailure('Explore preload', error, stack),
        ),
      );

      // Wait for at least one preload to complete before proceeding
      await Future.any([homePreload, explorePreload]).catchError((_) {
        // Ignore errors here, they're already handled above
        return null;
      });

      if (_disposed) return;

      // Ensure splash screen is visible for a minimum duration
      final elapsed = pipelineWatch.elapsedMilliseconds;
      const minDisplay = 800; // milliseconds
      if (elapsed < minDisplay) {
        final remaining = minDisplay - elapsed;
        debugPrint('[startup] enforcing minimum splash time, waiting ${remaining}ms more');
        await Future.delayed(Duration(milliseconds: remaining));
      }

      if (_disposed) return;

      update(
        progress: 0.9,
        message: 'Ready',
        appendLog: true,
      );
      logNextFrames(count: 2, label: 'warm');
      debugPrint('[startup] pipeline complete in ${pipelineWatch.elapsedMilliseconds}ms');
    } catch (error, stack) {
      pipelineFailed = true;
      StartupMetrics.instance.failStage(
        'startup_pipeline',
        'Splash warm-up pipeline',
        error,
        stack,
      );
      rethrow;
    } finally {
      if (!pipelineFailed) {
        StartupMetrics.instance.endStage(
          'startup_pipeline',
          'Splash warm-up pipeline',
        );
      }
    }
  }

  Future<String?> _preloadHome() async {
    if (_disposed || !ref.mounted) {
      debugPrint('[startup][home] warm-up skipped (disposed=$_disposed, mounted=${ref.mounted})');
      return 'Home warm-up skipped';
    }
    _homeWarmup ??= () async {
      final sw = Stopwatch()..start();
      debugPrint('[startup][home] warm-up begin');
      StartupMetrics.instance.startStage('home_preload', 'Home data preload');
      DocumentListNotifier.enableWarmupMode();
      try {
        final notifier = ref.read(documentListProvider.notifier);
        final message = await notifier.warmUpForSplash();
        sw.stop();
        StartupMetrics.instance.endStage('home_preload', 'Home data preload');
        debugPrint('[startup][home] warm-up finished in ${sw.elapsedMilliseconds}ms ($message)');
        return message;
      } catch (error, stack) {
        sw.stop();
        debugPrint('[startup][home] warm-up failed in ${sw.elapsedMilliseconds}ms: $error');
        StartupMetrics.instance.failStage('home_preload', 'Home data preload', error, stack);
        rethrow;
      } finally {
        DocumentListNotifier.disableWarmupMode();
      }
    }();
    if (_disposed) {
      debugPrint('[startup][home] warm-up cancelled after dispose');
      return 'Home warm-up cancelled';
    }
    return await _homeWarmup!;
  }

  Future<String?> _preloadDiscover() async {
    if (_disposed || !ref.mounted) {
      debugPrint('[startup][explore] warm-up skipped (disposed=$_disposed, mounted=${ref.mounted})');
      return 'Explore warm-up skipped';
    }
    _discoverWarmup ??= () async {
      final sw = Stopwatch()..start();
      debugPrint('[startup][explore] warm-up begin');
      StartupMetrics.instance.startStage('explore_preload', 'Explore data preload');
      try {
        // Load data - this is lightweight (19-21ms)
        await ref.read(discoverToolsProvider.future);
        
        // Note: Widget pre-building requires BuildContext and is handled
        // in AppScaffold.initState via addPostFrameCallback
        // The 170ms first navigation includes shader compilation, which is
        // unavoidable without a full pre-render pass (would block splash longer)
        
        StartupMetrics.instance.endStage('explore_preload', 'Explore data preload');
        sw.stop();
        debugPrint('[startup][explore] warm-up finished in ${sw.elapsedMilliseconds}ms');
        return 'Explore ready';
      } catch (error, stack) {
        sw.stop();
        debugPrint('[startup][explore] warm-up failed in ${sw.elapsedMilliseconds}ms: $error');
        StartupMetrics.instance.failStage('explore_preload', 'Explore data preload', error, stack);
        rethrow;
      }
    }();
    if (_disposed) {
      debugPrint('[startup][explore] warm-up cancelled after dispose');
      return 'Explore warm-up cancelled';
    }
    return await _discoverWarmup!;
  }

  Future<bool> _waitForNextFrame({Duration timeout = const Duration(milliseconds: 50)}) async {
    final completer = Completer<bool>();

    void complete(bool timedOut) {
      if (completer.isCompleted) return;
      completer.complete(timedOut);
    }

    SchedulerBinding.instance.endOfFrame.then((_) => complete(false));
    final timer = Timer(timeout, () => complete(true));

    final timedOut = await completer.future;
    timer.cancel();
    return timedOut;
  }

  void dispose() {
    _disposed = true;
    _progressCallback = null;
  }
}
