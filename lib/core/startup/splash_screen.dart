// core/startup/splash_screen.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'startup_controller.dart';
import 'startup_metrics.dart';
import 'package:go_router/go_router.dart';

class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({super.key});

  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen> {
  late StartupState _state;
  double? _lastLoggedProgress;
  int _lastLogCount = -1;

  @override
  void initState() {
    super.initState();
    _logUi('initState');
    _state = StartupState.initial();
    StartupMetrics.instance.markSplashShown();

    // Defer controller initialization to after first frame to allow splash UI to render immediately
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (!mounted) return;
      _logUi('first frame callback');
      StartupMetrics.instance.markEvent('first_frame_rendered');

      // Add small delay to ensure splash UI is actually painted
      await Future.delayed(const Duration(milliseconds: 100));
      if (!mounted) return;

      // Start controller after first frame is rendered
      _logUi('controller start (post-frame)');
      StartupMetrics.instance.markEvent('controller_run_scheduled');
      final ctrl = ref.read(startupControllerProvider);
      await ctrl.run(onProgress: (next) {
        if (!mounted) return;
        setState(() {
          _state = next;
        });
      });
      
      if (!mounted) return;
      _logUi('controller completed – navigating');
      StartupMetrics.instance.markSplashDismissed();
      // Use GoRouter for named navigation in tests/app
      // ignore: use_build_context_synchronously
      context.go('/');
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _logUi('didChangeDependencies');
  }

  @override
  void setState(VoidCallback fn) {
    _logUi('setState invoked');
    super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    _logBuild();
    final progress = _state.progress.clamp(0.0, 1.0).toDouble();
    final logs = _state.logs;
    final visibleLogs = logs.length > 4
        ? logs.sublist(logs.length - 4)
        : logs;

    return Scaffold(
      body: Stack(
        children: [
          Align(
            alignment: Alignment.center,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24.0),
              child: ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 360),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const FlutterLogo(size: 64),
                    const SizedBox(height: 24),
                    LinearProgressIndicator(
                      value: progress.isNaN ? null : progress,
                    ),
                    const SizedBox(height: 12),
                    Align(
                      alignment: Alignment.centerLeft,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: visibleLogs
                            .map(
                              (line) => Padding(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 2.0,
                                ),
                                child: Text(
                                  line,
                                  style:
                                      Theme.of(context).textTheme.bodyMedium,
                                ),
                              ),
                            )
                            .toList(),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _logBuild() {
    final progress = _state.progress.clamp(0.0, 1.0);
    final logs = _state.logs.length;
    final shouldLogProgress =
        _lastLoggedProgress == null || (progress - _lastLoggedProgress!).abs() >= 0.02;
    final shouldLogCount = logs != _lastLogCount;
    if (shouldLogProgress || shouldLogCount) {
      _logUi('build progress=${(progress * 100).toStringAsFixed(1)}% logs=$logs');
      _lastLoggedProgress = progress;
      _lastLogCount = logs;
    }
  }

  void _logUi(String message) {
    debugPrint('[splash_ui] $message');
  }

  @override
  void dispose() {
    _logUi('dispose');
    super.dispose();
  }
}
