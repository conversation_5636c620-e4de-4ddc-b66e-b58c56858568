// core/startup/startup_metrics.dart

import 'package:flutter/foundation.dart';

class StartupMetrics {
  StartupMetrics._();

  static final StartupMetrics instance = StartupMetrics._();

  DateTime? _appStart;
  DateTime? _lastMark;
  final Map<String, DateTime> _stageStarts = {};

  void markAppStart() {
    _appStart = DateTime.now();
    _lastMark = _appStart;
    _log('app_start');
  }

  void markSplashShown() {
    _log('splash_shown');
  }

  void markSplashDismissed() {
    _log('splash_dismissed');
  }

  void markEvent(String label, {String? detail}) {
    _log(label, extra: detail);
  }

  void startStage(String id, String label) {
    final now = DateTime.now();
    if (_stageStarts.containsKey(id)) return;
    _stageStarts[id] = now;
    _log('stage_start', extra: 'id=$id | $label');
  }

  void endStage(String id, String label) {
    final now = DateTime.now();
    final start = _stageStarts.remove(id);
    final duration = start != null ? now.difference(start).inMilliseconds : null;
    _log('stage_end', extra: duration != null ? 'id=$id | $label | duration=${duration}ms' : 'id=$id | $label');
  }

  void failStage(String id, String label, Object error, [StackTrace? stack]) {
    final now = DateTime.now();
    final start = _stageStarts.remove(id);
    final duration = start != null ? now.difference(start).inMilliseconds : null;
    _log(
      'stage_error',
      extra: duration != null
          ? 'id=$id | $label | duration=${duration}ms | error=$error'
          : 'id=$id | $label | error=$error',
    );
    if (stack != null) {
      debugPrint(stack.toString());
    }
  }

  void _log(String stage, {String? extra}) {
    final now = DateTime.now();
    final sinceStart = _appStart != null ? now.difference(_appStart!).inMilliseconds : null;
    final sincePrev = _lastMark != null ? now.difference(_lastMark!).inMilliseconds : null;

    final buffer = StringBuffer('[startup] $stage');
    if (sinceStart != null) {
      buffer.write(' | sinceStart=${sinceStart}ms');
    }
    if (sincePrev != null) {
      buffer.write(' | sincePrev=${sincePrev}ms');
    }
    if (extra != null && extra.isNotEmpty) {
      buffer.write(' | $extra');
    }

    debugPrint(buffer.toString());
    _lastMark = now;
  }
}
