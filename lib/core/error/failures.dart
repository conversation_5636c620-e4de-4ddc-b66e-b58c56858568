// core/error/failures.dart

abstract class Failure {
  final String message;
  const Failure(this.message);
}

// File discovery failures
class StoragePermissionFailure extends Failure {
  const StoragePermissionFailure(super.message);
}

class FileSystemFailure extends Failure {
  const FileSystemFailure(super.message);
}

class NoDocumentsFoundFailure extends Failure {
  const NoDocumentsFoundFailure() : super('No compatible documents found on device');
}

// File reading failures
class FileNotFoundFailure extends Failure {
  final String filePath;

  const FileNotFoundFailure(this.filePath) : super('File not found: $filePath');
}

class FileReadFailure extends Failure {
  const FileReadFailure(super.message);
}

class UnsupportedFileFormatFailure extends Failure {
  const UnsupportedFileFormatFailure(String format)
    : super('Unsupported file format: $format');
}

class CorruptedFileFailure extends Failure {
  const CorruptedFileFailure(super.message);
}

// Generic operation failures
class FileValidationFailure extends Failure {
  const FileValidationFailure(super.message);
}

class OperationFailure extends Failure {
  const OperationFailure(super.message);
}

class StorageFailure extends Failure {
  const StorageFailure(super.message);
}

// Permission failures
class PermissionDeniedException extends Failure {
  const PermissionDeniedException()
    : super('Storage permission is required to scan documents');
}

class PermissionPermanentlyDeniedFailure extends Failure {
  const PermissionPermanentlyDeniedFailure()
    : super('Storage permission permanently denied. Please enable in settings.');
}

// File conversion failures
class CameraFailure extends Failure {
  const CameraFailure(super.message);
}

class UserCancelledFailure extends Failure {
  const UserCancelledFailure() : super('Scan cancelled by user');
}

class ConversionFailure extends Failure {
  const ConversionFailure(super.message);
}

class FilePickerFailure extends Failure {
  const FilePickerFailure(super.message);
}

// Additional failures for PRD-001 features
class MetadataReadFailure extends Failure {
  const MetadataReadFailure(super.message);
}

class PdfEncryptionFailure extends Failure {
  const PdfEncryptionFailure(super.message);
}

class PdfAlreadyEncryptedFailure extends Failure {
  const PdfAlreadyEncryptedFailure() : super('The PDF is already encrypted');
}

class PdfNotEncryptedFailure extends Failure {
  const PdfNotEncryptedFailure() : super('The PDF is not encrypted');
}

class InvalidPasswordFailure extends Failure {
  const InvalidPasswordFailure() : super('Invalid password provided');
}

class SharingFailure extends Failure {
  const SharingFailure(super.message);
}
