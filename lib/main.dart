import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Note: Providers are referenced where needed; explicit imports removed to avoid unused warnings

// Navigation
import 'core/navigation/app_router.dart';
import 'core/startup/startup_metrics.dart';
// Theme
import 'core/theme/app_theme.dart';

void main() {
  StartupMetrics.instance.markAppStart();
  runApp(const ProviderScope(child: MyApp()));
}

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final router = ref.watch(routerProvider);

    return MaterialApp.router(
      title: 'Document Reader',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system, // Follow system theme
      routerConfig: router,
    );
  }
}
