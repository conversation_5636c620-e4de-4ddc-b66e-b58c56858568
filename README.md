# Document Reader MVP

Fast, clean Flutter app for viewing documents (PDF, DOCX, XLSX, PPTX) on Android. Feature-first Clean Architecture with Riverpod, Freezed, and GoRouter.

For full context, development workflows, architecture, troubleshooting, and docs index, see CLAUDE.md.

## Quick Start

- Clone: `git clone https://github.com/ducphamhoang/fs-fast-pdf-reader`
- Install: `flutter pub get`
- Generate: `dart run build_runner build --delete-conflicting-outputs`
- Run: `flutter run`

## Highlights

- PDF, DOCX, XLSX, PPTX viewers
- Auto scan or manual file picker
- Material 3 UI, GoRouter navigation

## Architecture

- Feature-first Clean Architecture (data, domain, presentation)
- Riverpod v3 (codegen), Freezed models, Dartz Either

See `CLAUDE.md: Architecture` for the full breakdown and project commands.

## Documentation

- Full guide and docs index: `CLAUDE.md`
- Plans and reports: `docs/impl_plan/`

## Troubleshooting

- Freezed classes must be `abstract class`.
- If codegen issues occur, re-run build_runner. More in `CLAUDE.md`.
