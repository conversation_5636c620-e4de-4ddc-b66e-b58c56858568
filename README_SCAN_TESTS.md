# Scan Tests Documentation

This document explains the scan-related tests and how to run them.

## Test Categories

### 1. Scan Notifier Finalize Flow Tests
Tests the `finalizeScan()` method in `ScanNotifier` to ensure:
- Pages are preserved on error (no data loss)
- Method returns typed `Either<Failure, ConversionResult?>` instead of throwing
- No loading state transitions that could cause data loss
- Rotation angles and filters are properly reset after processing

### 2. Scan Review Screen Tests
Tests the UI interaction in `ScanReviewScreen` to ensure:
- Retry flow uses typed result from finalizeScan
- Error messaging properly uses the typed result instead of reading `scanProvider.error`
- UI gracefully handles various error scenarios

### 3. PDF Converter Data Source Tests
Tests the `PDFConverterDataSourceImpl` with real temporary files to ensure:
- Image processing and conversion work with actual file I/O
- Different filters (color, grayscale, blackAndWhite) work correctly
- Black and white filter uses O<PERSON>'s method for better thresholding
- Rotation and cropping work properly
- Proper error handling for unimplemented features

### 4. Scan Flow Integration Tests
End-to-end tests that verify:
- Complete scan workflow maintains data integrity
- Error handling preserves user data throughout the flow
- State management works correctly across different operations

## Running Tests

To run all scan tests, use the provided script:

```bash
./test/run_scan_tests.sh
```

Or run specific test files:

```bash
# Run notifier tests
flutter test test/features/file_conversion/presentation/notifiers/scan_notifier_finalize_flow_test.dart

# Run screen tests
flutter test test/features/file_conversion/presentation/screens/scan_review_screen_test.dart

# Run data source tests
flutter test test/features/file_conversion/data/datasources/pdf_converter_data_source_impl_test.dart

# Run integration tests
flutter test test/features/file_conversion/integration/scan_flow_integration_test.dart
```

## Key Verification Points

The tests verify that the fixes from the implementation plan are working correctly:

1. **Data Preservation**: Finalize flow preserves pages on error instead of losing them
2. **Typed Results**: Methods return `Either<Failure, T>` instead of throwing exceptions
3. **State Management**: No problematic loading state transitions
4. **Filter Quality**: Black and white filter uses Otsu's thresholding method
5. **Rotation Reset**: Rotation angles and filters reset after processing to avoid double-application
6. **Proper Error Handling**: All error paths are handled without data loss