# Environment Setup Guide

Related docs:
- README.md — Project overview and quick start
- CLAUDE.md — Master index and architecture overview
- RELEASE_GUIDE.md — Release process and QA

This document provides instructions for setting up the development environment for the fs-fast-pdf-reader Flutter project on Ubuntu.

## Prerequisites

Before starting development, ensure your system has the following prerequisites:

- Git, unzip, curl/wget
- Java JDK 17 or 21 (recommended for recent Android build tools; JDK 17 preferred)
- Android SDK (commandline-tools only; Android Studio is NOT required)
- Flutter SDK (includes Dart)
- Gradle (typically handled automatically by Android projects via Gradle wrapper)

## Automated Setup (Recommended)

We provide scripts to help with setup:

### 1. Check Current Setup

First, check if your system already has the required components:

```bash
chmod +x check_prerequisites.sh
./check_prerequisites.sh
```

This script will check for all required components and report what's missing.

### 2. Install Missing Components

If any components are missing, run the installation script:

```bash
chmod +x install_prerequisites.sh
./install_prerequisites.sh
```

This script will install all missing components and configure environment variables.

### 3. Verify Installation

After installation, either restart your terminal or run:

```bash
source ~/.bashrc
```

Then verify the installation:

```bash
flutter doctor
```

## Manual Setup

If you prefer to set up manually, follow the steps below:

### 1. System Dependencies

Install required system dependencies:

```bash
sudo apt-get update
sudo apt-get install -y git unzip curl zip tar libglu1-mesa default-jdk
```

Verify Java is installed (JDK 17 or 21 recommended for Android development):

```bash
java -version
```

If Java is not installed or not version 17 or 21, install JDK 17:

```bash
sudo apt-get install -y openjdk-17-jdk
```

### 2. Android SDK

Set up Android SDK with command-line tools only:

```bash
# Create SDK dir
mkdir -p "$HOME/Android/Sdk/cmdline-tools/latest"
cd "$HOME/Android/Sdk/cmdline-tools"

# Download cmdline-tools (update URL to latest if needed)
curl -L -o tools.zip \
  https://dl.google.com/android/repository/commandlinetools-linux-11076708_latest.zip
unzip tools.zip -d latest
rm tools.zip

# Environment variables (add to shell profile)
export ANDROID_SDK_ROOT="$HOME/Android/Sdk"
export ANDROID_HOME="$ANDROID_SDK_ROOT"
export PATH="$ANDROID_SDK_ROOT/platform-tools:$ANDROID_SDK_ROOT/emulator:$ANDROID_SDK_ROOT/cmdline-tools/latest/bin:$PATH"
```

Add the exports to your shell profile (e.g., `~/.bashrc`) to make them persistent:

```bash
echo "" >> ~/.bashrc
echo "# Android SDK" >> ~/.bashrc
echo "export ANDROID_SDK_ROOT=$HOME/Android/Sdk" >> ~/.bashrc
echo "export ANDROID_HOME=\$ANDROID_SDK_ROOT" >> ~/.bashrc
echo 'export PATH="$ANDROID_SDK_ROOT/platform-tools:$ANDROID_SDK_ROOT/emulator:$ANDROID_SDK_ROOT/cmdline-tools/latest/bin:$PATH"' >> ~/.bashrc
```

Install required Android packages:

```bash
yes | sdkmanager --licenses
sdkmanager \
  "platform-tools" \
  "platforms;android-34" \
  "build-tools;34.0.0"
```

### 3. Gradle (Build System)

Gradle is the build system used by Android applications. In Flutter projects, Gradle is typically managed automatically:
- Each Android project includes a Gradle wrapper that downloads the appropriate Gradle version
- Flutter handles Gradle setup during project initialization
- You don't need to install Gradle separately in most cases

### 4. Flutter SDK (includes Dart)

Download and install Flutter:

```bash
# Choose a directory for Flutter
cd "$HOME"
curl -L -o flutter.tar.xz https://storage.googleapis.com/flutter_infra_release/releases/stable/linux/flutter_linux_3.24.0-stable.tar.xz
tar xf flutter.tar.xz
rm flutter.tar.xz

# Add Flutter to PATH (add to shell profile)
export PATH="$HOME/flutter/bin:$PATH"
```

Add Flutter to your shell profile:

```bash
echo "" >> ~/.bashrc
echo "# Flutter" >> ~/.bashrc
echo 'export PATH="$HOME/flutter/bin:$PATH"' >> ~/.bashrc
```

Run doctor and accept Android licenses:

```bash
flutter doctor
flutter doctor --android-licenses
```

### 5. FVM (Optional)

Optionally, manage Flutter versions with FVM:

```bash
dart pub global activate fvm
export PATH="$HOME/.pub-cache/bin:$PATH"

# Add FVM to shell profile
echo "" >> ~/.bashrc
echo "# FVM" >> ~/.bashrc
echo 'export PATH="$HOME/.pub-cache/bin:$PATH"' >> ~/.bashrc

fvm install stable
fvm use stable
```

## Project Setup

Once the prerequisites are installed, set up the project:

```bash
git clone <repo-url>
cd fs-fast-pdf-reader
flutter --version
flutter pub get

# Generate code (Riverpod, Freezed, JsonSerializable)
dart run build_runner build --delete-conflicting-outputs

# Quality & Tests
flutter analyze
dart format .
flutter test

# Run on a connected Android device (optional)
flutter run
```

## Troubleshooting

If you encounter issues:

1. Make sure all environment variables are properly set:
   ```bash
   echo $ANDROID_HOME
   echo $ANDROID_SDK_ROOT
   echo $PATH
   ```

2. Re-run code generation if build errors appear:
   ```bash
   dart run build_runner build --delete-conflicting-outputs
   ```

3. Verify Android SDK environment variables are correct and ensure licenses are accepted

4. Ensure Java 17 is installed and being used

Common Flutter doctor issues can often be resolved by running `flutter doctor` and addressing the issues it reports.